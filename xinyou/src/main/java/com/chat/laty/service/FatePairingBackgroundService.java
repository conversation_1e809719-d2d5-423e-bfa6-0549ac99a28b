package com.chat.laty.service;

import android.app.Service;
import android.content.Intent;
import android.os.IBinder;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.hjq.permissions.OnPermissionCallback;
import com.hjq.permissions.Permission;
import com.hjq.permissions.XXPermissions;
import com.chat.laty.utils.PermissionInterceptor;
import com.yhao.floatwindow.FloatWindow;
import com.chat.laty.activity.CallPhoneActivity;
import com.chat.laty.contants.Constant;
import com.chat.laty.entity.FatePairingInfo;
import com.chat.laty.entity.event.FatePairEvent;
import com.chat.laty.entity.event.FatePairingEvent;
import com.chat.laty.entity.event.FateSessionEvent;
import com.chat.laty.utils.LogUtil;
import com.chat.laty.utils.XYSPUtils;
import com.chat.laty.utils.net.Common;
import com.chat.laty.utils.net.GsonUtils;
import com.chat.laty.utils.net.HttpUtils;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.List;

import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.WebSocket;
import okhttp3.WebSocketListener;
import okio.ByteString;

/**
 * <AUTHOR>
 * @date 2024/1/6 19:58
 * @description:
 */
public class FatePairingBackgroundService extends Service {
    private static final String TAG = "BackgroundService";
    //开始连接
    WebSocket mWebsocket;

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onReceivedCall(FateSessionEvent sessionEvent) {
        stopSelf();
    }


    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        LogUtil.e(TAG, "Service onCreate");
        EventBus.getDefault().register(this);
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        LogUtil.e(TAG, "Service onStartCommand");
        // 在这里执行你的后台任务
        startPair();

        return START_NOT_STICKY;
    }

    public void startPair() {
        String userId = XYSPUtils.getString(Common.KEY_APP_USER_RY_ID);
        LogUtil.e("接口链接", "WebSocket==========userId-->"+userId);
        String url = "wss://mmly.yijiarj.com/xiny/websocket/" + userId;
        String head = XYSPUtils.getString(Common.KEY_APP_TOKEN);
        OkHttpClient client = HttpUtils.getClient();
        Request.Builder build = new Request.Builder();
        if (!TextUtils.isEmpty(head))
            build.addHeader("Sec-WebSocket-Protocol", head);
        build.url(url);

        mWebsocket = client.newWebSocket(build.build(), new WebSocketListener() {
            @Override
            public void onClosed(@NonNull WebSocket webSocket, int code, @NonNull String reason) {
                super.onClosed(webSocket, code, reason);
                LogUtil.e("接口链接", "WebSocket==========onClosed");
            }

            @Override
            public void onClosing(@NonNull WebSocket webSocket, int code, @NonNull String reason) {
                super.onClosing(webSocket, code, reason);
                LogUtil.e("接口链接", "WebSocket==========onClosing");
            }

            @Override
            public void onFailure(@NonNull WebSocket webSocket, @NonNull Throwable t, @Nullable Response response) {
                super.onFailure(webSocket, t, response);
                LogUtil.e("接口链接", "WebSocket==========onFailure");
            }

            @Override
            public void onMessage(@NonNull WebSocket webSocket, @NonNull String text) {
                super.onMessage(webSocket, text);
                LogUtil.e("接口链接", "WebSocket==========onMessage-->" + text);
                FatePairingInfo base = GsonUtils.JsonToBean(text, FatePairingInfo.class);
                if (null != base && TextUtils.equals("1", base.getType()) && !TextUtils.isEmpty(base.getUserId())) {
                    EventBus.getDefault().post(new FatePairEvent(false));
                    if (null != FloatWindow.get("fatePairing"))
                        FloatWindow.get("fatePairing").hide();
                    XXPermissions.with(FatePairingBackgroundService.this).permission(Permission.RECORD_AUDIO, Permission.CAMERA).interceptor(new PermissionInterceptor()).request(new OnPermissionCallback() {
                        @Override
                        public void onGranted(List<String> permissions, boolean all) {
                            if (!all) {
                                return;
                            }
                            XYSPUtils.put(Constant.KEY_USER_FATE_PAIRING, false);
                            EventBus.getDefault().postSticky(new FatePairingEvent(base));
                            CallPhoneActivity.start(FatePairingBackgroundService.this, base.getUserId(), 0, 1,"{\"type\":\"1\",\"remark\":\"缘分配对\"}");
                            stopSelf();
                        }
                    });
                }
            }

            @Override
            public void onMessage(@NonNull WebSocket webSocket, @NonNull ByteString bytes) {
                super.onMessage(webSocket, bytes);
                LogUtil.e("接口链接", "WebSocket==========onMessage--->bytes");
            }

            @Override
            public void onOpen(@NonNull WebSocket webSocket, @NonNull Response response) {
                super.onOpen(webSocket, response);
                LogUtil.e("接口链接", "WebSocket==========onOpen");
                sedMsgWeb();
            }
        });
    }

    public void sedMsgWeb(){
        if (mWebsocket != null)
            mWebsocket.send("{\"type\":\"1\",\"remark\":\"缘分配对\"}");
    }


    @Override
    public void onDestroy() {
        super.onDestroy();

        if (mWebsocket != null) {
            mWebsocket.cancel();
            mWebsocket = null;
        }
    }
}
