package com.chat.laty.service;


import android.app.Service;
import android.content.Intent;
import android.os.IBinder;

import com.chat.laty.activity.CallPhoneActivity;
import com.chat.laty.activity.ReceivedActivity;
import com.chat.laty.entity.event.RCHangUpSessionEvent;
import com.chat.laty.entity.event.RCReceiveCallPlusSessionEvent;
import com.chat.laty.entity.event.RYCallConnectedEvent;
import com.chat.laty.utils.BgmPlayer;
import com.chat.laty.utils.LogUtil;
import com.chat.laty.utils.SystemRoundUtils;
import com.chat.laty.utils.XYVideoUtils;

import org.greenrobot.eventbus.EventBus;

import cn.rongcloud.callplus.api.RCCallPlusAudioRouteClient;
import cn.rongcloud.callplus.api.RCCallPlusAudioRouteType;
import cn.rongcloud.callplus.api.RCCallPlusClient;
import cn.rongcloud.callplus.api.RCCallPlusReason;
import cn.rongcloud.callplus.api.RCCallPlusSession;
import cn.rongcloud.callplus.api.callback.IRCCallPlusAudioRouteListener;
import cn.rongcloud.callplus.api.callback.IRCCallPlusEventListener;

/**
 * <AUTHOR>
 * @date 2024/1/6 19:58
 * @description:
 */
public class BackgroundService extends Service {
    private static final String TAG = "BackgroundService";

    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        LogUtil.e(TAG, "Service onCreate");
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        LogUtil.e(TAG, "Service onStartCommand");
        // 在这里执行你的后台任务
//        performBackgroundTask();
        startTask();

        return START_NOT_STICKY;
    }

    private void startTask() {

//        if (RCCallPlusAudioRouteClient.getInstance().hasHeadSet()) {
//            BgmPlayer.getInstance(BackgroundService.this).changeToHeadset();
//        } else if (RCCallPlusAudioRouteClient.getInstance().hasBluetoothA2dpConnected()) {
//            BgmPlayer.getInstance(BackgroundService.this).changeToBluetoothSco();
//        } else {
//            BgmPlayer.getInstance(BackgroundService.this).changeToSpeaker();
//        }

        RCCallPlusAudioRouteClient.getInstance().setOnAudioRouteChangedListener(new IRCCallPlusAudioRouteListener() {

            /**
             * 音频路由设备改变通知
             *
             * @param type 设备类型
             */
            @Override
            public void onRouteChanged(RCCallPlusAudioRouteType type) {
//                if (type == RCCallPlusAudioRouteType.HEADSET_BLUETOOTH) {
                    BgmPlayer.getInstance(BackgroundService.this).changeToBluetoothSco();
//                } else if (type == RCCallPlusAudioRouteType.HEADSET) {
//                    BgmPlayer.getInstance(BackgroundService.this).changeToHeadset();
//                } else {
//                    BgmPlayer.getInstance(BackgroundService.this).changeToSpeaker();
//                }
            }

            /**
             * 音频路由切换失败通知
             *
             * @param fromType 原状态
             * @param toType 目标
             */
            @Override
            public void onRouteSwitchFailed(RCCallPlusAudioRouteType fromType, RCCallPlusAudioRouteType toType) {

            }
        });
    }


    int enterType = 0;

    private void performBackgroundTask() {
//        RCCallPlusClient.getInstance().setCallPlusEventListener(phoneCallListener);
//        RCCallPlusClient.getInstance().setCallPlusResultListener(new IRCCallPlusResultListener() {
//
//            /**
//             * 发起通话方法结果回调
//             *
//             * @param code 方法请求结果
//             * @param callId 通话Id
//             * @param busyUserList 呼叫成功后，返回被邀请人列表中的忙线用户列表
//             */
//            @Override
//            public void onStartCall(RCCallPlusCode code, String callId, List<RCCallPlusUser> busyUserList) {
//                IRCCallPlusResultListener.super.onStartCall(code, callId, busyUserList);
//                enterType = 0;
//            }
//
//            /**
//             * 接听通话结果回调
//             *
//             * @param code 方法请求结果
//             * @param callId 通话Id
//             */
//            @Override
//            public void onAccept(RCCallPlusCode code, String callId) {
//                IRCCallPlusResultListener.super.onAccept(code, callId);
//                LogUtil.i(TAG, "onAccept--->code:" + code + "----" + callId);
//                enterType = 1;
//            }
//        });
//        RongCoreClient.addOnReceiveMessageListener(receiveMsg);
    }


    IRCCallPlusEventListener phoneCallListener = new IRCCallPlusEventListener() {


        @Override
        public void onReceivedCall(RCCallPlusSession callSession, String extra) {
//            LogUtil.i(TAG, "onReceivedCall，，，，" + new Gson().toJson(callSession));
            LogUtil.i(TAG, "onReceivedCall，，，，");
            // 来电
            LogUtil.e("来电提示", "呼叫ID--->BackgroundService--->" + callSession.getCallId());
            LogUtil.e("来电提示", "呼叫ID--->extra-->" + extra);
//            if (AppHelper.isAppBackground(BackgroundService.this)){
//                RYCallPlusManager.getInstance(BackgroundService.this).showCallReminderView();
//            }else {
//                SystemRoundUtils.playRing(BackgroundService.this);
//                SystemRoundUtils.setVibrator();
//                ReceivedActivity.start(BackgroundService.this);
//                EventBus.getDefault().postSticky(new RCReceiveCallPlusSessionEvent(callSession, extra));
//            }
            SystemRoundUtils.playRing(BackgroundService.this);
            SystemRoundUtils.setVibrator();
            ReceivedActivity.start(BackgroundService.this);
            EventBus.getDefault().postSticky(new RCReceiveCallPlusSessionEvent(callSession, extra));
        }

        @Override
        public void onCallConnected(RCCallPlusSession callSession) {
            LogUtil.i(TAG, "onCallConnected，，，，" + callSession.getCallId());
            IRCCallPlusEventListener.super.onCallConnected(callSession);
            if (callSession != null) {
                XYVideoUtils.callStartTime = System.currentTimeMillis();
                XYVideoUtils.session = callSession;

                if (enterType == 1) {
                    CallPhoneActivity.start(BackgroundService.this, callSession.getCallId(), enterType, callSession.getMediaType().getValue());
                }
            }

            EventBus.getDefault().post(new RYCallConnectedEvent(callSession));
        }

        @Override
        public void onCallEnded(RCCallPlusSession session, RCCallPlusReason reason) {
            LogUtil.i(TAG, "onCallEnded，，，，" + session);
            LogUtil.e("挂断提示", "挂断ID--->" + session.getCallId());
            SystemRoundUtils.cancelVibrator();
            SystemRoundUtils.stopRing();

            if (XYVideoUtils.session != null && (XYVideoUtils.session.getCallId() == session.getCallId())) {
                RCCallPlusClient.getInstance().enableMicrophone(false);
                RCCallPlusClient.getInstance().stopCamera();
                XYVideoUtils.callStartTime = -1;
                XYVideoUtils.session = null;
            }
            EventBus.getDefault().post(new RCHangUpSessionEvent(session));
        }
    };


    @Override
    public void onDestroy() {
        super.onDestroy();
    }
}
