package com.chat.laty.service;

import android.annotation.SuppressLint;
import android.bluetooth.BluetoothClass;
import android.bluetooth.BluetoothDevice;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.chat.laty.base.XYApplication;
import com.chat.laty.utils.BgmPlayer;

/**
 * <AUTHOR>
 * @date 2024/4/13 17:08
 * @description:
 */
public class HeadsetReceiver extends BroadcastReceiver {
    @SuppressLint("MissingPermission")
    @Override
    public void onReceive(Context context, Intent intent) {
        String action = intent.getAction();
        if (action != null && action.equals(BluetoothDevice.ACTION_ACL_CONNECTED)) {
            BluetoothDevice device = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE);
            if (device != null && device.getBluetoothClass().getMajorDeviceClass() == BluetoothClass.Device.Major.AUDIO_VIDEO) {
                // 蓝牙耳机连接
//                Toaster.show("蓝牙耳机连接");
                BgmPlayer.getInstance(XYApplication.getAppApplicationContext()).changeToBluetoothSco();
            }
        } else {
            if (Intent.ACTION_HEADSET_PLUG.equals(intent.getAction())) {
                boolean isConnected = intent.getIntExtra("state", 0) == 1;
                if (isConnected) {
                    // 耳机已连接
//                    Toaster.show("耳机已插入");
                    boolean hasMic = intent.getIntExtra("microphone", 0) == 1;
                    // 根据需要处理
                    BgmPlayer.getInstance(XYApplication.getAppApplicationContext()).changeToHeadset();

                } else {
                    // 耳机已断开
                    // 处理
//                    Toaster.show("耳机被拔出");
                    BgmPlayer.getInstance(XYApplication.getAppApplicationContext()).changeToSpeaker();
                }
            }
        }

    }
}
