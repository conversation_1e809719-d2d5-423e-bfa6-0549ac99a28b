package com.chat.laty.service;

import android.content.Context;

import com.hjq.toast.Toaster;

import io.rong.push.PushType;
import io.rong.push.notification.PushMessageReceiver;
import io.rong.push.notification.PushNotificationMessage;

/**
 * <AUTHOR>
 * @date 2024/4/2 14:56
 * @description:
 */
public class XYCustomMessageReceiver extends PushMessageReceiver {

    @Override
    public boolean onNotificationMessageClicked(
            Context context, PushType pushType, PushNotificationMessage message) {
        if (pushType.equals(PushType.GOOGLE_FCM)){

            // TODO
        }
        Toaster.show("点击了消息");
        // 返回 true 表示拦截，false 为不拦截
        return true;
    }
}
