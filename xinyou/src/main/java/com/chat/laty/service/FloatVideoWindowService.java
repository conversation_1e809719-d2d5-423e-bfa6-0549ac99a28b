package com.chat.laty.service;

import android.annotation.SuppressLint;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.os.Binder;
import android.os.Build;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.WindowManager;
import android.widget.FrameLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.chat.laty.R;
import com.chat.laty.activity.CallPhoneActivity;
import com.chat.laty.entity.event.RCHangUpSessionEvent;
import com.chat.laty.entity.event.RCReceiveCallPlusSessionEvent;
import com.chat.laty.entity.event.RYCallConnectedEvent;
import com.chat.laty.utils.LogUtil;
import com.chat.laty.utils.XYVideoUtils;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;

import cn.rongcloud.callplus.api.RCCallPlusClient;
import cn.rongcloud.callplus.api.RCCallPlusLocalVideoView;
import cn.rongcloud.callplus.api.RCCallPlusRemoteVideoView;
import cn.rongcloud.callplus.api.RCCallPlusSession;
import cn.rongcloud.callplus.api.RCCallPlusUser;

/**
 * <AUTHOR>
 * @date 2024/1/14 20:10
 * @description:悬浮窗
 */


public class FloatVideoWindowService extends Service {
    private static final String TAG = "FloatVideoWindowService";
    private WindowManager mWindowManager;
    private WindowManager.LayoutParams wmParams;
    private LayoutInflater inflater;
    private String currentBigUserId;
    private int callType;
    private int entryType;
    private long remainTime;
    //浮动布局view
    private View mFloatingLayout;
    //容器父布局
    private FrameLayout mBigVideoView, mSmallVideoView;


    TextView mRemoteHintTv;


    TextView callTimeTv;

    // 视频布局和电话布局
    private View mVideoLl, mCallLl;


    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onCallConnected(RYCallConnectedEvent sessionEvent) {
        RCCallPlusSession callSession = sessionEvent.session;
        if (callSession == null) {
            return;
        }

        new Handler(Looper.getMainLooper()).post(() -> {
            updateWindow();//悬浮框点击事件的处理
        });
        // 视频接通
//                Toaster.show("onCallConnected");
//                mCallSession = callSession;
////                callTime(System.currentTimeMillis());
//                updCallTime();
//                IRCCallPlusEventListener.super.onCallConnected(callSession);
//                videoType = 1;
//                setRemoteUserVideoView(callSession.getRemoteUserList());
//                setLocalVideoView();
//                mVideoTipsTv.setFocusable(true);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onReceivedCall(RCReceiveCallPlusSessionEvent sessionEvent) {
        RCCallPlusSession callSession = sessionEvent.getSession();
        LogUtil.i(TAG, "onReceivedCall ，，，，");
        RCCallPlusSession currentCallSession = RCCallPlusClient.getInstance().getCurrentCallSession();
        if (currentCallSession != null && !TextUtils.equals(callSession.getCallId(), currentCallSession.getCallId())) {
            //可以使用该方法判断出，有正在进行中的通话，又有第二通通话呼入的情况
            //todo 第二通通话可以直接调用 RCCallPlusClient.getInstance().accept 方法接听，SDK内部会将第一通通话挂断
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onCallEnded(RCHangUpSessionEvent sessionEvent) {
        LogUtil.i(TAG, "onCallEnded ，，，，");
        RCCallPlusSession session = sessionEvent.getSession();
        if (XYVideoUtils.session != null && XYVideoUtils.session.getCallId() == session.getCallId()) {
//            XYVideoUtils.callStartTime = -1;
//            XYVideoUtils.session = null;
            cancelStartTimeTask();
            XYVideoUtils.stop(getApplicationContext());
        } else if (XYVideoUtils.session == null) {
            XYVideoUtils.stop(getApplicationContext());
        }
    }


    @Override
    public void onCreate() {
        super.onCreate();
        EventBus.getDefault().register(this);
        initWindow();//设置悬浮窗基本参数（位置、宽高等）

    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        currentBigUserId = intent.getStringExtra("userId");
        callType = intent.getIntExtra("callType", 0);
        entryType = intent.getIntExtra("entryType", 0);
        remainTime = intent.getLongExtra("remainTime",0);
        initFloating();//悬浮框点击事件的处理
        initRYCall();
        return new MyBinder();
    }


//    @Override
//    public boolean bindService(Intent intent, ServiceConnection conn, int flags) {
//        currentBigUserId = intent.getStringExtra("userId");
//        callType = intent.getIntExtra("callType", 0);
//        initFloating();//悬浮框点击事件的处理
//        return super.bindService(intent, conn, flags);
//    }


    public class MyBinder extends Binder {
        public FloatVideoWindowService getService() {
            return FloatVideoWindowService.this;
        }
    }


    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        currentBigUserId = intent.getStringExtra("userId");
        callType = intent.getIntExtra("callType", 0);
        entryType = intent.getIntExtra("entryType", 0);
        remainTime = intent.getLongExtra("remainTime", 0);
        initFloating();//悬浮框点击事件的处理
        initRYCall();
        return super.onStartCommand(intent, flags, startId);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (mFloatingLayout != null) {
            // 移除悬浮窗口
            mWindowManager.removeView(mFloatingLayout);
            mFloatingLayout = null;
//            Constents.isShowFloatWindow = false;
        }
        EventBus.getDefault().unregister(this);
    }

    /**
     * 设置悬浮框基本参数（位置、宽高等）
     */
    private void initWindow() {
        mWindowManager = (WindowManager) getApplicationContext().getSystemService(Context.WINDOW_SERVICE);
        //设置好悬浮窗的参数

        wmParams = new WindowManager.LayoutParams();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            wmParams.type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY;
        } else {
            wmParams.type = WindowManager.LayoutParams.TYPE_PHONE;
        }
        //设置可以显示在状态栏上
        wmParams.flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE | WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL |
                WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN | WindowManager.LayoutParams.FLAG_LAYOUT_INSET_DECOR |
                WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH;

        //设置悬浮窗口长宽数据
        wmParams.width = WindowManager.LayoutParams.WRAP_CONTENT;
        wmParams.height = WindowManager.LayoutParams.WRAP_CONTENT;
        // 悬浮窗默认显示以左上角为起始坐标
        wmParams.gravity = Gravity.LEFT | Gravity.TOP;
        //悬浮窗的开始位置，因为设置的是从左上角开始，所以屏幕左上角是x=0;y=0
        wmParams.x = 70;
        wmParams.y = 210;
        //得到容器，通过这个inflater来获得悬浮窗控件
        inflater = LayoutInflater.from(getApplicationContext());
        // 获取浮动窗口视图所在布局
        mFloatingLayout = inflater.inflate(R.layout.alert_float_video_layout, null);
        // 添加悬浮窗的视图
        mWindowManager.addView(mFloatingLayout, wmParams);

        mVideoLl = mFloatingLayout.findViewById(R.id.video_ll);
        mCallLl = mFloatingLayout.findViewById(R.id.call_ll);
        callTimeTv = mFloatingLayout.findViewById(R.id.call_time_tv);
    }

    private void updateWindow() {
        LogUtil.e("initFloating", "设置本地视频--->");
        if (1 == callType) {
            mVideoLl.setVisibility(View.VISIBLE);
            mCallLl.setVisibility(View.GONE);


            // 本地照片
            RCCallPlusClient.getInstance().startCamera();
            RCCallPlusClient.getInstance().enableMicrophone(true);
            //创建本地视图对象
            RCCallPlusLocalVideoView localVideoView = new RCCallPlusLocalVideoView(this.getApplicationContext());
            //FIT: 视频帧通过保持宽高比(可能显示黑色边框)来缩放以适应视图的大小
//        localVideoView.setRenderMode(RCCallPlusRenderMode.FIT);
            //设置本地视图给SDK
            RCCallPlusClient.getInstance().setVideoView(localVideoView);

            FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(FrameLayout.LayoutParams.MATCH_PARENT, FrameLayout.LayoutParams.MATCH_PARENT);
            params.gravity = Gravity.CENTER_HORIZONTAL;//在父布局中横向居中显示
            //将本地视图添加到XML中显示
            //示例代码中 mLocalVideoViewFrameLayout 为 android.widget.FrameLayout 对象


            if (XYVideoUtils.session != null) {
                setRemoteUserVideoView(XYVideoUtils.session.getRemoteUserList());
//                mRemoteHintTv.setVisibility(View.GONE);
                mSmallVideoView.removeAllViews();
                mSmallVideoView.addView(localVideoView);
            } else {
//                mRemoteHintTv.setVisibility(View.VISIBLE);
//                mRemoteVideoView.removeAllViews();
//                mRemoteVideoView.addView(localVideoView);
                mSmallVideoView.removeAllViews();
                mBigVideoView.removeAllViews();
                mBigVideoView.addView(localVideoView);
            }

        } else {
            mRemoteHintTv.setVisibility(View.GONE);
            mVideoLl.setVisibility(View.GONE);
            mCallLl.setVisibility(View.VISIBLE);
            updCallTime();
        }
    }

    private void initFloating() {
        mBigVideoView = mFloatingLayout.findViewById(R.id.big_video);
        mRemoteHintTv = mFloatingLayout.findViewById(R.id.remote_hint_tv);
        mSmallVideoView = mFloatingLayout.findViewById(R.id.small_video);
        updateWindow();
//        TRTCVideoViewLayout mTRTCVideoViewLayout = Constents.mBigVideoViewLayout;
//        TXCloudVideoView mLocalVideoView = mTRTCVideoViewLayout.getCloudVideoViewByUseId(currentBigUserId);
//        if (mLocalVideoView == null) {
//            mLocalVideoView = mTRTCVideoViewLayout.getCloudVideoViewByIndex(0);
//        }
//        if (ConstData.userid.equals(currentBigUserId)) {
//            TXCGLSurfaceView mTXCGLSurfaceView = mLocalVideoView.getGLSurfaceView();
//            if (mTXCGLSurfaceView != null && mTXCGLSurfaceView.getParent() != null) {
//                ((ViewGroup) mTXCGLSurfaceView.getParent()).removeView(mTXCGLSurfaceView);
//                mTXCloudVideoView.addVideoView(mTXCGLSurfaceView);
//            }
//        } else {
//            TextureView mTextureView = mLocalVideoView.getVideoView();
//            if (mTextureView != null && mTextureView.getParent() != null) {
//                ((ViewGroup) mTextureView.getParent()).removeView(mTextureView);
//                mTXCloudVideoView.addVideoView(mTextureView);
//            }
//        }
//        Constents.isShowFloatWindow = true;
//        //悬浮框触摸事件，设置悬浮框可拖动
//        mBigVideoView.setOnTouchListener(new FloatingListener());
        mFloatingLayout.setOnTouchListener(new FloatingListener());
//        //悬浮框点击事件
        mFloatingLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //在这里实现点击重新回到Activity
                Intent intent = new Intent(FloatVideoWindowService.this, CallPhoneActivity.class);
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                intent.putExtra(CallPhoneActivity.KEY_CALL_TYPE, callType);
                intent.putExtra(CallPhoneActivity.KEY_ENTER_EXTRA, entryType);
                intent.putExtra(CallPhoneActivity.KEY_USER_ID, currentBigUserId);
                intent.putExtra(CallPhoneActivity.KEY_REMAIN_TIME, remainTime);
                intent.putExtra("is_video_ing", 1);
                startActivity(intent);
                XYVideoUtils.stop(getApplicationContext());
            }
        });
    }

    private void initRYCall() {

//        RCCallPlusClient.getInstance().setCallPlusEventListener(new IRCCallPlusEventListener() {
//
//
//            @Override
//            public void onCallStartTimeFromServer(long callStartTime) {
//                LogUtil.e(TAG, "onCallStartTimeFromServer callStartTime--->" + callStartTime);
////                callTime(callStartTime);
//                XYVideoUtils.callStartTime = callStartTime;
//                updCallTime();
//            }
//
//            @Override
//            public void onCallConnected(RCCallPlusSession callSession) {
//                LogUtil.i(TAG, "onCallConnected ，，，，");
//                XYVideoUtils.callStartTime = System.currentTimeMillis();
//                XYVideoUtils.session = callSession;
//
//                new Handler(Looper.getMainLooper()).post(() -> {
//                    updateWindow();//悬浮框点击事件的处理
//                });
//                // 视频接通
////                Toaster.show("onCallConnected");
////                mCallSession = callSession;
//////                callTime(System.currentTimeMillis());
////                updCallTime();
////                IRCCallPlusEventListener.super.onCallConnected(callSession);
////                videoType = 1;
////                setRemoteUserVideoView(callSession.getRemoteUserList());
////                setLocalVideoView();
////                mVideoTipsTv.setFocusable(true);
//            }
//
//            /**
//             * 本端用户通过该回调接收到通话呼叫
//             *
//             * @param callSession   通话实体信息
//             */
//            @Override
//            public void onReceivedCall(RCCallPlusSession callSession, String extra) {
//                LogUtil.i(TAG, "onReceivedCall ，，，，");
//                RCCallPlusSession currentCallSession = RCCallPlusClient.getInstance().getCurrentCallSession();
//                if (currentCallSession != null && !TextUtils.equals(callSession.getCallId(), currentCallSession.getCallId())) {
//                    //可以使用该方法判断出，有正在进行中的通话，又有第二通通话呼入的情况
//                    //todo 第二通通话可以直接调用 RCCallPlusClient.getInstance().accept 方法接听，SDK内部会将第一通通话挂断
//                }
////                runOnUiThread(new Runnable() {
////                    @Override
////                    public void run() {
////                        RCCallPlusClient.getInstance().enableMicrophone(true);
////                        if (1 == mCallType) {
////                            RCCallPlusClient.getInstance().startCamera();
////                            setLocalVideoView();//复用发起通话逻辑中的 设置本地视频渲染视图 方法
////                        }
////
////                        showReceiveDialog(callSession.getCallId());
////                    }
////                });
//            }
//
//            @Override
//            public void onCallEnded(RCCallPlusSession session, RCCallPlusReason reason) {
//                LogUtil.i(TAG, "onCallEnded ，，，，");
//                IRCCallPlusEventListener.super.onCallEnded(session, reason);
//                XYVideoUtils.stop(getApplicationContext());
//                cancelStartTimeTask();
//                XYVideoUtils.callStartTime = -1;
//                XYVideoUtils.session = null;
////                runOnUiThread(new Runnable() {
////                    @Override
////                    public void run() {
////                        Toaster.show("通话结束");
////                        RCCallPlusClient.getInstance().enableMicrophone(false);
////                        RCCallPlusClient.getInstance().stopCamera();
////                        EventBus.getDefault().post(new CallPlushListenerEvent(1));
////                        XYVideoUtils.stop(getThisActivity());
////                        cancelStartTimeTask();
//////                        XYVideoUtils.session = null;
////                        finish();
////                    }
////                });
//            }
//
//            /**
//             * 远端用户状态改变监听
//             *
//             * @param callId 通话Id
//             * @param userId 用户Id
//             * @param status 该用户当前状态
//             * @param reason 该用户当前状态原因
//             */
//            @Override
//            public void onRemoteUserStateChanged(String callId, String userId, RCCallPlusUserSessionStatus status, RCCallPlusReason reason) {
//                IRCCallPlusEventListener.super.onRemoteUserStateChanged(callId, userId, status, reason);
////                runOnUiThread(new Runnable() {
////                    @Override
////                    public void run() {
////                        StringBuilder stringBuilder = new StringBuilder("通话 ");
////                        stringBuilder.append(" 对方 ");
////                        switch (status) {
////                            case IDLE:
////                                stringBuilder.append("空闲");
////                                break;
////                            case CALLING:
////                                stringBuilder.append("呼叫中");
////                                break;
////                            case INVITED:
////                                stringBuilder.append("被邀请中");
////                                break;
////                            case RINGING:
////                                stringBuilder.append("响铃中");
////                                break;
////                            case BUSY_LINE_RINGING:
////                                stringBuilder.append("忙线(响铃中)");
////                                break;
////                            case BUSY_LINE_WAIT:
////                                stringBuilder.append("忙线(通话中)");
////                                break;
////                            case CONNECTING:
////                                stringBuilder.append("已接听，连接中");
////                                break;
////                            case ON_CALL:
////                                stringBuilder.append("通话中");
////                                break;
////                            case ENDED:
////                                stringBuilder.append("通话已结束");
////                                break;
////                            case NO_ANSWER:
////                                stringBuilder.append("未应答");
////                                break;
////                            case MISSED:
////                                stringBuilder.append("未接听");
////                                break;
////                            case CANCELED:
////                                stringBuilder.append("已取消");
////                                break;
////                            case DECLINED:
////                                stringBuilder.append("已拒绝");
////                                break;
////                            case ERROR:
////                                stringBuilder.append("错误");
////                                break;
////                        }
//////                        Toaster.show(stringBuilder.toString());
////                    }
////                });
//            }
//        });
    }


    /**
     * 设置远端用户视频渲染视图
     */
    private void setRemoteUserVideoView(List<RCCallPlusUser> remoteUserList) {
        List<String> userIds = new ArrayList<>();
        List<RCCallPlusRemoteVideoView> rcCallPlusRemoteVideoViewList = new ArrayList<>();
        for (RCCallPlusUser rcCallPlusUser : remoteUserList) {
            String remoteUserId = rcCallPlusUser.getUserId();
            userIds.add(remoteUserId);
            RCCallPlusClient.getInstance().removeVideoView(userIds);
            RCCallPlusRemoteVideoView rcCallPlusVideoView = new RCCallPlusRemoteVideoView(remoteUserId, getApplicationContext(), false);
            mBigVideoView.removeAllViews();
            mBigVideoView.addView(rcCallPlusVideoView);
            rcCallPlusRemoteVideoViewList.add(rcCallPlusVideoView);
        }
        RCCallPlusClient.getInstance().setVideoView(rcCallPlusRemoteVideoViewList);
    }

    //开始触控的坐标，移动时的坐标（相对于屏幕左上角的坐标）
    private int mTouchStartX, mTouchStartY, mTouchCurrentX, mTouchCurrentY;
    //开始时的坐标和结束时的坐标（相对于自身控件的坐标）
    private int mStartX, mStartY, mStopX, mStopY;
    //判断悬浮窗口是否移动，这里做个标记，防止移动后松手触发了点击事件
    private boolean isMove;

    private class FloatingListener implements View.OnTouchListener {

        @Override
        public boolean onTouch(View v, MotionEvent event) {
            int action = event.getAction();
            switch (action) {
                case MotionEvent.ACTION_DOWN:
                    isMove = false;
                    mTouchStartX = (int) event.getRawX();
                    mTouchStartY = (int) event.getRawY();
                    mStartX = (int) event.getX();
                    mStartY = (int) event.getY();
                    break;
                case MotionEvent.ACTION_MOVE:
                    mTouchCurrentX = (int) event.getRawX();
                    mTouchCurrentY = (int) event.getRawY();
                    wmParams.x += mTouchCurrentX - mTouchStartX;
                    wmParams.y += mTouchCurrentY - mTouchStartY;
                    mWindowManager.updateViewLayout(mFloatingLayout, wmParams);

                    mTouchStartX = mTouchCurrentX;
                    mTouchStartY = mTouchCurrentY;
                    break;
                case MotionEvent.ACTION_UP:
                    mStopX = (int) event.getX();
                    mStopY = (int) event.getY();
                    if (Math.abs(mStartX - mStopX) >= 1 || Math.abs(mStartY - mStopY) >= 1) {
                        isMove = true;
                    }
                    break;
                default:
                    break;
            }
            //如果是移动事件不触发OnClick事件，防止移动的时候一放手形成点击事件
            return isMove;
        }

    }


    private TimerTask mStartTimeTask;
    private Timer mStartTimeTimer;

    void cancelStartTimeTask() {
        if (mStartTimeTask != null) {
            mStartTimeTask.cancel();
            mStartTimeTask = null;
        }
        if (mStartTimeTimer != null) {
            mStartTimeTimer.cancel();
            mStartTimeTimer = null;
        }
    }

    private void updCallTime() {
        cancelStartTimeTask();
        mStartTimeTask =
                new TimerTask() {
                    @Override
                    public void run() {
                        new Handler(Looper.getMainLooper()).post(
                                new Runnable() {
                                    @SuppressLint("DefaultLocale")
                                    @Override
                                    public void run() {
                                        if (XYVideoUtils.callStartTime <= -1) {
                                            return;
                                        }
                                        long duration = XYVideoUtils.getCallTime() / 1000;
                                        LogUtil.i(TAG, "duration===>" + duration);
                                        if (duration >= 3600) {
                                            callTimeTv.setText(String.format(
                                                    "%d:%02d:%02d",
                                                    duration / 3600,
                                                    (duration % 3600) / 60,
                                                    (duration % 60)));
                                        } else {
                                            callTimeTv.setText(
                                                    String.format(
                                                            "%02d:%02d",
                                                            (duration % 3600) / 60,
                                                            (duration % 60)));
                                        }
                                    }
                                });
//                        Looper.prepare();
                    }
                };
        mStartTimeTimer = new Timer();
        mStartTimeTimer.schedule(mStartTimeTask, 0, 1000);

    }

}
