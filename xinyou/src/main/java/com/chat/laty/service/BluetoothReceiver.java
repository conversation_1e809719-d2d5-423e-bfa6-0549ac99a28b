package com.chat.laty.service;

import android.bluetooth.BluetoothDevice;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.chat.laty.base.XYApplication;
import com.chat.laty.utils.BgmPlayer;
import com.chat.laty.utils.LogUtil;

public class BluetoothReceiver extends BroadcastReceiver {
    private static final String TAG = "BluetoothReceiver";
    @Override
    public void onReceive(final Context context, final Intent intent) {
        LogUtil.i(TAG,"onReceive - BluetoothBroadcast");
        final String action = intent.getAction();

        // 当蓝牙设备连接状态改变时
        if (BluetoothDevice.ACTION_ACL_CONNECTED.equals(action) ) {
            // 蓝牙耳机已连接
//            Toaster.show("蓝牙耳机连接");
            BgmPlayer.getInstance(XYApplication.getAppApplicationContext()).changeToBluetoothSco();
            // 处理连接事件
        } else if (BluetoothDevice.ACTION_ACL_DISCONNECTED.equals(action)) {
            // 蓝牙耳机已断开
            LogUtil.i(TAG,"onReceive - 蓝牙耳机已断开");
            BgmPlayer.getInstance(XYApplication.getAppApplicationContext()).changeToSpeaker();
            // 处理断开事件
        }
    }
}
