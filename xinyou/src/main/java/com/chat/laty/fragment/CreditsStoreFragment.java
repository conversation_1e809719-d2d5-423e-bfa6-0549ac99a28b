package com.chat.laty.fragment;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;

import androidx.activity.result.ActivityResultLauncher;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.chat.laty.R;
import com.chat.laty.activity.CreditsStoreDetailActivity;
import com.chat.laty.base.BaseFragment;
import com.chat.laty.entity.CreditsStoreInfoModel;
import com.chat.laty.utils.PicassoUtils;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;

/**
 * <AUTHOR>
 * @date 2023/11/21 11:20
 * @description:好友
 */
public class CreditsStoreFragment extends BaseFragment {

    private static final String KEY_ARGS_TYPE = "_KEY_TO_ARGS_TYPE_";

    int type = 0;

    @BindView(R.id.recycler_view)
    RecyclerView recyclerView;

    CreditsStoreAdapter adapter;

    ActivityResultLauncher launcher;

    List<CreditsStoreInfoModel.CreditStoreItemModel> list = new ArrayList<>();

    @SuppressLint("NotifyDataSetChanged")
    public void setDataList(List<CreditsStoreInfoModel.CreditStoreItemModel> list) {
        this.list.clear();
        this.list.addAll(list);
        if (adapter != null) {
            adapter.notifyDataSetChanged();
        }
    }

    public CreditsStoreFragment addLauncher(ActivityResultLauncher launcher) {
        this.launcher = launcher;
        return this;
    }

    public static CreditsStoreFragment newInstance(int type) {
        Bundle args = new Bundle();
        CreditsStoreFragment fragment = new CreditsStoreFragment();
        args.putInt(KEY_ARGS_TYPE, type);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    protected int getContentViewId() {
        return R.layout.fragment_credits_store_layout;
    }


    @Override
    protected void initDatas() {
        super.initDatas();
    }

    @SuppressLint("ClickableViewAccessibility")
    @Override
    public void initViews(View rootView) {
        if (getArguments() != null) {
            type = getArguments().getInt(KEY_ARGS_TYPE);
        }
        adapter = new CreditsStoreAdapter(type);

        adapter.setItemAnimation(BaseQuickAdapter.AnimationType.SlideInRight);
        adapter.setOnItemClickListener((baseQuickAdapter, view, i) -> {

            CreditsStoreInfoModel.CreditStoreItemModel item = baseQuickAdapter.getItem(i);
            item.msgType = adapter.getType();

            if (launcher != null) {
                Intent intent = new Intent(getContext(), CreditsStoreDetailActivity.class);
                intent.putExtra(CreditsStoreDetailActivity.KEY_ARGS, item);
                launcher.launch(intent);
            } else {
                CreditsStoreDetailActivity.startAct(getActivity(), item);
            }
        });

        adapter.setItems(list);
        recyclerView.setAdapter(adapter);
    }

    private static class CreditsStoreAdapter extends BaseQuickAdapter<CreditsStoreInfoModel.CreditStoreItemModel, QuickViewHolder> {

        int type = 0;

        public CreditsStoreAdapter(int type) {
            this.type = type;
        }

        @Override
        protected void onBindViewHolder(@NonNull QuickViewHolder helper, int i, @Nullable CreditsStoreInfoModel.CreditStoreItemModel item) {
            helper.setText(R.id.tv_value, item.getNumber() + "条");
            helper.setText(R.id.tv_label, getType());
            helper.setText(R.id.tv_title, item.getName());
            helper.setText(R.id.tv_price, item.getPrice() + "积分");
            PicassoUtils.showImageWithGlide(getContext(), helper.getView(R.id.image_view), item.getProductImg(), 0);
        }

        @NonNull
        @Override
        protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int i) {
            return new QuickViewHolder(R.layout.adapter_credits_store_item_layout, viewGroup);
        }

        public String getType() {
            if (type == 1) {
                return "文字消息";
            } else if (type == 2) {
                return "语音消息";
            } else {
                return "视频消息";
            }
        }
    }
}
