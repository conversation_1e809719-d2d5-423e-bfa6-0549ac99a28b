package com.chat.laty.fragment;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.net.Uri;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.util.Log;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.ImageView;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager.widget.ViewPager;

import com.chat.laty.activity.VisitorMeUI;
import com.google.android.material.tabs.TabLayout;
import com.hjq.permissions.OnPermissionCallback;
import com.hjq.permissions.Permission;
import com.hjq.permissions.XXPermissions;
import com.hjq.toast.Toaster;
import com.lxj.xpopup.XPopup;
import com.chat.laty.activity.GoldCoinActivity;
import com.chat.laty.activity.VipCenter1Activity;
import com.chat.laty.dialog.QSNDialog;
import com.chat.laty.dialog.RecommendDialog;
import com.chat.laty.dialog.YUEBZDialog;
import com.chat.laty.entity.RecommendationBean;
import com.chat.laty.utils.PermissionInterceptor;
import com.chat.laty.R;
import com.chat.laty.activity.CreditsStoreActivity;
import com.chat.laty.activity.FatePairingUI;
import com.chat.laty.activity.TaskCenterActivity;
import com.chat.laty.activity.WebViewActivity;
import com.chat.laty.base.AppHelper;
import com.chat.laty.base.XYApplication;
import com.chat.laty.contants.Constant;
import com.chat.laty.controllers.DiscoverController;
import com.chat.laty.dialog.BottomFilterDialog;
import com.chat.laty.dialog.TodayFateDialog;
import com.chat.laty.dialog.UpdateAppDialog;
import com.chat.laty.entity.AppVersionInfo;
import com.chat.laty.entity.BannerInfo;
import com.chat.laty.entity.FilterBean;
import com.chat.laty.entity.OtherUserInfo;
import com.chat.laty.entity.RYTokenInfo;
import com.chat.laty.entity.RyUserInfo;
import com.chat.laty.entity.TodayFateEntity;
import com.chat.laty.entity.TopGuideInfo;
import com.chat.laty.entity.WebBeanInfo;
import com.chat.laty.entity.WeiChatPayInfo;
import com.chat.laty.entity.XYUserInfo;
import com.chat.laty.entity.event.FatePairEvent;
import com.chat.laty.mvp.BasePresenterFragment;
import com.chat.laty.presenters.DiscoverPresenter;
import com.chat.laty.presenters.PayManager;
import com.chat.laty.utils.XYSPUtils;
import com.chat.laty.utils.net.Common;
import com.chat.laty.view.AttachReButton;
import com.chat.laty.view.SpeedDateSurePopup;
import com.youth.banner.listener.OnBannerListener;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * <AUTHOR>
 * @date 2023/11/21 11:20
 * @description:发现
 */
public class FirstPageFragment extends BasePresenterFragment<DiscoverPresenter> implements DiscoverController.View, OnBannerListener {

    //    @BindView(R.id.banner)
//    Banner mBannerView;
    @BindView(R.id.top_guide_rv)
    RecyclerView mTopGuideRv;


//    @BindView(R.id.common_tab_layout)
//    CommonTabLayout mCommonTabLayout;

    @BindView(R.id.tab_layout)
    TabLayout mTabLayout;

    @BindView(R.id.fate_peidui_rl)
    AttachReButton mFatePeiduiRl;

//    @BindView(R.id.fragment_srl)
//    SmartRefreshLayout mSmartRefreshLayout;

    @BindView(R.id.viewpager)
    ViewPager mViewPager;

    @BindView(R.id.item_peidui)
    ImageView item_peidui;
    @BindView(R.id.cl_rwzx)
    ImageView cl_rwzx;
    @BindView(R.id.cl_jfdh)
    View cl_jfdh;
    @BindView(R.id.cl_hyzx)
    ImageView cl_hyzx;
    @BindView(R.id.cl_fzpzn)
    ImageView cl_fzpzn;
    @BindView(R.id.cl_jryf)
    ImageView cl_jryf;
    @BindView(R.id.cl_skgw)
    ImageView cl_skgw;

    private ArrayList<Fragment> mFragments = new ArrayList<>();

    //    TopGuideAdapter mAdapter;
    FirstPageAdapter mPageAdapter;
    private RecommendDialog mRecommendDialog;


    List<TopGuideInfo> mTopGuides = new ArrayList<>();

    private int mCurrentPosition = 0;

    FilterBean mCurrentInfo;

    TodayFateDialog mTodayFateDialog;

    RyUserInfo mUserInfo;
    private String[] mTitles = {"热门", "发现"};

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onFilterBeanCallback(FatePairEvent info) {
        showTopGuide();
    }

    @Override
    protected int getContentViewId() {
        return R.layout.fragment_firstpage_temp_layout;
    }

    @Override
    public void initViews(View rootView) {
        /*mAdapter = new TopGuideAdapter();
        LinearLayoutManager llm = new LinearLayoutManager(getActivity());
        llm.setOrientation(LinearLayoutManager.HORIZONTAL);
        mTopGuideRv.setLayoutManager(llm);
        mTopGuideRv.setAdapter(mAdapter);

        mAdapter.setOnItemClickListener((adapter, view, position) -> {
            if (XYApplication.appLogin(true)) {
                TopGuideInfo info = (TopGuideInfo) adapter.getItem(position);
                goTopGuide(info);
            }
        });*/

        adjustImageViewWidths();

        showTopGuide();

        for (int i = 0; i < mTitles.length; i++) {
            mFragments.add(RecomendFragment.newInstance(i));
        }

        mPageAdapter = new FirstPageAdapter(getChildFragmentManager());

        mViewPager.setAdapter(mPageAdapter);

        // 设置TabLayout和ViewPager的关联
        for (int i = 0; i < mTitles.length; i++) {
            TabLayout.Tab tab = mTabLayout.newTab();
            View view = LayoutInflater.from(getContext()).inflate(R.layout.tab_layout_item_view, null);
            TextView textView = view.findViewById(R.id.tv_title);
            textView.setText(mTitles[i]);
            tab.setCustomView(view);
            mTabLayout.addTab(tab);
        }

        mTabLayout.addOnTabSelectedListener(new TabLayout.OnTabSelectedListener() {
            @Override
            public void onTabSelected(TabLayout.Tab tab) {
                toggleTextStyle(tab.getCustomView(), true);
                mViewPager.setCurrentItem(tab.getPosition());
            }

            @Override
            public void onTabUnselected(TabLayout.Tab tab) {
                toggleTextStyle(tab.getCustomView(), false);
            }

            @Override
            public void onTabReselected(TabLayout.Tab tab) {

            }
        });

        // 设置默认选中第一个Tab
        if (mTabLayout.getTabCount() > 0) {
            TabLayout.Tab firstTab = mTabLayout.getTabAt(0);
            if (firstTab != null) {
                toggleTextStyle(firstTab.getCustomView(), true);
            }
        }

        // 设置ViewPager页面变化监听
        mViewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

            }

            @Override
            public void onPageSelected(int position) {
                mTabLayout.selectTab(mTabLayout.getTabAt(position));
            }

            @Override
            public void onPageScrollStateChanged(int state) {

            }
        });

        if (mRecommendDialog == null) {
            mRecommendDialog = RecommendDialog.getInstance();
            mRecommendDialog.setOnClickListener(new RecommendDialog.OnClickListener() {
                @Override
                public void onRefresh() {
                    presenter.getRecommendList();
                }

                @Override
                public void onOneKey(List<String> uids) {
                    presenter.getOneKeyAccost(uids);
                }
            });
        }

        if (XYApplication.appLogin(false)) {
            presenter.getRYToken();
            presenter.getNewVersions();
            getUserInfo();


            //青少年模式增加
            Log.i("QSNDialog", "start");

            SharedPreferences sharedPreferences = requireActivity().getSharedPreferences("app_prefs", Context.MODE_PRIVATE);

            if (!sharedPreferences.getBoolean("qsnms_status", false)) {
                QSNDialog qsndialog = QSNDialog.getInstance();
                qsndialog.setOnClickListener(new QSNDialog.OnQSNClickListener() {
                    @Override
                    public void onSureAgree() {

                        SharedPreferences prefs = requireActivity().getSharedPreferences("app_prefs", Context.MODE_PRIVATE);
                        boolean isFirstRun = prefs.getBoolean("is_first_run", true);

                        if (isFirstRun) {
                            prefs.edit().putBoolean("is_first_run", false).apply();
                            XXPermissions.with(getContext()).permission(Permission.POST_NOTIFICATIONS).interceptor(new PermissionInterceptor()).request(new OnPermissionCallback() {
                                @Override
                                public void onGranted(List<String> permissions, boolean all) {
                                }
                            });
                        }

                        if (!XYApplication.getCurrentIsMan()) {
                            RyUserInfo userinfo = XYApplication.getCurrentUserInfo();
                            if ((userinfo.getIsName() != null && userinfo.getIsReal() != null) && (!userinfo.getIsName().equals("0") && !userinfo.getIsReal().equals("0"))) {
                                mRecommendDialog.show(getChildFragmentManager(), "每日推荐");
                                presenter.getRecommendList();
                            }

                        } else {
                            mRecommendDialog.show(getChildFragmentManager(), "每日推荐");
                            presenter.getRecommendList();
                        }
                    }
                });

                qsndialog.show(getChildFragmentManager(), "");
            }
        }
    }

    private void showTopGuide() {
        /*mTopGuides.clear();
        if (XYSPUtils.getBoolean(Constant.KEY_USER_FATE_PAIRING))
            mTopGuides.add(new TopGuideInfo("配对中~", "遇见心仪的Ta", R.mipmap.bg_yuanfenpeidui, R.mipmap.yuanfenpeidui));
        else
            mTopGuides.add(new TopGuideInfo("缘分配对", "遇见心仪的Ta", R.mipmap.bg_yuanfenpeidui, R.mipmap.yuanfenpeidui));
        mTopGuides.add(new TopGuideInfo("任务中心", "做任务领奖励", R.mipmap.bg_renwuzhongxin, R.mipmap.renwuzhongxin));
        mTopGuides.add(new TopGuideInfo("积分兑换", "热门礼包兑换", R.mipmap.bg_jifenduihuan, R.mipmap.jifenduihuan));
        mTopGuides.add(new TopGuideInfo("会员中心", "享受会员福利", R.mipmap.bg_vip, R.mipmap.vip));
        mTopGuides.add(new TopGuideInfo("防诈骗指南", "安全提示防诈", R.mipmap.bg_fangzhapian, R.mipmap.fangzhapian));
        mAdapter.setItems(mTopGuides);
        mAdapter.notifyDataSetChanged();*/

        item_peidui.setOnClickListener(v -> {
            if (XYApplication.getCurrentUserStatus()) {
                if (XYSPUtils.getBoolean(Constant.KEY_USER_SPEED_DATE_SURE) || XYSPUtils.getBoolean(Constant.KEY_USER_FATE_PAIRING)) {
                    if (XYApplication.getCurrentIsMan()) {
                        presenter.getUserInfo(XYSPUtils.getString(Common.KEY_APP_USER_RY_ID));
                    } else {
                        FatePairingUI.start(getActivity());
                    }

                } else {
                    if (XYApplication.getCurrentIsMan()) {
                        new XPopup.Builder(getContext()).autoOpenSoftInput(true)
//                        .isViewMode(true)
                                .asCustom(new SpeedDateSurePopup(getActivity(), new SpeedDateSurePopup.OnItemClick() {
                                    @Override
                                    public void onItemClick(int type) {
                                        presenter.getUserInfo(XYSPUtils.getString(Common.KEY_APP_USER_RY_ID));
                                    }
                                })).show();
                    } else {
                        FatePairingUI.start(getActivity());
                    }
                }
            }
        });

        cl_jryf.setOnClickListener(v -> {
            if (XYApplication.appLogin(true)) {
                VipCenter1Activity.startAct(getActivity());
            }
        });

        cl_skgw.setOnClickListener(v -> {
            if (XYApplication.appLogin(true)) {
                TaskCenterActivity.startAct(getActivity());
            }
        });

        cl_rwzx.setOnClickListener(v -> {
            TaskCenterActivity.startAct(getActivity());
        });

        cl_jfdh.setOnClickListener(v -> {
            if (XYApplication.appLogin(true)) {
                CreditsStoreActivity.startAct(getActivity());
            }
        });

        cl_hyzx.setOnClickListener(v -> {
            /*if (mUserInfo == null) {
                Toaster.showShort("无法获取您的ID，请尝试从新登录后再试！");
            }
            WebViewActivity.startActivity(getActivity(), "联系客服", Common.kefuUrl + "&userId=" + mUserInfo.getId() + "&userName=" + mUserInfo.getNickname());*/

            VipCenter1Activity.startAct(getActivity());
        });

        cl_fzpzn.setOnClickListener(v -> {
            presenter.getAccordByNum(5);
        });


    }

    public void openBrowser(String url) {
        final Intent intent = new Intent();
        intent.setAction(Intent.ACTION_VIEW);
        intent.setData(Uri.parse(url));
        if (intent.resolveActivity(getActivity().getPackageManager()) != null) {
            final ComponentName componentName = intent.resolveActivity(getActivity().getPackageManager());
            getActivity().startActivity(Intent.createChooser(intent, "请选择浏览器"));
        } else {
            startActivity(intent);
//            WebViewActivity.startActivity(getActivity(), "客服", url);
        }
    }

    private void getUserInfo() {
        if (XYApplication.appLogin(false)) {
            mUserInfo = XYApplication.getCurrentUserInfo();
            showUserInfo();
        }
    }

    private void showUserInfo() {
        if (null != mUserInfo && !TextUtils.isEmpty(mUserInfo.getSex()))
            mFatePeiduiRl.setVisibility("1".equals(mUserInfo.getSex()) ? View.GONE : View.VISIBLE);
    }

    @OnClick({R.id.search_tv, R.id.fate_peidui_rl})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.search_tv:
                showFilterDialog();
                break;

            case R.id.fate_peidui_rl:
                if (XYApplication.getCurrentUserStatus()) {
                    showTodayFateDialog();
                }
                break;
        }
    }

    private void goTopGuide(TopGuideInfo info) {
        switch (info.getBigTitle()) {
            case "缘分配对":
            case "配对中~":
                if (XYApplication.getCurrentUserStatus()) {
                    if (XYSPUtils.getBoolean(Constant.KEY_USER_SPEED_DATE_SURE) || XYSPUtils.getBoolean(Constant.KEY_USER_FATE_PAIRING)) {
                        if (XYApplication.getCurrentIsMan()) {
                            presenter.getUserInfo(XYSPUtils.getString(Common.KEY_APP_USER_RY_ID));
                        } else {
                            FatePairingUI.start(getActivity());
                        }

                    } else {
                        if (XYApplication.getCurrentIsMan()) {
                            new XPopup.Builder(getContext()).autoOpenSoftInput(true)
//                        .isViewMode(true)
                                    .asCustom(new SpeedDateSurePopup(getActivity(), new SpeedDateSurePopup.OnItemClick() {
                                        @Override
                                        public void onItemClick(int type) {
                                            presenter.getUserInfo(XYSPUtils.getString(Common.KEY_APP_USER_RY_ID));
                                        }
                                    })).show();
                        } else {
                            FatePairingUI.start(getActivity());
                        }
                    }
                }
                break;
            case "会员中心":
                VipCenter1Activity.startAct(getActivity());
                break;
            case "任务中心":
                TaskCenterActivity.startAct(getActivity());
                break;
            case "积分兑换":
                CreditsStoreActivity.startAct(getActivity());
                break;
            case "防诈骗指南":
                presenter.getAccordByNum(5);
                break;
        }
    }

    @Override
    protected DiscoverPresenter setPresenter() {
        return new DiscoverPresenter(this);
    }

    @Override
    public FragmentActivity context() {
        return getActivity();
    }

    @Override
    public void showPayParm(RYTokenInfo data) {
        if (null != data && !TextUtils.isEmpty(data.getPayUrl())) {
            PayManager.alipayPay(getActivity(), data.getPayUrl());
        }
    }

    @Override
    public void showAXPayParm(WeiChatPayInfo data) {
        if (null != data) {
            PayManager.weiChatPay(getActivity(), data);
        }
    }

    @Override
    public void showRecommend(List<OtherUserInfo> list) {

    }

    @Override
    public void showBanners(List<BannerInfo> data) {
        /*mBannerView.setAdapter(new BannerImageLoader(data));
        if (getActivity() == null) {
            return;
        }
        CircleIndicator circleIndicator = new CircleIndicator(getActivity());
        mBannerView.setIndicator(circleIndicator);
        mBannerView.setOnBannerListener(this);
        mBannerView.start();*/
    }

    @Override
    public void showAccostToUserSuccess() {
    }

    @Override
    public void showTodayFate(TodayFateEntity todayFate) {
        mTodayFateDialog.setNewDatas(todayFate);
    }

    @Override
    public void showAppVersion(AppVersionInfo appInfo) {
//        if (null != appInfo && AppHelper.getApplicationVersionCode(getActivity()) < appInfo.getVersionCode()) {
//            XXPermissions.with(getActivity()).permission(Permission.MANAGE_EXTERNAL_STORAGE, Permission.REQUEST_INSTALL_PACKAGES).request(new OnPermissionCallback() {
//                @Override
//                public void onGranted(List<String> permissions, boolean all) {
//                    if (!all) {
//                        return;
//                    }
        if (null != appInfo && AppHelper.getApplicationVersionCode(getActivity()) < appInfo.getVersionCode()) {
            update(appInfo);
        }
//                }
//            });
//        }
    }

    @Override
    public void showOneClickAccostCallback() {
        if (null != mTodayFateDialog) {
            mTodayFateDialog.dismiss();
            Toaster.show("搭讪成功");
        }
    }

    @Override
    public void showWebInfo(WebBeanInfo data) {
        WebViewActivity.startActivity(getActivity(), "附近聊爱探约", data.getUrl());
    }

    @Override
    public void showUserDetails(XYUserInfo userInfo) {
        if ((Double.valueOf(userInfo.getFreeVoiceNum()) > Double.valueOf(userInfo.getVideoGearNum()) || Double.valueOf(userInfo.getGoldNum()) > Double.valueOf(userInfo.getVideoGearNum()))) {
            FatePairingUI.start(getActivity());
        } else {
            Toaster.show("金币不足");
        }
    }

    @Override
    public void showRecomPop(List<RecommendationBean> recommendationBeans) {

        if (mRecommendDialog != null) {
            mRecommendDialog.setData(recommendationBeans);
        }

    }

    @Override
    public void showRecomPop1() {
        YUEBZDialog yuebzDialog = YUEBZDialog.getInstance();
        yuebzDialog.show(getChildFragmentManager(), "金币余额");
        yuebzDialog.setOnClickListener(new YUEBZDialog.OnClickListener() {
            @Override
            public void onSureAgree() {
                GoldCoinActivity.startAct(getActivity());
            }
        });
    }

    private void showFilterDialog() {
        BottomFilterDialog dialog = new BottomFilterDialog(getActivity());
        dialog.setOnDialogCallbackListener(new BottomFilterDialog.OnDialogCallbackListener() {
            @Override
            public void onDialogSelectItem(FilterBean info) {
                mCurrentInfo = info;
                EventBus.getDefault().post(mCurrentInfo);
            }
        });
        dialog.show();
    }

    private void showTodayFateDialog() {
        mTodayFateDialog = new TodayFateDialog(getActivity());
        presenter.getTodayFate();

        mTodayFateDialog.setOnDialogCallbackListener(new TodayFateDialog.OnDialogCallbackListener() {
            @Override
            public void onDialogSelectItem(String yuyinId, String textId, com.alibaba.fastjson.JSONArray userIds) {
                presenter.putClickAccost(yuyinId, textId, userIds);
            }
        });
        mTodayFateDialog.show();
    }

    private void update(AppVersionInfo appInfo) {
        UpdateAppDialog updateAppDialog = UpdateAppDialog.getInstance();
        updateAppDialog.setAppVersionInfo(appInfo);
        updateAppDialog.setOnClickListener(new UpdateAppDialog.OnUserNoteClickListener() {
            @Override
            public void onAgree() {
                XXPermissions.with(getActivity()).permission(Permission.MANAGE_EXTERNAL_STORAGE, Permission.REQUEST_INSTALL_PACKAGES).interceptor(new PermissionInterceptor()).request(new OnPermissionCallback() {
                    @Override
                    public void onGranted(List<String> permissions, boolean all) {
                        if (!all) {
                            return;
                        }
                        presenter.downLoadFile(appInfo.getAppUrl(), "xinyou");
                    }
                });
            }

            @Override
            public void onCancel() {

            }
        });
        updateAppDialog.show(getChildFragmentManager(), "");
    }

    @Override
    public void OnBannerClick(Object data, int position) {
//        BannerInfo info = (BannerInfo) data;
//        if (null != info && null != info.getLinkUrl()) {
//            WebViewActivity.startActivity(getActivity(), "附近聊爱探约", info.getLinkUrl());
//        }
    }

    @Override
    public void onResume() {
        super.onResume();
        getUserInfo();
//        presenter.getBanners();

    }

    @Override
    protected void onVisibleChanged(boolean isVisible) {
        super.onVisibleChanged(isVisible);
        if (isVisible) {
            getUserInfo();
//            presenter.getBanners();

        }
    }

    private class FirstPageAdapter extends FragmentPagerAdapter {
        public FirstPageAdapter(FragmentManager fm) {
            super(fm);
        }

        @Override
        public int getCount() {
            return mFragments.size();
        }

        @Override
        public CharSequence getPageTitle(int position) {
            return mTitles[position];
        }

        @Override
        public Fragment getItem(int position) {
            return mFragments.get(position);
        }

    }

    private void toggleTextStyle(View view, boolean selected) {
        TextView textView = view.findViewById(R.id.tv_title);
        if (textView != null) {
            textView.setSelected(selected);
        }
    }

    @Override
    protected boolean isUseEventBus() {
        return true;
    }

    private void adjustImageViewWidths() {
        // 不需要动态调整宽度，因为我们使用LinearLayout的weight属性来自动分配
        // 注释掉这个方法的内容，让布局文件中的weight设置生效

        /*
        // 获取屏幕宽度
        DisplayMetrics displayMetrics = new DisplayMetrics();
        getActivity().getWindowManager().getDefaultDisplay().getMetrics(displayMetrics);
        int screenWidth = displayMetrics.widthPixels;

        // 计算每个元素应该占用的宽度（2x2网格，每行两个元素）
        int totalMargin = (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, 30, getResources().getDisplayMetrics()); // 左右 margin 总和
        int itemSpacing = (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, 16, getResources().getDisplayMetrics()); // 两个图片之间的间隙

        int totalAvailableWidth = screenWidth - totalMargin - itemSpacing;
        int itemWidth = totalAvailableWidth / 2; // 每行两个元素

        setViewWidth(item_peidui, itemWidth);
        setViewWidth(cl_skgw, itemWidth);
        setViewWidth(cl_jryf, itemWidth);
        setViewWidth(cl_jfdh, itemWidth);
        */
    }

    // 设置 View 宽度的方法
    private void setViewWidth(View view, int width) {
        if (view == null) return;

        ViewGroup.LayoutParams params = view.getLayoutParams();
        if (params instanceof LinearLayout.LayoutParams) {
            ((LinearLayout.LayoutParams) params).width = width;
        } else if (params instanceof ConstraintLayout.LayoutParams) {
            ((ConstraintLayout.LayoutParams) params).width = width;
        } else {
            params.width = width;
        }
        view.setLayoutParams(params);
    }

}
