package com.chat.laty.fragment;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.header.ClassicsHeader;
import com.chat.laty.R;
import com.chat.laty.controllers.GoldCoinController;
import com.chat.laty.entity.GoldCoinDetailModel;
import com.chat.laty.mvp.BasePresenterFragment;
import com.chat.laty.presenters.GoldCoinPresenter;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;

/**
 * <AUTHOR>
 * @date 2023/11/21 11:20
 * @description:动态
 */
public class GoldCoinDetailFragment extends BasePresenterFragment<GoldCoinPresenter> implements GoldCoinController.IDetailView {
    @BindView(R.id.smart_refresh_layout)

    SmartRefreshLayout refreshLayout;
    @BindView(R.id.recycler_view)
    RecyclerView recyclerView;
    private static final String KEY_ARGS = "args";

    public static GoldCoinDetailFragment newInstance(int type) {
        Bundle args = new Bundle();
        GoldCoinDetailFragment fragment = new GoldCoinDetailFragment();
        args.putInt(KEY_ARGS, type);
        fragment.setArguments(args);
        return fragment;
    }

    int page = 1;
    int pageSize = 20;
    int type = 0;
    GoldCoinRecordAdapter adapter;

    List<GoldCoinDetailModel> list = new ArrayList<>();

    @Override
    protected int getContentViewId() {
        return R.layout.fragment_gold_coin_detail_layout;
    }

    @Override
    public void initViews(View rootView) {

        ClassicsHeader classicsHeader = new ClassicsHeader(context());
        classicsHeader.setProgressDrawable(ContextCompat.getDrawable(context(), R.drawable.pull_refresh_view_refresh_custom));
        classicsHeader.setArrowDrawable(ContextCompat.getDrawable(context(), R.drawable.pull_refresh_view_refresh_custom));
        classicsHeader.setEnableLastTime(false);
        refreshLayout.setRefreshHeader(classicsHeader);

        refreshLayout.setOnRefreshListener(refreshLayout -> presenter.getGoldCoinDetail(1, pageSize, false, true, false));
        refreshLayout.setOnLoadMoreListener(refreshLayout -> presenter.getGoldCoinDetail(page, pageSize, false, false, true));

        adapter = new GoldCoinRecordAdapter();
        adapter.setItems(list);
        recyclerView.setAdapter(adapter);
        adapter.setStateView(getLayoutInflater().inflate(R.layout.community_empty_layout, null));
        adapter.setStateViewEnable(true);

        presenter.getGoldCoinDetail(page, pageSize, true, false, false);
    }


    @Override
    public FragmentActivity context() {
        return getActivity();
    }


    @SuppressLint("NotifyDataSetChanged")
    @Override
    public void getDetailSucceed(List<GoldCoinDetailModel> list, boolean load, boolean refresh, boolean loadmore) {

        if (load) {
            this.list.clear();
            this.list.addAll(list);
        } else if (refresh) {
            page = 1;
            this.list.clear();
            this.list.addAll(list);
            refreshLayout.finishRefresh();
        } else if (loadmore) {
            this.list.addAll(list);
            refreshLayout.finishLoadMore();
        }
        if (list.size() >= pageSize) {
            page++;
            refreshLayout.setEnableLoadMore(true);
        } else {
            refreshLayout.setEnableLoadMore(false);
        }

        adapter.notifyDataSetChanged();
    }

    @Override
    public void getDetailFailed(boolean load, boolean refresh, boolean loadmore) {
//        dismissProgressDialog();
        if (refresh) {
            refreshLayout.finishRefresh();
        } else {
            refreshLayout.finishLoadMore();
        }
    }

    @Override
    protected GoldCoinPresenter setPresenter() {
        if (getArguments() != null) {
            type = getArguments().getInt(KEY_ARGS);
        }
        GoldCoinPresenter presenter = new GoldCoinPresenter(null, this);
        presenter.setType(type);
        return presenter;
    }


    static class GoldCoinRecordAdapter extends BaseQuickAdapter<GoldCoinDetailModel, QuickViewHolder> {

        @Override
        protected void onBindViewHolder(@NonNull QuickViewHolder helper, int i, @Nullable GoldCoinDetailModel item) {
            helper.setText(R.id.tv_value, (item.getChangeType() == 2 ? "-" : "+") + item.getChangeGold());
            helper.setText(R.id.tv_time, item.getCreateTime());
            helper.setText(R.id.tv_total, "余额：" + item.getLastGold() + "金币");
            helper.setText(R.id.tv_detail, item.getChangeDetail());
            if (item.getChangeType() == 2) {
                helper.setVisible(R.id.tv_detail, true);
            } else {
                helper.setGone(R.id.tv_detail, true);
            }
        }

        @NonNull
        @Override
        protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int i) {
            return new QuickViewHolder(R.layout.adapter_gold_coin_records_layout, viewGroup);
        }
    }
}
