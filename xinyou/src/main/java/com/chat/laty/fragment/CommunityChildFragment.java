package com.chat.laty.fragment;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;

import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.hjq.permissions.Permission;
import com.hjq.permissions.XXPermissions;
import com.hjq.toast.Toaster;
import com.chat.laty.utils.PermissionInterceptor;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.header.ClassicsHeader;
import com.chat.laty.R;
import com.chat.laty.activity.MineDynamicDetailUI;
import com.chat.laty.activity.UserCenterUI;
import com.chat.laty.adapter.CommunityAdapter;
import com.chat.laty.base.XYApplication;
import com.chat.laty.controllers.CommunityController;
import com.chat.laty.entity.AMapLocationEntity;
import com.chat.laty.entity.BaseBean;
import com.chat.laty.entity.CommunityInfoModel;
import com.chat.laty.entity.RyUserInfo;
import com.chat.laty.entity.event.LocationEvent;
import com.chat.laty.greenDao.RyUserInfoDao;
import com.chat.laty.mvp.BasePresenterFragment;
import com.chat.laty.presenters.CommunityPresenter;
import com.chat.laty.utils.KeyboardUtil;
import com.chat.laty.utils.LocationUtils;
import com.chat.laty.utils.XYSPUtils;
import com.chat.laty.utils.net.Common;
import com.chat.laty.view.RecyclerItemDecoration;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import io.rong.imkit.utils.RouteUtils;
import io.rong.imlib.model.Conversation;
import io.rong.imlib.model.ConversationIdentifier;

/**
 * <AUTHOR>
 * @date 2023/11/21 11:20
 * @description:好友
 */
public class CommunityChildFragment extends BasePresenterFragment<CommunityPresenter> implements CommunityController.IListView, CommunityController.IAccostView {

    private static final String KEY_TO_COMMUNITY_TYPE = "_KEY_TO_COMMUNITY_TYPE_";

    int type = 0;

    @BindView(R.id.smart_refresh_layout)
    SmartRefreshLayout refreshLayout;
    @BindView(R.id.recycler_view)
    RecyclerView recyclerView;

    @BindView(R.id.v_input)
    View vInput;
    @BindView(R.id.et_input)
    EditText etInput;
    @BindView(R.id.btn_send)
    Button send;


    CommunityAdapter adapter;

    int page = 1;

    int pageSize = 15;

    static final int request_code = 1008;

    String userId;
    RyUserInfo userInfo;

    AMapLocationEntity locationEntity;

    private String lat = "";
    private String lng = "";

    List<CommunityInfoModel> list = new ArrayList<>();

    public static CommunityChildFragment newInstance(int communityType) {
        Bundle args = new Bundle();
        CommunityChildFragment fragment = new CommunityChildFragment();
        args.putInt(KEY_TO_COMMUNITY_TYPE, communityType);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    protected int getContentViewId() {
        return R.layout.fragment_community_child_layout;
    }

    @SuppressLint("ClickableViewAccessibility")
    @Override
    public void initViews(View rootView) {
        initLocalInfo();

        adapter = new CommunityAdapter(type);
        userId = XYSPUtils.getString(Common.KEY_APP_USER_RY_ID);

        userInfo = XYApplication.getDaoInstant().getRyUserInfoDao().queryBuilder().where(RyUserInfoDao.Properties.Id.eq(userId)).unique();
        if (userInfo != null) {
            adapter.setAvatar(userInfo.getAvatar());
        }

        adapter.setCallback(new CommunityAdapter.OnItemCommentCallback() {
            @Override
            public void delete(CommunityInfoModel model, int index1, CommunityInfoModel.CommentInfoModel data) {
                // todo 删除评论
            }

            @Override
            public void replay(CommunityInfoModel model, int index1, CommunityInfoModel.CommentInfoModel data) {
                showInputDialogWithComment(model, index1, 1, data);
            }
        });
        recyclerView.setAdapter(adapter);
        recyclerView.addItemDecoration(new RecyclerItemDecoration());
        recyclerView.setOnTouchListener((v, event) -> {
            if (vInput.getVisibility() == View.VISIBLE) {
                toggleEditVisibility(View.GONE);
                return true;
            }
            return false;
        });

        adapter.setItems(list);
        adapter.setItemAnimation(BaseQuickAdapter.AnimationType.SlideInRight);
        adapter.setStateView(getLayoutInflater().inflate(R.layout.community_empty_layout, null));
        adapter.setStateViewEnable(true);

        ClassicsHeader classicsHeader = new ClassicsHeader(context());
        classicsHeader.setProgressDrawable(ContextCompat.getDrawable(context(), R.drawable.pull_refresh_view_refresh_custom));
        classicsHeader.setArrowDrawable(ContextCompat.getDrawable(context(), R.drawable.pull_refresh_view_refresh_custom));
        classicsHeader.setEnableLastTime(false);
        refreshLayout.setRefreshHeader(classicsHeader);

        refreshLayout.setOnRefreshListener(refreshLayout -> presenter.getCommunityList(1, pageSize, lat, lng, false, true, false));
        refreshLayout.setOnLoadMoreListener(refreshLayout -> presenter.getCommunityList(page, pageSize, lat, lng, false, false, true));

        // item click
        adapter.setOnItemClickListener((baseQuickAdapter, view, i) -> {
            CommunityInfoModel item = baseQuickAdapter.getItem(i);
            MineDynamicDetailUI.startActForResult(getActivity(), item, type, request_code);
//            MineDynamicDetailUI.startActById(getActivity(), item.getId());
            toggleEditVisibility(View.GONE);
        });
        // 头像
        adapter.addOnItemChildClickListener(R.id.iv_avatar, (baseQuickAdapter, view, i) -> {
            CommunityInfoModel model = baseQuickAdapter.getItem(i);
            UserCenterUI.start(getContext(), model.getUserId());
        });

        // 私聊 / 搭讪
        adapter.addOnItemChildClickListener(R.id.v_interact, (baseQuickAdapter, view, i) -> {
            boolean result = presenter.isNotRzUser(null);
            if (result) return;

            CommunityInfoModel model = baseQuickAdapter.getItem(i);
            String status = model.getIsAccost();
            if (TextUtils.equals("1", status)) {
//                ChatActivity.start(getContext(), model.getUserId());
                ConversationIdentifier conversationIdentifier = new ConversationIdentifier(Conversation.ConversationType.PRIVATE, model.getUserId());
                RouteUtils.routeToConversationActivity(getContext(), conversationIdentifier, false, null);
            } else {
                presenter.accostToUser(model.getUserId(), model, i);
            }
        });

        // 点赞
        adapter.addOnItemChildClickListener(R.id.v_collect, (baseQuickAdapter, view, i) -> {
            CommunityInfoModel model = baseQuickAdapter.getItem(i);
            presenter.likeToUser(model, i);
        });

        // 评论
        adapter.addOnItemChildClickListener(R.id.v_chat, (baseQuickAdapter, view, i) -> {
            CommunityInfoModel item = baseQuickAdapter.getItem(i);
            showInputDialogWithComment(item, i, 0, null);
        });
        adapter.addOnItemChildClickListener(R.id.tv_input, (baseQuickAdapter, view, i) -> {
            CommunityInfoModel item = baseQuickAdapter.getItem(i);
            showInputDialogWithComment(item, i, 0, null);
        });

        presenter.getCommunityList(page, pageSize, lat, lng, true, false, false);
    }


    private void initLocalInfo() {
        if (type != 2) {
            return;
        }

        locationEntity = XYApplication.locationEntity;

        if (locationEntity != null) {
            lat = String.valueOf(locationEntity.getLatitude());
            lng = String.valueOf(locationEntity.getLongitude());
            return;
        }

        XXPermissions.with(getActivity()).permission(Permission.ACCESS_COARSE_LOCATION, Permission.ACCESS_FINE_LOCATION).interceptor(new PermissionInterceptor()).request((permissions, all) -> {
            if (!all) {
                return;
            }
            LocationUtils.getInstance().startLocalService();
        });
    }

    @Override
    protected boolean isUseEventBus() {
        return true;
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onLocationCallback(LocationEvent event) {
        if (locationEntity == null && event.getLocationEntity() != null) {
            page = 1;
            lat = event.getLocationEntity().getLatitude() + "";
            lng = event.getLocationEntity().getLongitude() + "";
            presenter.getCommunityList(page, pageSize, lat, lng, true, false, false);
        }
        this.locationEntity = event.getLocationEntity();
    }

    private void toggleEditVisibility(int status) {
        if (status == View.GONE) {
            if (vInput.getVisibility() == View.GONE) return;
            etInput.setText("");
            KeyboardUtil.hideSoftInput(getActivity());
        } else if (status == View.VISIBLE) {
            if (vInput.getVisibility() == View.VISIBLE) return;
            etInput.requestFocus();
            KeyboardUtil.showSoftInput(getActivity());
        }
        vInput.setVisibility(status);
    }


    // v_input
    // type: 0,直接评论 ，1：回复具体评论
    private void showInputDialogWithComment(CommunityInfoModel model, int index, int type, CommunityInfoModel.CommentInfoModel data) {
        if (vInput.getVisibility() == View.VISIBLE) return;
        String hint = "";
        if (type == 1) {
            hint = "回复 " + data.getUserName();
        }
        etInput.setHint(hint);
        toggleEditVisibility(View.VISIBLE);
        send.setOnClickListener(v -> {
            String content = etInput.getText().toString();
            if (content.isEmpty()) {
                Toaster.show("请输入评论内容");
                return;
            }
//            showProgressDialog(R.string.app_downloadding);
            presenter.reply(content, model, index, type, data);
        });
    }

    @SuppressLint("NotifyDataSetChanged")
    @Override
    public void getListSucceed(List<CommunityInfoModel> list, boolean load, boolean refresh, boolean loadmore) {

        if (load) {
            this.list.clear();
            this.list.addAll(list);
        } else if (refresh) {
            page = 1;
            this.list.clear();
            this.list.addAll(list);
            refreshLayout.finishRefresh();
        } else if (loadmore) {
            this.list.addAll(list);
            refreshLayout.finishLoadMore();
        }
        if (list.size() >= pageSize) {
            page++;
            refreshLayout.setEnableLoadMore(true);
        } else {
            refreshLayout.setEnableLoadMore(false);
        }

        adapter.notifyDataSetChanged();
    }

    @Override
    public void getListFailed(String msg, boolean load, boolean refresh, boolean loadmore) {
        Toaster.show(msg);
        if (refresh) {
            refreshLayout.finishRefresh();
        } else if (loadmore) {
            refreshLayout.finishLoadMore();
        }
    }

    @Override
    public void getUserCommunitys(List<CommunityInfoModel> data) {

    }

    @Override
    protected CommunityPresenter setPresenter() {
        if (getArguments() != null) {
            type = getArguments().getInt(KEY_TO_COMMUNITY_TYPE);
        }
        CommunityPresenter presenter = new CommunityPresenter(this, this);
        presenter.setType(type);
        return presenter;
    }

    @Override
    public FragmentActivity context() {
        return getActivity();
    }

    @Override
    public void accostSucceed(CommunityInfoModel data, int index) {
        data.setIsAccost("1");
        adapter.notifyItemChanged(index);
        for (CommunityInfoModel item : list) {
            if (item.getUserId().equals(data.getUserId())) {
                item.setIsAccost("1");
            }
        }
        adapter.notifyDataSetChanged();
    }

    @Override
    public void likeSucceed(CommunityInfoModel data, int index) {
        if (TextUtils.equals("1", data.getIsLike())) {
            data.setIsLike("0");
            data.setLikeNum(data.getLikeNum() > 0 ? data.getLikeNum() - 1 : 0);
        } else {
            data.setIsLike("1");
            data.setLikeNum(data.getLikeNum() + 1);
        }
        adapter.notifyItemChanged(index);
    }

    @Override
    public void replySucceed(CommunityInfoModel model, int index, CommunityInfoModel.CommentInfoModel data) {
        presenter.update(model, model.getId(), index);
        toggleEditVisibility(View.GONE);
//        dismissProgressDialog();
    }

    @Override
    public void updateSucceed(CommunityInfoModel model, int index) {
        if (this.list.size() > index) {
            this.list.set(index, model);
            adapter.notifyItemChanged(index);
        }
    }

    @Override
    public void failed() {
        dismissProgressDialog();
    }

    @Override
    public void deleteCallback(BaseBean callbackBean) {

    }
}
