package com.chat.laty.fragment;

import android.content.Context;
import android.net.Uri;
import android.os.Handler;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;

import androidx.annotation.NonNull;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.bitmap.CircleCrop;
import com.bumptech.glide.request.RequestOptions;
import com.chat.laty.R;
import com.chat.laty.activity.UserCenterUI;
import com.chat.laty.base.BaseFragment;
import com.chat.laty.base.XYApplication;
import com.chat.laty.contants.Constant;
import com.chat.laty.entity.RyUserInfo;
import com.chat.laty.utils.FromJsonUtils;
import com.chat.laty.utils.LogUtil;
import com.chat.laty.utils.ResultData;
import com.chat.laty.utils.ResultData2;
import com.chat.laty.utils.XYSPUtils;
import com.chat.laty.utils.net.Common;
import com.chat.laty.utils.net.HttpUtils;
import com.chat.laty.utils.net.TextCallBack;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.util.HashMap;
import java.util.List;

import io.rong.imkit.GlideKitImageEngine;
import io.rong.imkit.IMCenter;
import io.rong.imkit.RongIM;
import io.rong.imkit.config.ConversationListBehaviorListener;
import io.rong.imkit.config.RongConfigCenter;
import io.rong.imkit.conversationlist.ConversationListFragment;
import io.rong.imkit.conversationlist.model.BaseUiConversation;
import io.rong.imkit.userinfo.RongUserInfoManager;
import io.rong.imlib.IRongCoreCallback;
import io.rong.imlib.IRongCoreEnum;
import io.rong.imlib.RongCoreClient;
import io.rong.imlib.RongIMClient;
import io.rong.imlib.model.Conversation;
import io.rong.imlib.model.UserInfo;

/**
 * <AUTHOR>
 * @date 2023/12/12 17:55
 * @description:消息列表_融云
 */
public class XinyouConversationListFragment extends BaseFragment {

    @Override
    protected int getContentViewId() {
        return R.layout.fragment_chatlist_activity;
    }

    @Override
    public void initViews(View rootView) {
        RongConfigCenter.conversationListConfig().setCountPerPage(10);
        RongConfigCenter.featureConfig().setKitImageEngine(new GlideKitImageEngine() {
            @Override
            public void loadConversationListPortrait(@NonNull Context context, @NonNull String url, @NonNull ImageView imageView, Conversation conversation) {
                Glide.with(context).load(url)
                        .apply(RequestOptions.bitmapTransform(new CircleCrop()))
                        .into(imageView);
            }
        });

        getChildFragmentManager().beginTransaction()
                .replace(R.id.fragment_container, new ConversationListFragment())
                .commit();


        RongIM.setConversationListBehaviorListener(new ConversationListBehaviorListener() {
            @Override
            public boolean onConversationPortraitClick(Context context, Conversation.ConversationType conversationType, String targetId) {
                if (XYApplication.getCurrentUserStatus()) {
                    UserCenterUI.start(getActivity(), targetId);
                    return true;
                }
                return false;
            }

            @Override
            public boolean onConversationPortraitLongClick(Context context, Conversation.ConversationType conversationType, String targetId) {
                return false;
            }

            @Override
            public boolean onConversationLongClick(Context context, View view, BaseUiConversation conversation) {
                return false;
            }

            @Override
            public boolean onConversationClick(Context context, View view, BaseUiConversation conversation) {
                if (XYApplication.getCurrentUserStatus()) {
                    return false;
                }
                return true;
            }
        });
    }

    @Override
    public void onResume() {
        super.onResume();
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                RongCoreClient.getInstance().getConversationList(new IRongCoreCallback.ResultCallback<List<Conversation>>() {
                    @Override
                    public void onSuccess(List<Conversation> conversations) {
                        if (!XYSPUtils.getBoolean(Constant.KEY_LOAD_MSG_LIST)) {
                            XYSPUtils.put(Constant.KEY_LOAD_MSG_LIST, true);
                            for (Conversation conversation : conversations) {
                                getChatUserInfo(conversation.getTargetId());
                            }
                        }
                        String uids = "";
                        for (Conversation conversation : conversations) {
                            uids += conversation.getTargetId() + ",";
                        }
                        if(!TextUtils.isEmpty(uids)){
                            getfhUser(uids);
                        }
                    }

                    @Override
                    public void onError(IRongCoreEnum.CoreErrorCode e) {

                    }
                }, Conversation.ConversationType.PRIVATE);
            }
        }, 3000);
    }

    private void getChatUserInfo(String userId) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("userId", userId);
        HttpUtils.get(Common.APP_GET_CHAT_USERINFO, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                ResultData<RyUserInfo> mData = FromJsonUtils.fromJson(text, RyUserInfo.class);
                if (200 == mData.getCode()) {
                    RyUserInfo xyUserInfo = mData.getData();
                    XYApplication.getDaoInstant().getRyUserInfoDao().insertOrReplace(xyUserInfo);
//                        RongIM.getInstance().refreshUserInfoCache(new UserInfo(xyUserInfo.getUserId(), xyUserInfo.getNickname(), Uri.parse(xyUserInfo.getAvatar())));
                    RongUserInfoManager.getInstance().refreshUserInfoCache(new UserInfo(xyUserInfo.getUserId(), (TextUtils.equals("null", xyUserInfo.getNotes()) || TextUtils.isEmpty(xyUserInfo.getNotes())) ? xyUserInfo.getNickname() : xyUserInfo.getNotes(), Uri.parse(xyUserInfo.getAvatar())));


//                    runOnUiThread(new Runnable() {
//                        @Override
//                        public void run() {
//
//                            if (null != xyUserInfo) {
////                                XYApplication.getDaoInstant().getRyUserInfoDao().insertOrReplace(xyUserInfo);
////                                RongUserInfoManager.getInstance().refreshUserInfoCache(new UserInfo(xyUserInfo.getUserId(), (TextUtils.equals("null", xyUserInfo.getNotes()) || TextUtils.isEmpty(xyUserInfo.getNotes())) ? xyUserInfo.getNickname() : xyUserInfo.getNotes(), Uri.parse(TextUtils.isEmpty(xyUserInfo.getAvatar()) ? xyUserInfo.getAvatar() : "https://xinyou-dev.oss-cn-shenzhen.aliyuncs.com/man_default.png")));
//                                RongIM.getInstance().refreshUserInfoCache(new UserInfo(xyUserInfo.getUserId(), xyUserInfo.getNickname(), Uri.parse(xyUserInfo.getAvatar())));
//                            }
//                        }
//                    });
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
            }
        });
    }

    private void getfhUser(String uids) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("userIds", uids);
        HttpUtils.get(Common.GET_USER_FH, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                ResultData2<List<String>> mData = new Gson().fromJson(text, new TypeToken<ResultData2<List<String>>>(){}.getType());
                if (200 == mData.getCode()) {
                    List<String> list = mData.getData();
                    if (list != null && !list.isEmpty()) {
                        for (String uid : list) {
                            IMCenter.getInstance().deleteMessages(Conversation.ConversationType.PRIVATE, uid, new RongIMClient.ResultCallback<Boolean>() {
                                @Override
                                public void onSuccess(Boolean aBoolean) {

                                }

                                @Override
                                public void onError(RongIMClient.ErrorCode e) {

                                }
                            });
                        }
                    }
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
            }
        });
    }
}
