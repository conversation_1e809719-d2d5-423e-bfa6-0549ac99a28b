package com.chat.laty.fragment;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.header.ClassicsHeader;
import com.chat.laty.R;
import com.chat.laty.activity.MyMoreGroupActivity;
import com.chat.laty.adapter.MyGroupMoreAdapter;
import com.chat.laty.controllers.MyGroupController;
import com.chat.laty.dialog.BottomSignWheelViewDialog;
import com.chat.laty.entity.MyGroupInfoModel;
import com.chat.laty.entity.MyGroupListInfoModel;
import com.chat.laty.entity.PickerBean;
import com.chat.laty.mvp.BasePresenterFragment;
import com.chat.laty.presenters.MyGroupPresenter;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;

/**
 * <AUTHOR>
 * @date 2023/11/21 11:20
 * @description:好友
 */
public class MyGroupFragment extends BasePresenterFragment<MyGroupPresenter> implements MyGroupController.IGroupListView, MyGroupController.ISetLevelView {

    private static final String KEY_TO_COMMUNITY_TYPE = "_KEY_TO_COMMUNITY_TYPE_";
    private static final String KEY_TO_COMMUNITY_DATA = "_KEY_TO_COMMUNITY_DATA_";

    int type = 1;

    @BindView(R.id.tv_value_1)
    TextView tvValue1;
    @BindView(R.id.tv_value_2)
    TextView tvValue2;

    @BindView(R.id.tv_value_1_2)
    TextView tvValue12;
    @BindView(R.id.tv_value_2_2)
    TextView tvValue22;

    @BindView(R.id.smart_refresh_layout)
    SmartRefreshLayout refreshLayout;
    @BindView(R.id.recycler_view)
    RecyclerView recyclerView;

    MyGroupMoreAdapter adapter;

    int page = 1;

    int pageSize = 15;

    MyGroupInfoModel data;

    String date = null;

    List<MyGroupListInfoModel> list = new ArrayList<>();

    public static MyGroupFragment newInstance(int type, MyGroupInfoModel data) {
        Bundle args = new Bundle();
        MyGroupFragment fragment = new MyGroupFragment();
        args.putInt(KEY_TO_COMMUNITY_TYPE, type);
        args.putParcelable(KEY_TO_COMMUNITY_DATA, data);
        fragment.setArguments(args);
        return fragment;
    }

    @SuppressLint("SetTextI18n")
    public void setData(MyGroupInfoModel data, String date) {
        if (data == null) return;
        this.data = data;
        this.date = date;
        if (type == 1) {
            if (tvValue1 != null) {
                tvValue1.setText(data.getTotalSocializeNum() + "");
                tvValue12.setText(data.getAlltotalSocializeNum() + "");
            }
            if (tvValue2 != null) {
                tvValue2.setText(data.getTotalRechargeNum() + "");
                tvValue22.setText(data.getTotalRechargeNum() + "");
            }
        } else {
            if (tvValue1 != null) {
                tvValue1.setText(data.getTotalIndirectSocialNum() + "");
                tvValue12.setText(data.getAlltotalIndirectSocialNum() + "");
            }
            if (tvValue2 != null) {
                tvValue2.setText(data.getTotalIndirectRechargeNum() + "");
                tvValue22.setText(data.getAlltotalIndirectRechargeNum() + "");
            }
        }

        if (refreshLayout != null) {
            refreshLayout.autoRefresh();
        }
    }

    @Override
    protected int getContentViewId() {
        return R.layout.fragment_my_group_layout;
    }

    @SuppressLint({"ClickableViewAccessibility", "SetTextI18n"})
    @Override
    public void initViews(View rootView) {
        adapter = new MyGroupMoreAdapter(type, data.getDbnLevel(), data.getUserId());

        if (type == 1) {
            tvValue1.setText(data.getTotalSocializeNum() + "");
            tvValue2.setText(data.getTotalRechargeNum() + "");
            tvValue12.setText(data.getAlltotalSocializeNum() + "");
            tvValue22.setText(data.getAlltotalRechargeNum() + "");
        } else {
            tvValue1.setText(data.getTotalIndirectSocialNum() + "");
            tvValue2.setText(data.getTotalIndirectRechargeNum() + "");
            tvValue12.setText(data.getAlltotalIndirectSocialNum() + "");
            tvValue22.setText(data.getAlltotalIndirectRechargeNum() + "");
        }

        recyclerView.setAdapter(adapter);
//        recyclerView.addItemDecoration(new RecyclerItemDecoration());

        adapter.setItemAnimation(BaseQuickAdapter.AnimationType.SlideInRight);
        adapter.setStateView(getLayoutInflater().inflate(R.layout.community_empty_layout, null));
        adapter.setStateViewEnable(true);

        ClassicsHeader classicsHeader = new ClassicsHeader(context());
        classicsHeader.setProgressDrawable(ContextCompat.getDrawable(context(), R.drawable.pull_refresh_view_refresh_custom));
        classicsHeader.setArrowDrawable(ContextCompat.getDrawable(context(), R.drawable.pull_refresh_view_refresh_custom));
        classicsHeader.setEnableLastTime(false);
        refreshLayout.setRefreshHeader(classicsHeader);

        refreshLayout.setOnRefreshListener(refreshLayout -> {
//            FragmentActivity activity = getActivity();
//            if (activity instanceof MyGroupActivity) {
//                ((MyGroupActivity) activity).update(null);
//            }
            if (date != null) {
                presenter.getMyGroupList("", type, data.getUserId(), date, 1, pageSize, false, true, false);
            } else {
                presenter.getMyGroupList("", type, data.getUserId(), 1, pageSize, false, true, false);
            }

        });


        refreshLayout.setOnLoadMoreListener(refreshLayout -> {
            if (date != null) {
                presenter.getMyGroupList("", type, data.getUserId(), date, page, pageSize, false, false, true);
            } else {
                presenter.getMyGroupList("", type, data.getUserId(), page, pageSize, false, false, true);
            }

        });

        // item click
        adapter.setOnItemClickListener((baseQuickAdapter, view, i) -> {
            MyGroupListInfoModel item = baseQuickAdapter.getItem(i);
//            if (TextUtils.equals("1", item.getIsLogOff())) return;
            if (TextUtils.equals(data.getUserId(), item.getUserId())) return;
            MyMoreGroupActivity.startAct(getActivity(), type, item);
        });

        // 设置级别
        adapter.addOnItemChildClickListener(R.id.tv_set_level, (baseQuickAdapter, view, i) -> {

            MyGroupListInfoModel infoModel = list.get(i);
            if (TextUtils.equals("1", infoModel.getIsLogOff())) return;
            if (TextUtils.equals("1", infoModel.getIsSettingsLevel())) return;


            BottomSignWheelViewDialog dialog = new BottomSignWheelViewDialog(getContext());

            int userLevel = data.getDbnLevel();
            int dbnLevel = infoModel.getDbnLevel();

            List<PickerBean> list = adapter.getLevelList(userLevel, dbnLevel);


            if (list.isEmpty()) return;

            dialog.setOnDialogCallbackListener(info -> {
                showProgressDialog(R.string.app_loadding);
                presenter.setLevelInfo(infoModel.getUserId(), info.getKey(), i);
            });
            dialog.show();
            dialog.setNewDatas(list);

        });


        if (date != null) {
            presenter.getMyGroupList("", type, data.getUserId(), date, 1, pageSize, true, true, false);
        } else {
            presenter.getMyGroupList("", type, data.getUserId(), page, pageSize, true, true, false);
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    @Override
    public void getGroupListSucceed(List<MyGroupListInfoModel> list, boolean load, boolean refresh, boolean loadmore) {
//        if (list.isEmpty()) {
//            MyGroupListInfoModel model = new MyGroupListInfoModel();
//            model.setAvatar("");
//            model.setNickname("NikeName");
//            model.setDbnLevel(20);
//            model.setDirectNum(34);
//            model.setUserId("4834925");
//            model.setRechargeNum(45);
//            model.setRegisterTime("2023-03-45");
//            model.setSocializeNum(54);
//            list.add(model);
//        }
        if (load) {
            this.list = list;
            adapter.setStateViewEnable(true);
        } else if (refresh) {
            page = 1;
            this.list = list;
            refreshLayout.finishRefresh();
        } else if (loadmore) {
            this.list.addAll(list);
            refreshLayout.finishLoadMore();
        }
        if (list.size() >= pageSize) {
            page++;
            refreshLayout.setEnableLoadMore(true);
        } else {
            refreshLayout.setEnableLoadMore(false);
        }

        adapter.setItems(this.list);
        adapter.notifyDataSetChanged();
    }

    @Override
    public void getGroupListFailed(boolean load, boolean refresh, boolean loadmore) {
        if (refresh) {
            refreshLayout.finishRefresh();
        } else if (loadmore) {
            refreshLayout.finishLoadMore();
        }
    }

    @Override
    protected MyGroupPresenter setPresenter() {
        if (getArguments() != null) {
            type = getArguments().getInt(KEY_TO_COMMUNITY_TYPE);
            data = getArguments().getParcelable(KEY_TO_COMMUNITY_DATA);
        }
        MyGroupPresenter presenter = new MyGroupPresenter(null, this);
        presenter.setType(type);
        presenter.setSetLevelView(this);
        return presenter;
    }

    @Override
    public FragmentActivity context() {
        return getActivity();
    }

    @Override
    public void setLevelSucceed(int index, int level) {
        MyGroupListInfoModel item = adapter.getItem(index);
        if (item != null) {
            item.setDbnLevel(level);
            item.setIsSettingsLevel("1");
            adapter.notifyItemChanged(index);
        }
        dismissProgressDialog();
    }

    @Override
    public void setLevelFailed() {
        dismissProgressDialog();
    }
}
