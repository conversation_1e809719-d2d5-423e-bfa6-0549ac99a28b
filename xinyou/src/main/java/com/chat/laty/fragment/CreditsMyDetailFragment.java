package com.chat.laty.fragment;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.header.ClassicsHeader;
import com.chat.laty.R;
import com.chat.laty.controllers.CreditsController;
import com.chat.laty.entity.MyCreditsDetailModel;
import com.chat.laty.mvp.BasePresenterFragment;
import com.chat.laty.presenters.MyCreditsPresenter;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;

/**
 * <AUTHOR>
 * @date 2023/11/21 11:20
 * @description:砖石
 */
public class CreditsMyDetailFragment extends BasePresenterFragment<MyCreditsPresenter> implements CreditsController.IMyDetailView {
    @BindView(R.id.smart_refresh_layout)

    SmartRefreshLayout refreshLayout;
    @BindView(R.id.recycler_view)
    RecyclerView recyclerView;
    private static final String KEY_ARGS = "args";

    public static CreditsMyDetailFragment newInstance(int type) {
        Bundle args = new Bundle();
        CreditsMyDetailFragment fragment = new CreditsMyDetailFragment();
        args.putInt(KEY_ARGS, convert(type));
        fragment.setArguments(args);
        return fragment;
    }

    private static int convert(int type) {
        switch (type) {
            case 1:
                return 3;
            case 2:
                return 4;
            case 3:
                return 1;
            case 4:
                return 2;
            default:
                return 5;
        }
    }

    int page = 1;
    int pageSize = 20;
    int type = 0;
    CreditsDetailAdapter adapter;

    List<MyCreditsDetailModel> list = new ArrayList<>();

    @Override
    protected int getContentViewId() {
        return R.layout.fragment_gold_coin_detail_layout;
    }

    @Override
    public void initViews(View rootView) {

        ClassicsHeader classicsHeader = new ClassicsHeader(context());
        classicsHeader.setProgressDrawable(ContextCompat.getDrawable(context(), R.drawable.pull_refresh_view_refresh_custom));
        classicsHeader.setArrowDrawable(ContextCompat.getDrawable(context(), R.drawable.pull_refresh_view_refresh_custom));
        classicsHeader.setEnableLastTime(false);
        refreshLayout.setRefreshHeader(classicsHeader);

        refreshLayout.setOnRefreshListener(refreshLayout -> presenter.getMyCreditsDetail(1, pageSize, false, true, false));
        refreshLayout.setOnLoadMoreListener(refreshLayout -> presenter.getMyCreditsDetail(page, pageSize, false, false, true));

        adapter = new CreditsDetailAdapter(type);
        adapter.setItems(list);
        recyclerView.setAdapter(adapter);
        adapter.setStateView(getLayoutInflater().inflate(R.layout.community_empty_layout, null));
        adapter.setStateViewEnable(true);

        presenter.getMyCreditsDetail(page, pageSize, true, false, false);
    }


    @Override
    public FragmentActivity context() {
        return getActivity();
    }


    @SuppressLint("NotifyDataSetChanged")
    @Override
    public void getDetailSucceed(List<MyCreditsDetailModel> list, boolean load, boolean refresh, boolean loadmore) {

        if (load) {
            this.list.clear();
            this.list.addAll(list);
        } else if (refresh) {
            page = 1;
            this.list.clear();
            this.list.addAll(list);
            refreshLayout.finishRefresh();
        } else if (loadmore) {
            this.list.addAll(list);
            refreshLayout.finishLoadMore();
        }
        if (list.size() >= pageSize) {
            page++;
            refreshLayout.setEnableLoadMore(true);
        } else {
            refreshLayout.setEnableLoadMore(false);
        }

        adapter.notifyDataSetChanged();
    }

    @Override
    public void getDetailFailed(boolean load, boolean refresh, boolean loadmore) {
        if (refresh) {
            refreshLayout.finishRefresh();
        } else {
            refreshLayout.finishLoadMore();
        }
    }

    @Override
    protected MyCreditsPresenter setPresenter() {
        if (getArguments() != null) {
            type = getArguments().getInt(KEY_ARGS);
        }
        MyCreditsPresenter presenter = new MyCreditsPresenter(null, this);
        presenter.setType(type);
        return presenter;
    }


    static class CreditsDetailAdapter extends BaseQuickAdapter<MyCreditsDetailModel, QuickViewHolder> {

        private int type;

        public CreditsDetailAdapter(int type) {
            this.type = type;
        }

        @Override
        protected void onBindViewHolder(@NonNull QuickViewHolder helper, int i, @Nullable MyCreditsDetailModel item) {
            helper.setText(R.id.tv_time, item.getCreateTime());
            helper.setText(R.id.tv_value, (type == 5 ? "-" : "+") + item.getChangeIntegral() + "积分");
            helper.setText(R.id.tv_balance, "余额：" + item.getLastIntegral() + "积分");
            helper.setText(R.id.tv_detail, item.getChangeDetail());
        }

        @NonNull
        @Override
        protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int i) {
            return new QuickViewHolder(R.layout.adapter_credits_detail_layout, viewGroup);
        }
    }
}
