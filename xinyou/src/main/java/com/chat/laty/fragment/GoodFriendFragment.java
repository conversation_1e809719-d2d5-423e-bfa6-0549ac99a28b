package com.chat.laty.fragment;

import android.os.Bundle;
import android.view.View;
import android.widget.RelativeLayout;

import androidx.annotation.NonNull;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.RecyclerView;

import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshLoadMoreListener;
import com.chat.laty.R;
import com.chat.laty.activity.UserCenterUI;
import com.chat.laty.adapter.FriendAdapter;
import com.chat.laty.base.XYApplication;
import com.chat.laty.controllers.FriendController;
import com.chat.laty.dialog.BottomChoiceGoodFriendDialog;
import com.chat.laty.entity.FriendInfo;
import com.chat.laty.mvp.BasePresenterFragment;
import com.chat.laty.presenters.FriendPresenter;
import com.chat.laty.view.RecyclerItemDecoration;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;
import cn.xjc_soft.lib_utils.LibCollections;
import io.rong.imkit.config.RongConfigCenter;
import io.rong.imkit.utils.RouteUtils;
import io.rong.imlib.model.Conversation;

/**
 * <AUTHOR>
 * @date 2023/11/21 11:20
 * @description:好友
 */
public class GoodFriendFragment extends BasePresenterFragment<FriendPresenter> implements FriendController.View {

    private static final String KEY_TO_FRIEND_TYPE = "_KEY_TO_FRIEND_TYPE_";

    @BindView(R.id.fg_srl)
    SmartRefreshLayout mSmartRefreshLayout;

    @BindView(R.id.fragment_rv)
    RecyclerView mGoodFriendRv;
    @BindView(R.id.qunfa_rl)
    RelativeLayout mQunSendMsgRl;

    FriendAdapter mAdapter;

    List<FriendInfo> mDatas = new ArrayList<>();

    FriendInfo mCurrentFriend;

    public static GoodFriendFragment newInstance(int friendType) {
        Bundle args = new Bundle();
        GoodFriendFragment fragment = new GoodFriendFragment();
        args.putInt(KEY_TO_FRIEND_TYPE, friendType);
        fragment.setArguments(args);
        return fragment;
    }

    private int getFriendsType() {
        return null == getArguments() ? 0 : getArguments().getInt(KEY_TO_FRIEND_TYPE);
    }

    @Override
    protected int getContentViewId() {
        return R.layout.fragment_xy_message_rv_layout;
    }

    @Override
    public void initViews(View rootView) {
        mAdapter = new FriendAdapter(getFriendsType());
        mGoodFriendRv.setAdapter(mAdapter);
        mAdapter.setStateViewEnable(true);
        mGoodFriendRv.addItemDecoration(new RecyclerItemDecoration());

        mQunSendMsgRl.setVisibility((1 == getFriendsType()) ? View.VISIBLE : View.GONE);

        mAdapter.addOnItemChildClickListener(R.id.follow_tv, (adapter, view, position) -> {
            if (XYApplication.appLogin(true)) {
                mCurrentFriend = (FriendInfo) adapter.getItem(position);
                presenter.followUser(mCurrentFriend.getUserId(), "1".equals(mCurrentFriend.getIsFollow()));
            }
        });

        mAdapter.addOnItemChildClickListener(R.id.item_layout, (adapter, view, position) -> {
            if (XYApplication.appLogin(true)) {
                FriendInfo currentFriend = (FriendInfo) adapter.getItem(position);
                if (1 == getFriendsType() || 2 == getFriendsType()) {
                    // 默认拉取历史消息数量
                    RongConfigCenter.conversationConfig().setConversationHistoryMessageCount(10);
                    RouteUtils.routeToConversationActivity(getActivity(), Conversation.ConversationType.PRIVATE, currentFriend.getUserId(), null);
                } else if (3 == getFriendsType() || 4 == getFriendsType()) {
                    UserCenterUI.start(getActivity(), currentFriend.getUserId());
                }
            }
        });

        mAdapter.addOnItemChildClickListener(R.id.cancle_follow_tv, (adapter, view, position) -> {
            if (XYApplication.appLogin(true)) {
                mCurrentFriend = (FriendInfo) adapter.getItem(position);
                presenter.cancleFollowUser(mCurrentFriend.getUserId());
            }
        });

        mSmartRefreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                PAGE_INDEX++;
                presenter.getFriendsByType(getFriendsType(), PAGE_INDEX);
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                PAGE_INDEX = MIN_PAGE_INDEX_1;
                presenter.getFriendsByType(getFriendsType(), PAGE_INDEX);
            }
        });
        mSmartRefreshLayout.autoRefresh();
    }

    @OnClick({R.id.qunfa_rl})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.qunfa_rl:
                BottomChoiceGoodFriendDialog dialog = new BottomChoiceGoodFriendDialog(getActivity(),mDatas);
                dialog.create();
                dialog.show();
                break;
        }
    }

    @Override
    protected FriendPresenter setPresenter() {
        return new FriendPresenter(this);
    }

    @Override
    public FragmentActivity context() {
        return getActivity();
    }

    @Override
    public void showFriends(List<FriendInfo> friends) {
        if (LibCollections.isEmpty(friends) || friends.size() < PAGE_SIZE) {
            mSmartRefreshLayout.setEnableLoadMore(false);
        }

        if (PAGE_INDEX == MIN_PAGE_INDEX_1) {
            mDatas = friends;
            mSmartRefreshLayout.finishRefresh();
            mSmartRefreshLayout.setEnableLoadMore(true);
        } else {
            mDatas.addAll(friends);
            mSmartRefreshLayout.finishLoadMore();
        }
        mAdapter.setStateView(createEmptyListView());
        mAdapter.setItems(mDatas);
        mAdapter.notifyDataSetChanged();
    }

    @Override
    public void showFollwoCallback() {
        String tempFollow = mCurrentFriend.getIsFollow();
        mCurrentFriend.setIsFollow("1".equals(tempFollow) ? "0" : "1");
        mAdapter.notifyDataSetChanged();
    }

    @Override
    public void showCancleFollwoCallback() {
        mAdapter.remove(mCurrentFriend);
    }
}
