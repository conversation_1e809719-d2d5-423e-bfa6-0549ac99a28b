package com.chat.laty.fragment;

import android.graphics.Color;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.LinearLayout;

import androidx.appcompat.widget.AppCompatTextView;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;
import androidx.viewpager.widget.ViewPager;

import com.chat.laty.activity.WebViewActivity;
import com.chat.laty.entity.BannerInfo;
import com.chat.laty.view.BannerImageLoader;
import com.google.android.material.tabs.TabLayout;

import android.view.LayoutInflater;
import android.widget.TextView;

import com.hjq.toast.Toaster;
import com.chat.laty.R;
import com.chat.laty.activity.CallPhoneActivity;
import com.chat.laty.activity.DisturbSettingsUI;
import com.chat.laty.activity.LikeMsgActivity;
import com.chat.laty.activity.ReportMsgActivity;
import com.chat.laty.activity.SystemMsgActivity;
import com.chat.laty.activity.VisitorMeUI;
import com.chat.laty.base.XYApplication;
import com.chat.laty.controllers.MessagePageController;
import com.chat.laty.dialog.BottomMessageOptDialog;
import com.chat.laty.dialog.TopReceiveCallDialog;
import com.chat.laty.entity.MessageUnReadInfo;
import com.chat.laty.entity.RyUserInfo;

import com.chat.laty.entity.XinYouCallInfo;
import com.chat.laty.entity.event.ClearMsgEvent;
import com.chat.laty.entity.event.MainTabChange;
import com.chat.laty.entity.event.RCCallPlusSessionEvent;
import com.chat.laty.entity.event.RCHangUpSessionEvent;
import com.chat.laty.entity.event.RCReceiveCallPlusSessionEvent;
import com.chat.laty.entity.event.RYCallConnectedEvent;
import com.chat.laty.im.message.XYCallVideoContent;
import com.chat.laty.mvp.BasePresenterFragment;
import com.chat.laty.presenters.MessagePagePresenter;
import com.chat.laty.utils.SystemRoundUtils;
import com.youth.banner.Banner;
import com.youth.banner.indicator.CircleIndicator;
import com.youth.banner.listener.OnBannerListener;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;
import cn.rongcloud.callplus.api.RCCallPlusClient;
import cn.rongcloud.callplus.api.RCCallPlusSession;
import io.rong.imkit.IMCenter;
import io.rong.imkit.RongIM;
import io.rong.imlib.IRongCallback;
import io.rong.imlib.IRongCoreCallback;
import io.rong.imlib.IRongCoreEnum;
import io.rong.imlib.RongCoreClient;
import io.rong.imlib.RongIMClient;
import io.rong.imlib.model.Conversation;
import io.rong.imlib.model.Message;
import q.rorbin.badgeview.QBadgeView;

/**
 * <AUTHOR>
 * @date 2023/9/14 9:44
 * @description:
 */
public class MessageFragment extends BasePresenterFragment<MessagePagePresenter> implements MessagePageController.View, OnBannerListener {
    @BindView(R.id.tab_layout)
    TabLayout mTabLayout;

    @BindView(R.id.viewpager)
    ViewPager mViewPager;

    @BindView(R.id.system_tv)
    AppCompatTextView mSystemCardView;
    @BindView(R.id.like_tv)
    AppCompatTextView mLikeCardView;
    @BindView(R.id.visitorView)
    AppCompatTextView mVisitorView;
    @BindView(R.id.reportView)
    AppCompatTextView mReportView;
    @BindView(R.id.reportView_layout)
    LinearLayout mReportLl;

    @BindView(R.id.fl_dz)
    FrameLayout fl_dz;
    @BindView(R.id.fl_fk)
    FrameLayout fl_fk;
    @BindView(R.id.fl_xt)
    FrameLayout fl_xt;
    @BindView(R.id.fl_jb)
    FrameLayout fl_jb;

    @BindView(R.id.banner)
    Banner mBannerView;


    RCCallPlusSession mReceiveRCCallPlusSession;

    ArrayList<String> mDatas = new ArrayList<>(Arrays.asList("消息", "密友", "好友", "关注", "粉丝"));

    private String[] mTitles;


    private ArrayList<Fragment> mFragments = new ArrayList<>();


    QBadgeView mSystemBadgeView;
    QBadgeView mLikeBadgeView;
    QBadgeView mVisitorBadgeView;
    QBadgeView mReportBadgeView;

    BottomMessageOptDialog mBottomChoiceDialog;

    RyUserInfo mUserInfo;

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void changeTab(MainTabChange event) {
        mViewPager.setCurrentItem(event.getTabPosition() + 1, true);
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onCallConnected(RYCallConnectedEvent sessionEvent) {
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onReceivedCall(RCReceiveCallPlusSessionEvent sessionEvent) {
        RCCallPlusSession callSession = sessionEvent.getSession();
//        showReceiveDialog(callSession);
//        XYVideoUtils.callStartTime = System.currentTimeMillis();
//        LogUtil.i(TAG, "onCallConnected，，，，");
//        XYVideoUtils.session = callSession;

    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onCallEnded(RCHangUpSessionEvent sessionEvent) {
        RCCallPlusSession session = sessionEvent.getSession();
//        if(XYVideoUtils.session != null && XYVideoUtils.session.getCallId() == session.getCallId()) {
//            XYVideoUtils.callStartTime = -1;
//            XYVideoUtils.session = null;
//        }
    }

    TopReceiveCallDialog mDialog;

    protected void showReceiveDialog(RCCallPlusSession callSession) {
        mReceiveRCCallPlusSession = callSession;
        presenter.getUserInfo(callSession.getCallerUserId());
        if (null == mDialog)
            mDialog = new TopReceiveCallDialog(getContext());
        mDialog.setOnDialogCallbackListener(new TopReceiveCallDialog.OnDialogCallbackListener() {
            @Override
            public void onDialogSelectItem(int type) {
                SystemRoundUtils.cancelVibrator();
                SystemRoundUtils.stopRing();
                if (1 == type) {
                    EventBus.getDefault().postSticky(new RCCallPlusSessionEvent(callSession));
                    CallPhoneActivity.start(getContext(), callSession.getCallId(), 1, callSession.getMediaType().getValue(), "");
                } else if (2 == type) {
                    sendVideoMsg(new XinYouCallInfo("已拒绝", "0", "", callSession.getCallerUserId()));
                    RCCallPlusClient.getInstance().hangup(callSession.getCallId());
                }
                mDialog.dismiss();
            }
        });

        mDialog.show();
    }

    public void sendVideoMsg(XinYouCallInfo callInfo) {
        Conversation.ConversationType conversationType = Conversation.ConversationType.PRIVATE;
        XYCallVideoContent messages = XYCallVideoContent.obtain(callInfo);
        Message message = Message.obtain(mUserInfo.getId(), conversationType, messages);
        message.setCanIncludeExpansion(true);
        RongIM.getInstance().sendMessage(message, null, null, new IRongCallback.ISendMediaMessageCallback() {
            @Override
            public void onProgress(Message message, int i) {

            }

            @Override
            public void onCanceled(Message message) {

            }

            @Override
            public void onAttached(Message message) {

            }

            @Override
            public void onSuccess(Message message) {

            }

            @Override
            public void onError(final Message message, final RongIMClient.ErrorCode errorCode) {

            }
        });
    }


    @Override
    protected int getContentViewId() {
        return R.layout.fragment_message_layout;
    }

    @Override
    public void initViews(View rootView) {
        mTitles = new String[mDatas.size()];
        mUserInfo = XYApplication.getCurrentUserInfo();
        for (int i = 0; i < mDatas.size(); i++) {
            mTitles[i] = mDatas.get(i);
            if (0 == i) {
                mFragments.add(new XinyouConversationListFragment());
            } else {
                mFragments.add(GoodFriendFragment.newInstance(i));
            }

        }

        MessagePageAdapter mAdapter = new MessagePageAdapter(getChildFragmentManager(), mFragments);
        mViewPager.setAdapter(mAdapter);

        // 设置TabLayout和ViewPager的关联
        for (int i = 0; i < mTitles.length; i++) {
            TabLayout.Tab tab = mTabLayout.newTab();
            View view = LayoutInflater.from(getContext()).inflate(R.layout.tab_layout_item_view, null);
            TextView textView = view.findViewById(R.id.tv_title);
            textView.setText(mTitles[i]);
            tab.setCustomView(view);
            mTabLayout.addTab(tab);
        }

        mTabLayout.addOnTabSelectedListener(new TabLayout.OnTabSelectedListener() {
            @Override
            public void onTabSelected(TabLayout.Tab tab) {
                toggleTextStyle(tab.getCustomView(), true);
                mViewPager.setCurrentItem(tab.getPosition());
            }

            @Override
            public void onTabUnselected(TabLayout.Tab tab) {
                toggleTextStyle(tab.getCustomView(), false);
            }

            @Override
            public void onTabReselected(TabLayout.Tab tab) {

            }
        });

        // 设置默认选中第一个Tab
        if (mTabLayout.getTabCount() > 0) {
            TabLayout.Tab firstTab = mTabLayout.getTabAt(0);
            if (firstTab != null) {
                toggleTextStyle(firstTab.getCustomView(), true);
            }
        }

        mViewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

            }

            @Override
            public void onPageSelected(int position) {
                TabLayout.Tab tab = mTabLayout.getTabAt(position);
                if (tab != null) {
                    mTabLayout.selectTab(tab);
                }
            }

            @Override
            public void onPageScrollStateChanged(int state) {

            }
        });

        if (null != mUserInfo) {
            if (TextUtils.equals("1", mUserInfo.getSex())) {
                mReportLl.setVisibility(View.GONE);
            } else {
                mReportLl.setVisibility(View.VISIBLE);
            }
        } else {
            mReportLl.setVisibility(View.GONE);
        }

        initBadgeView();
//        RYCallPlusManager.getInstance().init();
    }

    private void clearAllConversations() {

        Conversation.ConversationType[] mConversationTypes = {
                Conversation.ConversationType.PRIVATE,
                Conversation.ConversationType.GROUP
        };

        IMCenter.getInstance().clearConversations(new RongIMClient.ResultCallback() {
            @Override
            public void onSuccess(Object o) {

            }

            @Override
            public void onError(RongIMClient.ErrorCode e) {

            }
        }, mConversationTypes);
    }

    private void clearUnReadMsg() {
        Conversation.ConversationType[] conversationTypes = {Conversation.ConversationType.PRIVATE, Conversation.ConversationType.GROUP, Conversation.ConversationType.SYSTEM};
        RongCoreClient.getInstance().getUnreadConversationList(new IRongCoreCallback.ResultCallback<List<Conversation>>() {

            @Override
            public void onSuccess(List<Conversation> conversations) {
                // 成功并返回会话信息
                for (Conversation conversation : conversations) {
                    clearUnReadMsgCount(conversation);
                }
            }

            @Override
            public void onError(IRongCoreEnum.CoreErrorCode e) {

            }
        }, conversationTypes);
    }

    private void clearUnReadMsgCount(Conversation conversation) {

        IMCenter.getInstance().clearMessagesUnreadStatus(conversation.getConversationType(), conversation.getTargetId(), new RongIMClient.ResultCallback<Boolean>() {
            @Override
            public void onSuccess(Boolean aBoolean) {

            }

            @Override
            public void onError(RongIMClient.ErrorCode e) {

            }
        });
    }

    private void initBadgeView() {

        mSystemBadgeView = new QBadgeView(getContext());
        mSystemBadgeView.setBadgeGravity(Gravity.END | Gravity.BOTTOM);
//        mSystemBadgeView.setGravityOffset(55, 0, true);
//        mSystemBadgeView.stroke(requireActivity().getColor(R.color.white), 1, true);
        mSystemBadgeView.bindTarget(fl_xt);
//        mSystemBadgeView.setBadgeNumber(3);
        mSystemBadgeView.setBadgeBackgroundColor(Color.parseColor("#E24B5A"));
        mSystemBadgeView.setShowShadow(false);


        mLikeBadgeView = new QBadgeView(getContext());
        mLikeBadgeView.setBadgeGravity(Gravity.END | Gravity.BOTTOM);
//        mLikeBadgeView.setGravityOffset(55, 0, true);
//        mLikeBadgeView.stroke(requireActivity().getColor(R.color.white), 1, true);
        mLikeBadgeView.bindTarget(fl_dz);
        mLikeBadgeView.setShowShadow(false);
//        mLikeBadgeView.setBadgeNumber(20);
        mLikeBadgeView.setBadgeBackgroundColor(Color.parseColor("#E24B5A"));

        mVisitorBadgeView = new QBadgeView(getContext());
        mVisitorBadgeView.setBadgeGravity(Gravity.END | Gravity.BOTTOM);
//        mVisitorBadgeView.setGravityOffset(55, 0, true);
//        mVisitorBadgeView.stroke(requireActivity().getColor(R.color.white), 1, true);
        mVisitorBadgeView.setShowShadow(false);
        mVisitorBadgeView.bindTarget(fl_fk);
        mVisitorBadgeView.setBadgeBackgroundColor(Color.parseColor("#E24B5A"));

        mReportBadgeView = new QBadgeView(getContext());
        mReportBadgeView.setBadgeGravity(Gravity.END | Gravity.BOTTOM);
//        mReportBadgeView.setGravityOffset(55, 0, true);
//        mReportBadgeView.stroke(requireActivity().getColor(R.color.white), 1, true);
        mReportBadgeView.setShowShadow(false);
        mReportBadgeView.bindTarget(fl_jb);
        mReportBadgeView.setBadgeBackgroundColor(Color.parseColor("#E24B5A"));
//        mVisitorBadgeView.setBadgeNumber(121);

        if (null != mUserInfo && TextUtils.equals("1", mUserInfo.getSex())) {
            mReportLl.setVisibility(View.GONE);
        } else {
            mReportLl.setVisibility(View.VISIBLE);
        }
    }

    private void setBadgeView(MessageUnReadInfo msgCount) {
        mSystemBadgeView.setBadgeNumber(msgCount.getSysMessageNum());
        mLikeBadgeView.setBadgeNumber(msgCount.getLikeNum());
        mVisitorBadgeView.setBadgeNumber(msgCount.getVisitorNum());
        mReportBadgeView.setBadgeNumber(msgCount.getReportNum());
    }

    @Override
    protected MessagePagePresenter setPresenter() {
        return new MessagePagePresenter(this);
    }

    @Override
    public FragmentActivity context() {
        return getActivity();
    }

    @Override
    public void showMsgCount(MessageUnReadInfo msgInfo) {
        setBadgeView(msgInfo);
    }

    @Override
    public void showUserInfo(RyUserInfo data) {
        if (mReceiveRCCallPlusSession != null) {
            mDialog.setUserInfo(data, mReceiveRCCallPlusSession.getMediaType());
        }
    }

    @Override
    public void showBanners(List<BannerInfo> data) {
        mBannerView.setAdapter(new BannerImageLoader(data));
        if (getActivity() == null) {
            return;
        }
        CircleIndicator circleIndicator = new CircleIndicator(getActivity());
        mBannerView.setIndicator(circleIndicator);
        mBannerView.setOnBannerListener(this);
        mBannerView.start();
    }

    @Override
    public void OnBannerClick(Object data, int position) {
        BannerInfo info = (BannerInfo) data;
        if (null != info && null != info.getLinkUrl()) {
            WebViewActivity.startActivity(getActivity(), "附近聊爱探约", info.getLinkUrl());
        }
    }


    private class MessagePageAdapter extends FragmentPagerAdapter {
        ArrayList<Fragment> mFragments;

        public MessagePageAdapter(FragmentManager fm, ArrayList<Fragment> fragments) {
            super(fm);
            mFragments = fragments;
        }

        @Override
        public int getCount() {
            return mFragments.size();
        }

        @Override
        public CharSequence getPageTitle(int position) {
            return mDatas.get(position);
        }

        @Override
        public Fragment getItem(int position) {
            return mFragments.get(position);
        }
    }


    @OnClick({R.id.message_setting, R.id.fl_xt, R.id.fl_dz, R.id.fl_fk, R.id.fl_jb})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.message_setting:
                if (XYApplication.appLogin(true))
                    showMsgOptDialog();
                break;

            case R.id.fl_xt:
                if (XYApplication.appLogin(true))
                    SystemMsgActivity.start(getActivity());
                break;

            case R.id.fl_dz:
                if (XYApplication.appLogin(true))
                    LikeMsgActivity.start(getActivity());
                break;

            case R.id.fl_fk:
                if (XYApplication.appLogin(true)) {
                    RyUserInfo userInfo = XYApplication.getCurrentUserInfo();
                    if (TextUtils.equals("1", userInfo.getSex())) {
                        if (userInfo.getVipLevel() != 0)
                            VisitorMeUI.start(getActivity());
                        else
                            Toaster.show("您不是会员，不能查看访客");
                    } else {
                        VisitorMeUI.start(getActivity());
                    }
                }
                break;

            case R.id.fl_jb:
                if (XYApplication.appLogin(true))
                    ReportMsgActivity.start(getActivity());
                break;
        }
    }


    private void showMsgOptDialog() {

        if (null == mBottomChoiceDialog) {
            mBottomChoiceDialog = new BottomMessageOptDialog(getActivity());
        }
        mBottomChoiceDialog.setOnDialogCallbackListener(new BottomMessageOptDialog.OnDialogCallbackListener() {
            @Override
            public void onDialogSelectItem(int type) {
                optMessage(type);
            }
        });
        mBottomChoiceDialog.show();
    }

    private void optMessage(int type) {
        switch (type) {
            case 1:
                DisturbSettingsUI.start(getActivity());
                break;
            case 2:
                clearUnReadMsg();
                break;
            case 3:
                EventBus.getDefault().post(new ClearMsgEvent());
                clearUnReadMsg();
                clearAllConversations();
                break;
        }
    }

    @Override
    protected void onVisibleChanged(boolean isVisible) {
        super.onVisibleChanged(isVisible);
        if (isVisible) {
            presenter.getMsgCount();
        }
    }

    private void getUserInfo() {
        if (XYApplication.appLogin(false)) {
            presenter.getMsgCount();
        }
    }

    @Override
    protected boolean isUseEventBus() {
        return true;
    }

    @Override
    public void onResume() {
        super.onResume();
        getUserInfo();
        presenter.getBanners();
    }

    private void toggleTextStyle(View customView, boolean isSelected) {
        if (customView != null) {
            TextView textView = customView.findViewById(R.id.tv_title);
            if (textView != null) {
                textView.setSelected(isSelected);
            }
        }
    }
}
