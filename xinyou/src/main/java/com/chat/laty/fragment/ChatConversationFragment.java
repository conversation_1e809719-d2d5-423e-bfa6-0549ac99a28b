package com.chat.laty.fragment;

import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.hjq.toast.Toaster;
import com.makeramen.roundedimageview.RoundedImageView;
import com.chat.laty.R;
import com.chat.laty.activity.UserCenterUI;
import com.chat.laty.adapter.CommonPhrasesAdapter;
import com.chat.laty.adapter.UserChatIconAdapter;
import com.chat.laty.base.XYApplication;
import com.chat.laty.entity.BlockMessageInfo;
import com.chat.laty.entity.CommonPhrasesInfo;
import com.chat.laty.entity.XYUserInfo;
import com.chat.laty.im.message.XYGoldCoinsTextContent;
import com.chat.laty.utils.BgmPlayer;
import com.chat.laty.utils.LogUtil;
import com.chat.laty.utils.PicassoUtils;
import com.chat.laty.utils.net.GsonUtils;

import java.util.Arrays;
import java.util.List;

import io.rong.imkit.IMCenter;
import io.rong.imkit.RongIM;
import io.rong.imkit.conversation.ConversationFragment;
import io.rong.imlib.IRongCallback;
import io.rong.imlib.IRongCoreCallback;
import io.rong.imlib.IRongCoreEnum;
import io.rong.imlib.IRongCoreListener;
import io.rong.imlib.RongCoreClient;
import io.rong.imlib.RongIMClient;
import io.rong.imlib.listener.OnReceiveMessageWrapperListener;
import io.rong.imlib.model.BlockedMessageInfo;
import io.rong.imlib.model.Conversation;
import io.rong.imlib.model.Message;
import io.rong.imlib.model.ReceivedProfile;
import io.rong.message.TextMessage;


/**
 * <AUTHOR>
 * @date 2023/12/1 13:50
 * @description:
 */
public class ChatConversationFragment extends ConversationFragment {

    RoundedImageView mUserIconRiv;
    TextView mUserNameTv;
    AppCompatTextView mUserVoiceTv;

    TextView userIsrealTv;
    TextView userIsnameTv;
    TextView userIsphoneTv;
    TextView userInfoTv;
    TextView userMakeFriendTv;
    TextView userAccostPlayTv;

    TextView tvOnline;
    TextView tvCity;
    TextView tvJob;
    TextView tvDt;
    View line1;
    View ll_dt;


    LinearLayout mIsNameLl;

    RecyclerView userIconRv;

    BgmPlayer mBgmPlayer;


    RecyclerView mCommonPhrasesRv;

    CommonPhrasesAdapter mCommonPhrasesAdapter;

    XYUserInfo mUserInfo;

    View mTopView;


    public void setUser(XYUserInfo userInfo) {
        mUserInfo = userInfo;
        PicassoUtils.showImage(mUserIconRiv, userInfo.getAvatar());
        mUserNameTv.setText(TextUtils.isEmpty(userInfo.getNotes()) ? userInfo.getNickname() : userInfo.getNotes());
        userIsrealTv.setVisibility((!TextUtils.isEmpty(userInfo.getIsReal()) && "1".equals(userInfo.getIsReal())) ? View.VISIBLE : View.GONE);
        userIsnameTv.setVisibility((!TextUtils.isEmpty(userInfo.getIsName()) && "1".equals(userInfo.getIsName())) ? View.VISIBLE : View.GONE);

        tvCity.setText(userInfo.getLiveAddress());
//                tvJob.setText(userInfo.getLiveAddress());

        line1.setVisibility((!TextUtils.isEmpty(userInfo.getOnlineStatus()) && "1".equals(userInfo.getOnlineStatus())) ? View.VISIBLE : View.GONE);
        tvOnline.setVisibility((!TextUtils.isEmpty(userInfo.getOnlineStatus()) && "1".equals(userInfo.getOnlineStatus())) ? View.VISIBLE : View.GONE);

        if (!TextUtils.isEmpty(userInfo.getIsPhone()) && TextUtils.equals("1", userInfo.getSex())) {
            userIsphoneTv.setVisibility(View.VISIBLE);
            if ("1".equals(userInfo.getIsPhone())) {
                userIsphoneTv.setText("手机已认证");
            } else if ("0".equals(userInfo.getIsPhone())) {
                userIsphoneTv.setText("微信已认证");
            }
        } else {
            userIsphoneTv.setVisibility(View.GONE);
        }


        StringBuilder stringBuilder = new StringBuilder(userInfo.getAge() + "岁");
        if (!TextUtils.isEmpty(userInfo.getHeight()))
            stringBuilder.append("·" + userInfo.getHeight());
        if (!TextUtils.isEmpty(userInfo.getCareerName()))
            stringBuilder.append("·" + userInfo.getCareerName());


        if (TextUtils.isEmpty(userInfo.getMakeFriendsDetail())) {
            userMakeFriendTv.setVisibility(View.GONE);
        } else {
            userMakeFriendTv.setVisibility(View.VISIBLE);
            userMakeFriendTv.setText(userInfo.getMakeFriendsDetail());
        }

        if (TextUtils.isEmpty(userInfo.getAudioUrl())) {
            userAccostPlayTv.setVisibility(View.GONE);
        } else {
            userAccostPlayTv.setVisibility(View.VISIBLE);
            userAccostPlayTv.setText(userInfo.getAudioLength() + "s");
        }

        userAccostPlayTv.setOnClickListener(v -> {
            if (mBgmPlayer == null)
                mBgmPlayer = BgmPlayer.getInstance(getActivity());

            if (mBgmPlayer.isPlay()) {
                mBgmPlayer.stop();
            }
            mBgmPlayer.playNet(userInfo.getAudioUrl(), mUserVoiceTv);
        });

        if (!TextUtils.isEmpty(userInfo.getPhotoList())) {
            UserChatIconAdapter iconAdapter = new UserChatIconAdapter();
            userIconRv.setAdapter(iconAdapter);
            iconAdapter.setItems(Arrays.asList(userInfo.getPhotoList().split(",")));
            userIconRv.setVisibility(View.VISIBLE);
        } else {
            userIconRv.setVisibility(View.GONE);
        }

    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        mTopView = getLayoutInflater().inflate(R.layout.custom_chat_top_user_card, null);
        mUserIconRiv = mTopView.findViewById(R.id.left_user_riv);
        ll_dt = mTopView.findViewById(R.id.ll_dt);
        mUserNameTv = mTopView.findViewById(R.id.user_name_tv);
        mUserVoiceTv = mTopView.findViewById(R.id.accost_language_play_tv);
        userIsrealTv = mTopView.findViewById(R.id.user_isreal_tv);
        userIsnameTv = mTopView.findViewById(R.id.user_isname_tv);
        userIsphoneTv = mTopView.findViewById(R.id.user_isphone_tv);
        userInfoTv = mTopView.findViewById(R.id.user_info_tv);
        userMakeFriendTv = mTopView.findViewById(R.id.user_friend_tv);
        userAccostPlayTv = mTopView.findViewById(R.id.accost_language_play_tv);
        userIconRv = mTopView.findViewById(R.id.usericon_rv);
        mIsNameLl = mTopView.findViewById(R.id.isname_layout);

        tvOnline = mTopView.findViewById(R.id.tv_online);
        tvCity = mTopView.findViewById(R.id.tv_city);
        tvJob = mTopView.findViewById(R.id.tv_job);
        line1 = mTopView.findViewById(R.id.v_line_1);
        tvDt = mTopView.findViewById(R.id.tv_dt);

        mCommonPhrasesRv = view.findViewById(R.id.common_phrases_rv);

        RongIMClient.getInstance().setMessageBlockListener(blockListener);
//        RongCoreClient.addOnReceiveMessageListener(receiveMsg);


        mCommonPhrasesAdapter = new CommonPhrasesAdapter();
        LinearLayoutManager llm = new LinearLayoutManager(getActivity());
        llm.setOrientation(LinearLayoutManager.HORIZONTAL);
        mCommonPhrasesRv.setLayoutManager(llm);
        mCommonPhrasesRv.setAdapter(mCommonPhrasesAdapter);

//        mAdapter.notifyDataSetChanged();
        mCommonPhrasesAdapter.setOnItemClickListener((adapter, v, position) -> {
            if (XYApplication.appLogin(true)) {
                CommonPhrasesInfo info = (CommonPhrasesInfo) adapter.getItem(position);
                sendMsg(mUserInfo.getUserId(), info.getName());
            }
        });

        mUserIconRiv.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                UserCenterUI.start(getActivity(), mUserInfo.getUserId());
            }
        });
        ll_dt.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                UserCenterUI.start(getActivity(), mUserInfo.getUserId());
            }
        });

        mRongExtensionViewModel.getExtensionBoardState().observe(this.getViewLifecycleOwner(), aBoolean -> {
            if (listener != null) {
                listener.onChange(aBoolean);
            }
        });

        mList.scrollToPosition(mAdapter.getItemCount() - 1);
    }

    public interface OnExtensionBoardListener {
        void onChange(boolean isShow);
    }

    private OnExtensionBoardListener listener;

    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        try {
            listener = (OnExtensionBoardListener) context;
        } catch (Exception e) {

        }
    }

    @Override
    public void onDetach() {
        super.onDetach();
        if (listener != null) listener = null;
    }

    IRongCoreListener.MessageBlockListener blockListener = new IRongCoreListener.MessageBlockListener() {
        @Override
        public void onMessageBlock(BlockedMessageInfo info) {
            LogUtil.e("BlockedMessageInfo", "MessageInfoExtra--->" + info);
            if (TextUtils.isEmpty(info.getExtra())) {
                Toaster.show("违规消息,发送失败");
                RongCoreClient.getInstance().getMessageByUid(info.getBlockMsgUId(), new IRongCoreCallback.ResultCallback<Message>() {
                    @Override
                    public void onSuccess(Message message) {
                        message.setSentStatus(Message.SentStatus.FAILED);
                        RongIM.getInstance().setMessageSentStatus(message, new RongIMClient.ResultCallback<Boolean>() {
                            @Override
                            public void onSuccess(Boolean aBoolean) {
                            }

                            @Override
                            public void onError(RongIMClient.ErrorCode e) {

                            }
                        });
                    }

                    @Override
                    public void onError(IRongCoreEnum.CoreErrorCode e) {

                    }
                });
            } else {
                BlockMessageInfo msgInfo = GsonUtils.JsonToBean(info.getExtra(), BlockMessageInfo.class);
                switchTipInfo(msgInfo, info.getTargetId());
            }
        }
    };

    private void switchTipInfo(BlockMessageInfo msgInfo, String userId) {
        if (null != msgInfo) {
            Toaster.show(msgInfo.getMessage());
            switch (msgInfo.getType()) {
                case "1":
                    sendEmptyGoldMsg(userId);
                    break;

                case "2":
                    break;
            }
        }

    }

    OnReceiveMessageWrapperListener receiveMsg = new OnReceiveMessageWrapperListener() {
        @Override
        public void onReceivedMessage(Message message, ReceivedProfile profile) {
            LogUtil.e("响应结果", "==========ChatConversationFragment-->onWrapperdMessage:" + message.getExtra());
            //  针对接收离线消息时，服务端会将 200 条消息打成一个包发到客户端，客户端对这包数据进行解析。该参数表示每个数据包数据逐条上抛后，还剩余的条数
            int left = profile.getLeft();
            // 消息是否离线消息
            boolean isOffline = profile.isOffline();
            // 是否在服务端还存在未下发的消息包
            boolean hasPackage = profile.hasPackage();
        }
    };


    private void sendEmptyGoldMsg(String userId) {
        Conversation.ConversationType conversationType = Conversation.ConversationType.PRIVATE;
        XYGoldCoinsTextContent messages = XYGoldCoinsTextContent.obtain("温馨提示:金币不足");
        Message message = Message.obtain(userId, conversationType, messages);
        message.setSentStatus(Message.SentStatus.SENT);
        long sentTime = System.currentTimeMillis();

        IMCenter.getInstance().insertOutgoingMessage(conversationType, userId, Message.SentStatus.SENT, messages, new RongIMClient.ResultCallback<Message>() {
            @Override
            public void onSuccess(Message message) {

            }

            @Override
            public void onError(RongIMClient.ErrorCode e) {

            }
        });
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (null != mBgmPlayer)
            mBgmPlayer.release();
    }

    private void sendMsg(String userId, String content) {
        Conversation.ConversationType conversationType = Conversation.ConversationType.PRIVATE;
        TextMessage messages = TextMessage.obtain(content);
        Message message = Message.obtain(userId, conversationType, messages);
        message.setCanIncludeExpansion(true);
        IMCenter.getInstance().sendMessage(message, null, null, new IRongCallback.ISendMediaMessageCallback() {
            @Override
            public void onProgress(Message message, int i) {

            }

            @Override
            public void onCanceled(Message message) {

            }

            @Override
            public void onAttached(Message message) {

            }

            @Override
            public void onSuccess(Message message) {
                Toaster.show("常用语发送成功!");
            }

            @Override
            public void onError(final Message message, final RongIMClient.ErrorCode errorCode) {

            }
        });
    }

    public void setCommonPhrases(List<CommonPhrasesInfo> data) {
        mCommonPhrasesAdapter.setItems(data);
        mCommonPhrasesAdapter.notifyDataSetChanged();
        addHeaderView(mTopView);
    }
}