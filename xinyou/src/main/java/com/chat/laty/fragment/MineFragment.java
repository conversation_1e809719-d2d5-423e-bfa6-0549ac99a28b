package com.chat.laty.fragment;

import android.content.ComponentName;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.appcompat.widget.AppCompatTextView;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.chat.laty.activity.InviteActivity1;
import com.gongwen.marqueen.SimpleMF;
import com.gongwen.marqueen.SimpleMarqueeView;
import com.hjq.toast.Toaster;
import com.makeramen.roundedimageview.RoundedImageView;
import com.chat.laty.R;
import com.chat.laty.activity.DischargedPicBrowserActivity;
import com.chat.laty.activity.GoldCoinActivity;
import com.chat.laty.activity.ImproveInformationUI;
import com.chat.laty.activity.InviteActivity;
import com.chat.laty.activity.MyAuthenticationUI;
import com.chat.laty.activity.MyCreditsActivity;
import com.chat.laty.activity.MyGroupActivity;
import com.chat.laty.activity.MyRewardsActivity;
import com.chat.laty.activity.SettingActivity;
import com.chat.laty.activity.SiMiPhotoActivity;
import com.chat.laty.activity.SiMiVideoActivity;
import com.chat.laty.activity.TaskCenterActivity;
import com.chat.laty.activity.UserCenterUI;
import com.chat.laty.activity.VipCenter1Activity;
import com.chat.laty.activity.VisitorMeUI;
import com.chat.laty.activity.WebViewActivity;
import com.chat.laty.adapter.MineAdapter;
import com.chat.laty.adapter.OptionAdapter;
import com.chat.laty.base.AppHelper;
import com.chat.laty.base.XYApplication;
import com.chat.laty.controllers.MinePageController;
import com.chat.laty.dialog.BottomChannelRvDialog;
import com.chat.laty.entity.BalanceInfo;
import com.chat.laty.entity.ChannelInfo;
import com.chat.laty.entity.DiaplayPicInfo;
import com.chat.laty.entity.OptionItem;
import com.chat.laty.entity.PickerViewBean;
import com.chat.laty.entity.RYTokenInfo;
import com.chat.laty.entity.RyUserInfo;
import com.chat.laty.entity.UserCenterInfo;
import com.chat.laty.entity.WebBeanInfo;
import com.chat.laty.entity.WeiChatPayInfo;
import com.chat.laty.entity.event.MainTabChange;
import com.chat.laty.mvp.BasePresenterFragment;
import com.chat.laty.presenters.MinePagePresenter;
import com.chat.laty.presenters.PayManager;
import com.chat.laty.utils.ClipboardHelper;
import com.chat.laty.utils.PicassoUtils;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;
import io.rong.imkit.RongIM;
import io.rong.imkit.userinfo.RongUserInfoManager;
import io.rong.imlib.model.UserInfo;

/**
 * <AUTHOR>
 * @date 2023/9/14 9:44
 * @description:
 */
public class MineFragment extends BasePresenterFragment<MinePagePresenter> implements MinePageController.View {

    @BindView(R.id.user_head_riv)
    RoundedImageView mUserHeadRiv;
    @BindView(R.id.iv_txk)
    ImageView iv_txk;

    @BindView(R.id.user_name_tv)
    AppCompatTextView mUserNameTv;

    @BindView(R.id.mine_rv)
    RecyclerView mRecommendRv;

    @BindView(R.id.option_rv)
    RecyclerView mOptionRv;

    MineAdapter mineAdapter;
    OptionAdapter optionAdapter;

    @BindView(R.id.user_id_tv)
    AppCompatTextView mUserIdTv;

    @BindView(R.id.user_isreal_tv)
    AppCompatTextView mIsRealTv;

    @BindView(R.id.user_isname_tv)
    AppCompatTextView mIsNameTv;

    @BindView(R.id.user_isphone_tv)
    AppCompatTextView mIsPhoneTv;
    @BindView(R.id.user_gold_num_tv)
    TextView mUserGoldNumTv;

    @BindView(R.id.marquee_view)
    SimpleMarqueeView mSimpleMarqueeView;

    @BindView(R.id.my_like_num_tv)
    TextView mMyLikeNumTv;

    @BindView(R.id.lookme_num_tv)
    AppCompatTextView mLookMeNumTv;

    @BindView(R.id.visitor_me_layout)
    View mVisitorMeLl;

    @BindView(R.id.vip_tv)
    ImageView mVipTv;
    @BindView(R.id.vip_icon)
    AppCompatTextView mVipIconTv;
    @BindView(R.id.open_vip)
    AppCompatTextView mVipOpenTv;

    @BindView(R.id.v_mine_zuanshi)
    LinearLayout v_mine_zuanshi;//我的奖励

    @BindView(R.id.v_mine_credits1)
    View v_mine_credits1;//我的奖励

    @BindView(R.id.tv_credits1)
    TextView tvCredits1;
    @BindView(R.id.tv_credits)
    TextView tvCredits;
    @BindView(R.id.balance_tv)
    AppCompatTextView balanceTv;
    @BindView(R.id.user_age_tv)
    AppCompatTextView userAgeTv;

    UserCenterInfo mUserInfo;

    BottomChannelRvDialog mBottomDialog;

    List<ChannelInfo> mChannels = new ArrayList<>();

    List<String> mDatas = new ArrayList<>();

    List<OptionItem> mOptionDatas = new ArrayList<>();

    // 新老邀请页切换
    Boolean isNewYao = false;

//    QBadgeView mNewBadgeView;


    @Override
    protected int getContentViewId() {
        return R.layout.fragment_mine_layout;
    }

    @Override
    public void initViews(View rootView) {
        mineAdapter = new MineAdapter();
        mRecommendRv.setAdapter(mineAdapter);
        mineAdapter.setItems(mDatas);

        optionAdapter = new OptionAdapter();
        mOptionRv.setAdapter(optionAdapter);
        mOptionRv.setLayoutManager(new GridLayoutManager(getActivity(), 4));

        optionAdapter.setOnItemClickListener((adapter, view, position) -> {
            OptionItem info = (OptionItem) adapter.getItem(position);
            SwitchToPage(info);
        });

        initOptionViews();
//        mDatas.add("支付");
        mineAdapter.setItems(mDatas);

        mineAdapter.setOnItemClickListener((adapter, view, position) -> {
            String itemInfo = (String) adapter.getItem(position);
            goTo(itemInfo);
        });

//        mNewBadgeView = new QBadgeView(getContext());

        initMarqueeView();

    }

    private void initMarqueeView() {

    }

    private void SwitchToPage(OptionItem info) {
        switch (info.getTitleName()) {
            case "私密视频":
                SiMiVideoActivity.startAct(getActivity());
                break;
            case "私密相册":
                SiMiPhotoActivity.startAct(getActivity());
                break;
            case "通用设置":
                SettingActivity.start(getActivity());
                break;

            case "我的认证":
                MyAuthenticationUI.start(getActivity());
                break;

            case "邀请赚钱":
                if (isNewYao) {
                    InviteActivity.startAct(getActivity());
                } else {
                    InviteActivity1.startAct(getActivity());
                }
                break;

            case "任务中心":
                TaskCenterActivity.startAct(getActivity());
                break;

            case "我的团队":
                MyGroupActivity.startAct(getActivity());
                break;

            case "常见问题":
                presenter.getAccordByNum(3);
                break;
            case "联系客服":
                presenter.getKefuUrl(mUserInfo.getUserId(), mUserInfo.getNickname());
//                WebViewActivity.startActivity(getActivity(), "联系客服", Common.kefuUrl + "&userId=" + mUserInfo.getUserId() + "&userName=" + mUserInfo.getNickname());
                break;
        }
    }

    private void initOptionViews() {
        mOptionDatas.clear();
        if (!XYApplication.getCurrentIsMan()) {
            mOptionDatas.add(new OptionItem(R.mipmap.icon_simi, "私密相册"));
            mOptionDatas.add(new OptionItem(R.mipmap.icon_simi_video, "私密视频"));
            mOptionDatas.add(new OptionItem(R.mipmap.team, "我的团队"));
//            mOptionDatas.add(new OptionItem(R.mipmap.zhuanqian, "邀请赚钱"));
            v_mine_zuanshi.setVisibility(View.VISIBLE);
            v_mine_credits1.setVisibility(View.GONE);
        }
        mOptionDatas.add(new OptionItem(R.mipmap.renzheng120, "我的认证"));
        mOptionDatas.add(new OptionItem(R.mipmap.icon_renwuzhongxin, "任务中心"));
        mOptionDatas.add(new OptionItem(R.mipmap.shuoming, "常见问题"));
        mOptionDatas.add(new OptionItem(R.mipmap.kefu, "联系客服"));
//        mOptionDatas.add(new OptionItem(R.mipmap.yijian, "意见反馈"));
//        mOptionDatas.add(new OptionItem(R.mipmap.shezhi, "通用设置"));
        optionAdapter.setItems(mOptionDatas);
    }

    private void goTo(String itemInfo) {
        switch (itemInfo) {
            case "去登录":
                break;
            case "微信授权登录":
                PayManager.wxLogin(getActivity());
                break;
            case "支付":
                showChoiceChannel();
                break;
            case "动态":
                EventBus.getDefault().post(new MainTabChange(1));
                break;
            case "消息":
                EventBus.getDefault().post(new MainTabChange(2));
                break;
            case "首页":
                EventBus.getDefault().post(new MainTabChange(0));
                break;
        }
    }

    private void showChoiceChannel() {
        mChannels.clear();
        mChannels.add(new ChannelInfo("支付宝", R.mipmap.icon_wechat));
        mChannels.add(new ChannelInfo("微信", R.mipmap.icon_wechat));
        if (null == mBottomDialog) {
            mBottomDialog = new BottomChannelRvDialog(getActivity());
        }

        mBottomDialog.setTitleTv("渠道选择");
        mBottomDialog.setOnDialogCallbackListener(new BottomChannelRvDialog.OnDialogCallbackListener() {
            @Override
            public void onChoiceCallback(ChannelInfo channelInfo) {
                switchChannel(channelInfo);
            }
        });

        mBottomDialog.show();
        mBottomDialog.setNewDatas(mChannels);
    }

    private void switchChannel(ChannelInfo channelInfo) {
        switch (channelInfo.getTitle()) {
            case "微信":
                presenter.getWXPayParam();
                break;
            case "支付宝":
                presenter.getPayParm();
                break;
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        getUserInfo();
    }

    @Override
    protected void onVisibleChanged(boolean isVisible) {
        super.onVisibleChanged(isVisible);
        if (isVisible)
            getUserInfo();
    }

    private void getUserInfo() {
        if (XYApplication.appLogin(false)) {
            presenter.getUserCenterInfo();
            presenter.getViolationList();
        }
    }

    private void showUserInfo(UserCenterInfo userInfo) {
        if (null != userInfo) {
            mUserInfo = userInfo;
            RongUserInfoManager.getInstance().refreshUserInfoCache(new UserInfo(userInfo.getUserId(), userInfo.getNickname(), Uri.parse(TextUtils.isEmpty(userInfo.getAvatar()) ? "https://xinyou-dev.oss-cn-shenzhen.aliyuncs.com/man_default.png" : userInfo.getAvatar())));

            PicassoUtils.showImage(mUserHeadRiv, userInfo.getAvatar());
            mUserNameTv.setText(userInfo.getNickname());
            mUserGoldNumTv.setText(String.valueOf(userInfo.getGoldNum()));
            String integralBalanceStr = userInfo.getIntegralBalance();
            if (!TextUtils.isEmpty(integralBalanceStr)) {
                try {
                    long integralBalance = Long.parseLong(integralBalanceStr);
                    tvCredits.setText(formatNumberToWan(integralBalance));
                    tvCredits1.setText(formatNumberToWan(integralBalance));
                } catch (NumberFormatException e) {
                    tvCredits.setText("0");
                    tvCredits1.setText("0");
                }
            } else {
                tvCredits.setText("0");
                tvCredits1.setText("0");
            }

           /* String diamondBalanceStr = userInfo.getDiamondBalance() != null ? userInfo.getDiamondBalance().toString() : "0";
            try {
                long diamondBalance = Long.parseLong(diamondBalanceStr);
                balanceTv.setText(formatNumberToWan(diamondBalance));
            } catch (NumberFormatException e) {
                balanceTv.setText("0");
            }*/

            balanceTv.setText(userInfo.getDiamondBalance());

            mUserIdTv.setText("ID" + userInfo.getUserId());
            mMyLikeNumTv.setText(userInfo.getMyLoveNum() + "");
            mLookMeNumTv.setText(userInfo.getWhoSeeMeNum() + "");

            mIsRealTv.setVisibility((!TextUtils.isEmpty(userInfo.getIsReal()) && "1".equals(userInfo.getIsReal())) ? View.VISIBLE : View.GONE);
            mIsNameTv.setVisibility((!TextUtils.isEmpty(userInfo.getIsName()) && "1".equals(userInfo.getIsName())) ? View.VISIBLE : View.GONE);
            mIsPhoneTv.setVisibility((!TextUtils.isEmpty(userInfo.getIsPhone()) && "1".equals(userInfo.getIsPhone())) ? View.VISIBLE : View.GONE);

            Drawable dwLeft = getResources().getDrawable(TextUtils.equals("1", userInfo.getSex()) ? R.mipmap.icon_nan_select : R.mipmap.icon_nv_select);
            dwLeft.setBounds(0, 0, dwLeft.getMinimumWidth(), dwLeft.getMinimumHeight());
            userAgeTv.setBackground(getResources().getDrawable("1".equals(userInfo.getSex()) ? R.drawable.bg_border_5595ff_40 : R.drawable.badge_ff8c8c_to_ff5562_bg_40));
            userAgeTv.setCompoundDrawables(dwLeft, null, null, null);

            if (TextUtils.equals("2", userInfo.getSex())) {
                mIsPhoneTv.setVisibility(View.GONE);
            } else {
                mIsPhoneTv.setVisibility(View.VISIBLE);

                if ("1".equals(userInfo.getIsPhone())) {
                    mIsPhoneTv.setText("手机已认证");
                } else if ("0".equals(userInfo.getIsPhone())) {
                    mIsPhoneTv.setText("微信已认证");
                }
            }

            if (!TextUtils.isEmpty(userInfo.getAvatar()))
                RongIM.getInstance().setCurrentUserInfo(new UserInfo(userInfo.getUserId(), userInfo.getNickname(), Uri.parse(userInfo.getAvatar())));
            if (userInfo.getVipLevel() == 0) {
                mVipIconTv.setText("VIP会员");
                mVipOpenTv.setText("立即开通");

                mVipTv.setVisibility(View.GONE);
                iv_txk.setVisibility(View.GONE);
                mUserNameTv.setTextColor(Color.parseColor("#333333"));

            } else {
                if (userInfo.getVipLevel() == 4) {
                    mVipIconTv.setText("超级会员");
                    mVipOpenTv.setText("立即升级");
                } else {
                    mVipIconTv.setText("超级会员");
                    mVipOpenTv.setText("查看权益");
                }


                mVipTv.setVisibility(View.VISIBLE);
                iv_txk.setVisibility(View.VISIBLE);
                Glide.with(requireContext()).load(XYApplication.vipTypeModelList.get(userInfo.getVipLevel() - 1).getExclusiveIdentifier()).into(mVipTv);
                Glide.with(requireContext()).load(XYApplication.vipTypeModelList.get(userInfo.getVipLevel() - 1).getExclusiveBorder()).into(iv_txk);
//                PicassoUtils.showImage(mVipTv, XYApplication.vipTypeModelList.get(userInfo.getVipLevel()).getExclusiveIdentifier());
//                PicassoUtils.showImage(iv_txk, XYApplication.vipTypeModelList.get(userInfo.getVipLevel()).getExclusiveBorder());
                mUserNameTv.setTextColor(Color.parseColor(XYApplication.vipTypeModelList.get(userInfo.getVipLevel() - 1).getExclusiveNameColor()));
            }
/*            mNewBadgeView.setBadgeGravity(Gravity.CENTER | Gravity.TOP);
            mNewBadgeView.bindTarget(mVisitorMeLl);
            mNewBadgeView.setBadgeNumber(Integer.valueOf(userInfo.getNewSeeMeNum()));
//            mNewBadgeView.setBadgeTextSize(8,true);
            mNewBadgeView.setShowShadow(false);
            mNewBadgeView.setBadgeBackgroundColor(requireActivity().getColor(R.color.colorPrimary));*/

            RyUserInfo userInfo2 = XYApplication.getCurrentUserInfo();
            if (null == userInfo2)
                userInfo2 = new RyUserInfo();
            if (!TextUtils.isEmpty(userInfo.getIsName()))
                userInfo2.setIsName(userInfo.getIsName());
            userInfo2.setSex(userInfo.getSex());
            userInfo2.setIsReal(userInfo.getIsReal());
            userInfo2.setIsPhone(userInfo.getIsPhone());
            userInfo2.setVipLevel(userInfo.getVipLevel());
            userInfo2.setAvatar(userInfo.getAvatar());
            XYApplication.getDaoInstant().getRyUserInfoDao().insertOrReplace(userInfo2);


            //没有一行代码是多余的，只有一行代码是不够的。
            // ——  鲁迅
            // 2025/5/22 00:57
            presenter.getUserBalance();
        }
    }

    @OnClick({R.id.iv_invite, R.id.edit_tv, R.id.user_head_riv, R.id.copy_tv, R.id.v_mine_credits, R.id.v_mine_credits1, R.id.my_rewards_stv, R.id.v_gold_coin, R.id.v_gold_coin2, R.id.user_center_layout, R.id.visitor_me_layout, R.id.vip_layout, R.id.mylove_layout, R.id.call_phone, R.id.linearLayout2, R.id.user_shezhi})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.user_shezhi:
                SettingActivity.start(getActivity());
                break;
            case R.id.iv_invite:
                if (isNewYao) {
                    InviteActivity.startAct(getActivity());
                } else {
                    InviteActivity1.startAct(getActivity());
                }
                break;

            case R.id.user_head_riv:
                if (null != mUserInfo && !TextUtils.isEmpty(mUserInfo.getAvatar())) {
                    ArrayList<DiaplayPicInfo> mDiaplayPicInfos = new ArrayList<>();
                    mDiaplayPicInfos.add(new DiaplayPicInfo(mUserInfo.getAvatar()));
                    DischargedPicBrowserActivity.start(getActivity(), mDiaplayPicInfos, 0);
                }
                break;

            case R.id.vip_layout:
                if (XYApplication.appLogin(true)) {
                    VipCenter1Activity.startAct(getActivity());
                }
                break;

            case R.id.edit_tv:
                if (XYApplication.appLogin(true)) {
                    ImproveInformationUI.start(getActivity());
                }
//                SettingActivity.start(getActivity());
                break;

            case R.id.v_mine_credits:
                if (XYApplication.appLogin(true)) {
                    MyCreditsActivity.startAct(getActivity());
                }
                break;

            case R.id.v_mine_credits1:
                if (XYApplication.appLogin(true)) {
                    MyCreditsActivity.startAct(getActivity());
                }
                break;

            case R.id.my_rewards_stv://我的奖励
                if (XYApplication.appLogin(true)) {
                    MyRewardsActivity.start(getActivity());
                }
                break;

            case R.id.v_gold_coin:
                if (XYApplication.appLogin(true)) {
                    GoldCoinActivity.startAct(getActivity());
                }
                break;

            case R.id.v_gold_coin2:
                if (XYApplication.appLogin(true)) {
                    GoldCoinActivity.startAct(getActivity());
                }
                break;

            case R.id.linearLayout2:
                if (XYApplication.appLogin(true)) {
                    GoldCoinActivity.startAct(getActivity());
                }
                break;

            case R.id.copy_tv:
                if (XYApplication.appLogin(true)) {
                    new ClipboardHelper(getActivity()).copyText(mUserInfo.getUserId());
                    Toaster.show("已成功复制到粘贴板");
                }
                break;

            case R.id.user_center_layout:
                if (XYApplication.appLogin(true)) {
                    UserCenterUI.start(getActivity(), mUserInfo.getUserId());
                }
                break;

            case R.id.visitor_me_layout:
                if (XYApplication.appLogin(true)) {
                    if (TextUtils.equals("1", mUserInfo.getSex())) {
                        if (mUserInfo.getVipLevel() == 0) {
                            Toaster.show("您不是会员,不能查看访客!");
                        } else {
                            VisitorMeUI.start(getActivity());
                        }
                    } else {
                        VisitorMeUI.start(getActivity());
                    }
                }
                break;

            case R.id.mylove_layout:
                if (XYApplication.appLogin(true)) {
                    EventBus.getDefault().post(new MainTabChange(2));
                }
                break;

            case R.id.call_phone:
                if (XYApplication.appLogin(true)) {
                    AppHelper.callPhone(getActivity(), "19163625801");
                }
                break;

//            case R.id.to_authen:
//                RongConfigCenter.featureConfig().enableQuickReply(new IQuickReplyProvider() {
//                    @Override
//                    public List<String> getPhraseList(Conversation.ConversationType type) {
//                        List<String> phraseList = new ArrayList<>();
//                        phraseList.add("您好！");
//                        phraseList.add("您太客气了！");
//                        phraseList.add("您吃饭了吗？");
//                        phraseList.add("您是不是忘记了什么？");
//                        return phraseList;
//                    }
//                });
////                String targetId = "1730141495217930241";//1730141495217930241
////                RouteUtils.routeToConversationActivity(getActivity(), Conversation.ConversationType.PRIVATE, targetId, null);
//                break;
        }
    }


    @Override
    protected MinePagePresenter setPresenter() {
        return new MinePagePresenter(this);
    }

    @Override
    public FragmentActivity context() {
        return getActivity();
    }


    @Override
    public void showAXPayParm(WeiChatPayInfo data) {
        if (null != data) {
            PayManager.weiChatPay(getActivity(), data);
        }
    }

    @Override
    public void showPayParm(RYTokenInfo data) {
        if (null != data && !TextUtils.isEmpty(data.getPayUrl())) {
            PayManager.alipayPay(getActivity(), data.getPayUrl());
        }
    }

    @Override
    public void showDetails(PickerViewBean data) {
//        showUserInfo(data);
    }

    @Override
    public void showUserBalance(BalanceInfo balanceInfo) {
    }

    @Override
    public void showUserCenterDetails(UserCenterInfo data) {
        showUserInfo(data);
    }

    @Override
    public void showWebInfo(WebBeanInfo info) {
        WebViewActivity.startActivity(getActivity(), "", info.getUrl());
    }

    @Override
    public void showViolationList(List<String> result) {
        SimpleMF<String> marqueeFactory = new SimpleMF(getActivity());
        marqueeFactory.setData(result);
        mSimpleMarqueeView.setMarqueeFactory(marqueeFactory);
        mSimpleMarqueeView.startFlipping();
    }

    public void openBrowser(String url) {
        final Intent intent = new Intent();
        intent.setAction(Intent.ACTION_VIEW);
        intent.setData(Uri.parse(url));
        if (intent.resolveActivity(getActivity().getPackageManager()) != null) {
            final ComponentName componentName = intent.resolveActivity(getActivity().getPackageManager());
            getActivity().startActivity(Intent.createChooser(intent, "请选择浏览器"));
        } else {
            startActivity(intent);
//            WebViewActivity.startActivity(getActivity(), "客服", url);
        }
    }

    private String formatNumberToWan(long number) {
        if (number < 0) return "0";

        if (number >= 100_0000_0000L) {
            double value = number / 1_0000_0000.0;
            return (value == (long) value) ? String.format("%d亿", (long) value) : String.format("%.2f亿", value);
        } else if (number >= 10_0000) {
            double value = number / 1_0000.0;
            return (value == (long) value) ? String.format("%d万", (long) value) : String.format("%.2f万", value);
        } else {
            return String.valueOf(number);
        }
    }

}
