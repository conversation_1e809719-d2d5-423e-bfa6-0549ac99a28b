package com.chat.laty.fragment;

import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.viewpager2.adapter.FragmentStateAdapter;
import androidx.viewpager2.widget.ViewPager2;
import com.google.android.material.tabs.TabLayout;
import com.chat.laty.R;
import com.chat.laty.activity.MineDynamicReleaseUI;
import com.chat.laty.activity.UserDynamicsActivity;
import com.chat.laty.activity.WebViewActivity;
import com.chat.laty.base.XYApplication;
import com.chat.laty.controllers.DiscoverController;
import com.chat.laty.entity.AppVersionInfo;
import com.chat.laty.entity.BannerInfo;
import com.chat.laty.entity.OtherUserInfo;
import com.chat.laty.entity.RYTokenInfo;
import com.chat.laty.entity.RecommendationBean;
import com.chat.laty.entity.TodayFateEntity;
import com.chat.laty.entity.WebBeanInfo;
import com.chat.laty.entity.WeiChatPayInfo;
import com.chat.laty.entity.XYUserInfo;
import com.chat.laty.mvp.BasePresenterFragment;
import com.chat.laty.presenters.DiscoverPresenter;
import com.chat.laty.utils.XYSPUtils;
import com.chat.laty.utils.net.Common;
import com.chat.laty.view.BannerImageLoader;
import com.youth.banner.Banner;
import com.youth.banner.indicator.CircleIndicator;
import com.youth.banner.listener.OnBannerListener;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * <AUTHOR>
 * @date 2023/11/21 11:20
 * @description:动态
 */
public class CommunityFragment extends BasePresenterFragment<DiscoverPresenter>implements DiscoverController.View, OnBannerListener {
    @BindView(R.id.tab_layout)
    TabLayout tabLayout;

    @BindView(R.id.view_pager)
    ViewPager2 viewPager2;
    @BindView(R.id.banner)
    Banner mBannerView;

    ArrayList<String> titles = new ArrayList<>(Arrays.asList("全部", "发现", "更多", "关注"));

    private final ArrayList<Fragment> fragments = new ArrayList<>();

    @Override
    protected int getContentViewId() {
        return R.layout.fragment_community_v2_layout;
    }

    @Override
    public void initViews(View rootView) {

        for (int i = 0; i < titles.size(); i++) {
            View view = getLayoutInflater().inflate(R.layout.tab_layout_item_view, null);

            TextView textView = view.findViewById(R.id.tv_title);
//            ImageView imageView = view.findViewById(R.id.iv_indicator);
            if (textView != null) {
                textView.setText(titles.get(i));
                toggleTextStyle(textView, i == 0);
            }
//            if (imageView != null && i == 0) {
//                imageView.setVisibility(View.VISIBLE);
//            }

            TabLayout.Tab tab = tabLayout.newTab();
            tab.setCustomView(view);
            tabLayout.addTab(tab);

//            if (i == 1) {
//                fragments.add(CommunityChildV2Fragment.newInstance(2));
//            } else {
//                fragments.add(CommunityChildFragment.newInstance(i + 1));
//            }

            fragments.add(CommunityChildFragment.newInstance(i + 1));
        }

        CommunityAdapterV2 adapterV2 = new CommunityAdapterV2(this);
        viewPager2.setAdapter(adapterV2);
        viewPager2.setUserInputEnabled(false);
        tabLayout.addOnTabSelectedListener(new TabLayout.OnTabSelectedListener() {
            @Override
            public void onTabSelected(TabLayout.Tab tab) {
                toggleTextStyle(tab.getCustomView(), true);
                viewPager2.setCurrentItem(tab.getPosition());
            }

            @Override
            public void onTabUnselected(TabLayout.Tab tab) {
                toggleTextStyle(tab.getCustomView(), false);
            }

            @Override
            public void onTabReselected(TabLayout.Tab tab) {

            }
        });

//        不支持字体设置
//        new TabLayoutMediator(tabLayout, viewPager2, (tab, position) -> {
////            tab.setText(titles.get(position));
//            TextView textView = (TextView) tab.getCustomView();
//            textView.setSelected(tab.isSelected());
//            if (tab.isSelected()) {
//                textView.setTextSize(sp15);
//            } else {
//                textView.setTextSize(sp13);
//            }
//        }).attach();
    }


    private void toggleTextStyle(View view, boolean selected) {

        TextView textView = view.findViewById(R.id.tv_title);
//        ImageView imageView = view.findViewById(R.id.iv_indicator);

        if (textView != null) {
            textView.setSelected(selected);
            // 移除字体大小设置，使用背景选择器效果
            // textView.setTextSize(selected ? 18 : 15);
        }
//        if (imageView != null) {
//            imageView.setVisibility(selected ? View.VISIBLE : View.INVISIBLE);
//        }
    }
    @Override
    public void onResume() {
        super.onResume();
        presenter.getBanners();

    }
    @Override
    protected void onVisibleChanged(boolean isVisible) {
        super.onVisibleChanged(isVisible);
        if (isVisible) {
            presenter.getBanners();
        }
    }

    @Override
    public void onDestroy() {
        fragments.clear();
        super.onDestroy();
    }

    @OnClick(R.id.tv_my_dynamic)
    public void onClick(View view) {

//        SettingActivity.start(this.getContext());
        UserDynamicsActivity.start(getActivity(), XYSPUtils.getString(Common.KEY_APP_USER_RY_ID));
//        InviteBindSelfActivity.startAct(getContext());
    }

    @OnClick(R.id.tv_release)
    public void release(View view) {
        if (XYApplication.getCurrentUserStatus()) {
            MineDynamicReleaseUI.startAct(this.getContext());
        }
    }

    @Override
    public void OnBannerClick(Object data, int position) {
        BannerInfo info = (BannerInfo) data;
        if (null != info && null != info.getLinkUrl()) {
            WebViewActivity.startActivity(getActivity(), "附近聊爱探约", info.getLinkUrl());
        }
    }

    @Override
    protected DiscoverPresenter setPresenter() {
        return new DiscoverPresenter(this);
    }

    @Override
    public void showPayParm(RYTokenInfo data) {

    }

    @Override
    public void showAXPayParm(WeiChatPayInfo data) {

    }

    @Override
    public void showRecommend(List<OtherUserInfo> data) {

    }

    @Override
    public void showBanners(List<BannerInfo> data) {
        mBannerView.setAdapter(new BannerImageLoader(data));
        if (getActivity() == null) {
            return;
        }
        CircleIndicator circleIndicator = new CircleIndicator(getActivity());
        mBannerView.setIndicator(circleIndicator);
        mBannerView.setOnBannerListener(this);
        mBannerView.start();
    }

    @Override
    public void showAccostToUserSuccess() {

    }

    @Override
    public void showTodayFate(TodayFateEntity todayFate) {

    }

    @Override
    public void showAppVersion(AppVersionInfo data) {

    }

    @Override
    public void showOneClickAccostCallback() {

    }

    @Override
    public void showWebInfo(WebBeanInfo data) {

    }

    @Override
    public void showUserDetails(XYUserInfo userInfo) {

    }

    @Override
    public void showRecomPop(List<RecommendationBean> recommendationBeans) {

    }

    @Override
    public void showRecomPop1() {

    }

    @Override
    public FragmentActivity context() {
        return null;
    }


    private class CommunityAdapterV2 extends FragmentStateAdapter {

        public CommunityAdapterV2(@NonNull Fragment fragment) {
            super(fragment);
        }

        @NonNull
        @Override
        public Fragment createFragment(int position) {
            return fragments.get(position);
        }

        @Override
        public int getItemCount() {
            return fragments.size();
        }
    }
}
