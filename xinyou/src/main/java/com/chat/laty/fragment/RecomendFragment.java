package com.chat.laty.fragment;

import android.os.Bundle;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.hjq.permissions.OnPermissionCallback;
import com.hjq.permissions.Permission;
import com.hjq.permissions.XXPermissions;
import com.hjq.toast.Toaster;
import com.chat.laty.entity.RecommendationBean;
import com.chat.laty.utils.ItemDecoration;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshLoadMoreListener;
import com.chat.laty.R;
import com.chat.laty.activity.UserCenterUI;
import com.chat.laty.adapter.RecommendAdapter;
import com.chat.laty.base.XYApplication;
import com.chat.laty.controllers.DiscoverController;
import com.chat.laty.entity.AppVersionInfo;
import com.chat.laty.entity.BannerInfo;
import com.chat.laty.entity.FilterBean;
import com.chat.laty.entity.OtherUserInfo;
import com.chat.laty.entity.RYTokenInfo;
import com.chat.laty.entity.TodayFateEntity;
import com.chat.laty.entity.WebBeanInfo;
import com.chat.laty.entity.WeiChatPayInfo;
import com.chat.laty.entity.XYUserInfo;
import com.chat.laty.entity.event.LocationEvent;
import com.chat.laty.mvp.BasePresenterFragment;
import com.chat.laty.presenters.DiscoverPresenter;
import com.chat.laty.utils.LocationUtils;
import com.chat.laty.utils.PermissionInterceptor;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import cn.xjc_soft.lib_utils.LibCollections;
import io.rong.imkit.config.RongConfigCenter;
import io.rong.imkit.utils.RouteUtils;
import io.rong.imlib.model.Conversation;

/**
 * <AUTHOR>
 * @date 2023/11/21 11:20
 * @description:好友
 */
public class RecomendFragment extends BasePresenterFragment<DiscoverPresenter> implements DiscoverController.View {

    private static final String KEY_TO_FRIEND_TYPE = "_KEY_TO_FRIEND_TYPE_";

    @BindView(R.id.fg_srl)
    SmartRefreshLayout mSmartRefreshLayout;

    @BindView(R.id.fragment_rv)
    RecyclerView mGoodFriendRv;

    RecommendAdapter mRecommendAdapter;
    List<OtherUserInfo> mRecommendDatas = new ArrayList<>();


    private int mAccostUserPosition;

    private OtherUserInfo mAccostUserInfo;

    FilterBean mCurrentInfo;

    public static RecomendFragment newInstance(int friendType) {
        Bundle args = new Bundle();
        RecomendFragment fragment = new RecomendFragment();
        args.putInt(KEY_TO_FRIEND_TYPE, friendType);
        fragment.setArguments(args);
        return fragment;
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onFilterBeanCallback(FilterBean info) {
        mCurrentInfo = info;
//        presenter.getRecommend(PAGE_INDEX, getFriendsType(), mCurrentInfo,false);
        mSmartRefreshLayout.autoRefresh();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onLocationCallback(LocationEvent event) {
        presenter.updateLocation(event.getLocationEntity().getLatitude(), event.getLocationEntity().getLongitude());
//        mSmartRefreshLayout.autoRefresh();
        presenter.getRecommend(PAGE_INDEX, getFriendsType(), mCurrentInfo, false);
    }

    private int getFriendsType() {
        return null == getArguments() ? 0 : getArguments().getInt(KEY_TO_FRIEND_TYPE);
    }

    @Override
    protected int getContentViewId() {
        return R.layout.fragment_base_rv_layout;
    }

    @Override
    public void initViews(View rootView) {
        mRecommendAdapter = new RecommendAdapter();
        mGoodFriendRv.setAdapter(mRecommendAdapter);
        mRecommendAdapter.setStateViewEnable(true);
        mGoodFriendRv.setLayoutManager(new GridLayoutManager(getActivity(), 1));
        if (mGoodFriendRv.getItemDecorationCount() == 0) {
            mGoodFriendRv.addItemDecoration(new ItemDecoration(getContext(), 0, 10f, 10f));
        }
        mRecommendAdapter.setItemAnimation(BaseQuickAdapter.AnimationType.ScaleIn);

        mRecommendAdapter.addOnItemChildClickListener(R.id.accost_button, (baseQuickAdapter, view, position) -> {
            if (XYApplication.appLogin(true)) {
                if (XYApplication.getCurrentUserStatus()) {
                    mAccostUserPosition = position;
                    mAccostUserInfo = (OtherUserInfo) baseQuickAdapter.getItem(position);
//                sendMsg(mAccostUserInfo.getId());
                    presenter.accostToUser(mAccostUserInfo.getId());
                }
            }
        });

        mRecommendAdapter.addOnItemChildClickListener(R.id.chat_button, (baseQuickAdapter, view, position) -> {
            if (XYApplication.appLogin(true)) {
                if (XYApplication.getCurrentUserStatus()) {
                    OtherUserInfo info = (OtherUserInfo) baseQuickAdapter.getItem(position);
                    // 默认拉取历史消息数量
                    RongConfigCenter.conversationConfig().setConversationHistoryMessageCount(10);
                    RouteUtils.routeToConversationActivity(getActivity(), Conversation.ConversationType.PRIVATE, info.getId(), null);
                }
            }
        });

        mRecommendAdapter.setOnItemClickListener((adapter, view, position) -> {
            if (XYApplication.appLogin(true)) {
                OtherUserInfo info = (OtherUserInfo) adapter.getItem(position);
                UserCenterUI.start(getActivity(), info.getId());
//                sendMsg(info.getId());
            }
        });

        mSmartRefreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                PAGE_INDEX++;
                presenter.getRecommend(PAGE_INDEX, getFriendsType(), mCurrentInfo, true);
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                PAGE_INDEX = MIN_PAGE_INDEX_1;
//                presenter.getFriendsByType(getFriendsType(), PAGE_INDEX);
                presenter.getRecommend(PAGE_INDEX, getFriendsType(), mCurrentInfo, true);
                mCurrentInfo = null;
            }
        });
//        mSmartRefreshLayout.autoRefresh();

        if (0 == getFriendsType()) {
            presenter.getRecommend(PAGE_INDEX, getFriendsType());
        }
    }

    @Override
    protected void onVisibleChanged(boolean isVisible) {
        if (isVisible) {
            PAGE_INDEX = MIN_PAGE_INDEX_1;
            if (1 == getFriendsType()) {

                XXPermissions.with(this)
                        .permission(Permission.ACCESS_COARSE_LOCATION)
                        .permission(Permission.ACCESS_FINE_LOCATION)
                        // 如果不需要在后台使用定位功能，请不要申请此权限
//                    .permission(Permission.ACCESS_BACKGROUND_LOCATION)
                        .interceptor(new PermissionInterceptor())
                        .request(new OnPermissionCallback() {

                            @Override
                            public void onGranted(@NonNull List<String> permissions, boolean allGranted) {
                                if (!allGranted) {
                                    return;
                                }
                                LocationUtils.getInstance().startLocalService();
                            }
                        });
            }else {
                presenter.getRecommend(PAGE_INDEX, getFriendsType());
            }
        }
    }

    @Override
    protected DiscoverPresenter setPresenter() {
        return new DiscoverPresenter(this);
    }

    @Override
    public FragmentActivity context() {
        return getActivity();
    }

    @Override
    public void showPayParm(RYTokenInfo data) {

    }

    @Override
    public void showAXPayParm(WeiChatPayInfo data) {

    }

    @Override
    public void showRecommend(List<OtherUserInfo> list) {
        mSmartRefreshLayout.finishRefresh();
        mSmartRefreshLayout.finishLoadMore();
        if (!LibCollections.isEmpty(list)) {
            if (PAGE_INDEX == MIN_PAGE_INDEX_1) {
                mRecommendDatas = list;
            } else {
                mRecommendDatas.addAll(list);
            }
            mRecommendAdapter.setItems(mRecommendDatas);
            mRecommendAdapter.setStateView(createEmptyListView());
            mRecommendAdapter.notifyDataSetChanged();
        }
//        if (PAGE_INDEX == MIN_PAGE_INDEX_1) {
//            mRecommendDatas = list;
//        } else {
//            mRecommendDatas.addAll(list);
//        }
//        mRecommendAdapter.setItems(mRecommendDatas);
//        mRecommendAdapter.setStateView(createEmptyListView());
//        mRecommendAdapter.notifyDataSetChanged();
    }

    @Override
    public void showBanners(List<BannerInfo> data) {

    }

    @Override
    public void showAccostToUserSuccess() {
        Toaster.show("搭讪成功");
        mAccostUserInfo.setIsAccost("1");
        mRecommendAdapter.notifyItemChanged(mAccostUserPosition);
    }

    @Override
    public void showTodayFate(TodayFateEntity todayFate) {

    }

    @Override
    public void showAppVersion(AppVersionInfo data) {

    }

    @Override
    public void showOneClickAccostCallback() {

    }

    @Override
    public void showWebInfo(WebBeanInfo data) {

    }

    @Override
    public void showUserDetails(XYUserInfo userInfo) {

    }

    @Override
    public void showRecomPop(List<RecommendationBean> recommendationBeans) {

    }

    @Override
    public void showRecomPop1() {

    }

    @Override
    protected boolean isUseEventBus() {
        return true;
    }


}
