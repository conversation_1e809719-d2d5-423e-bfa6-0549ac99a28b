package com.chat.laty.fragment;

import android.os.Bundle;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.RecyclerView;

import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshLoadMoreListener;
import com.chat.laty.R;
import com.chat.laty.adapter.MasonryAdapter;
import com.chat.laty.controllers.MasonryController;
import com.chat.laty.entity.MasonryInfo;
import com.chat.laty.mvp.BasePresenterFragment;
import com.chat.laty.presenters.MasonryPresenter;

import org.angmarch.views.NiceSpinner;
import org.angmarch.views.OnSpinnerItemSelectedListener;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import cn.xjc_soft.lib_utils.LibCollections;

/**
 * <AUTHOR>
 * @date 2023/11/21 11:20
 * @description:砖石
 */
public class MasonryDetailFragment extends BasePresenterFragment<MasonryPresenter> implements MasonryController.View {

    private static final String KEY_ARGS = "args";
    @BindView(R.id.smart_refresh_layout)
    SmartRefreshLayout refreshLayout;
    @BindView(R.id.recycler_view)
    RecyclerView recyclerView;

    @BindView(R.id.nice_spinner)
    NiceSpinner mSpinner;

    MasonryAdapter adapter;

    List<String> mSpinnerDatas = new ArrayList<>();

    List<MasonryInfo> mDatas = new ArrayList<>();

    int mType = -1;
    int mSpinnerPosition = 0;


    public static MasonryDetailFragment newInstance(int type) {
        Bundle args = new Bundle();
        MasonryDetailFragment fragment = new MasonryDetailFragment();
        args.putInt(KEY_ARGS, type);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    protected int getContentViewId() {
        return R.layout.fragment_gold_coin_detail_filtter_layout;
    }

    @Override
    public void initViews(View rootView) {
        mType = getArguments().getInt(KEY_ARGS);
        mSpinner.setVisibility(mType == 5 ? View.VISIBLE : View.GONE);
        adapter = new MasonryAdapter(getArguments().getInt(KEY_ARGS));
        recyclerView.setAdapter(adapter);
        adapter.setStateViewEnable(true);

        initPop();
        refreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                PAGE_INDEX++;
                presenter.getWithdrawalList(PAGE_INDEX, getArguments().getInt(KEY_ARGS), mSpinnerPosition);
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                PAGE_INDEX = 1;
                presenter.getWithdrawalList(PAGE_INDEX, getArguments().getInt(KEY_ARGS), mSpinnerPosition);
            }
        });
    }

    private void initPop() {
        mSpinnerDatas.clear();
        mSpinnerDatas.add("全部");
        mSpinnerDatas.add("支付宝");
        mSpinnerDatas.add("银行卡");
//        mSpinnerDatas.add("打款成功");
//        mSpinnerDatas.add("提现失败");

        mSpinner.attachDataSource(mSpinnerDatas);

        mSpinner.setOnSpinnerItemSelectedListener(new OnSpinnerItemSelectedListener() {
            @Override
            public void onItemSelected(NiceSpinner parent, View view, int position, long id) {
                mSpinnerPosition = position;
                refreshLayout.autoRefresh();
            }
        });
    }


    @Override
    public FragmentActivity context() {
        return getActivity();
    }

    @Override
    public void getDetailSucceed(List<MasonryInfo> list) {
        if (LibCollections.isEmpty(list) || list.size() < PAGE_SIZE) {
            refreshLayout.setEnableLoadMore(false);
        }

        if (PAGE_INDEX == MIN_PAGE_INDEX_1) {
            mDatas = list;
            refreshLayout.finishRefresh();
        } else {
            mDatas.addAll(list);
            refreshLayout.finishLoadMore();
        }

        adapter.setStateView(createEmptyListView());
        adapter.setItems(mDatas);
        adapter.notifyDataSetChanged();
    }

    @Override
    public void onResume() {
        super.onResume();
        refreshLayout.autoRefresh();
    }

    @Override
    protected MasonryPresenter setPresenter() {
        return new MasonryPresenter(this);
    }
}
