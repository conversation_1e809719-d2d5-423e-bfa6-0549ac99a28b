package com.chat.laty.activity;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.chat.laty.presenters.InvitePresenter1;
import com.hjq.toast.Toaster;
import com.chat.laty.R;
import com.chat.laty.controllers.InviteController;
import com.chat.laty.dialog.BottomInviteShareDialog;
import com.chat.laty.dialog.InviteCodeDialog;
import com.chat.laty.entity.InviteFriendInfoModel;
import com.chat.laty.entity.InviteInfoModel;
import com.chat.laty.entity.event.InviteEvent;
import com.chat.laty.mvp.BasePresenterActivity;
import com.chat.laty.utils.AgreementController;
import com.chat.laty.utils.ClipboardHelper;
import com.chat.laty.view.CustomActionBar;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;

public class InviteActivity1 extends BasePresenterActivity<InvitePresenter1> implements InviteController.IInfoView {

    public static void startAct(Context context) {
        Intent intent = new Intent(context, InviteActivity1.class);
        context.startActivity(intent);
    }

    @BindView(R.id.custom_bar)
    CustomActionBar customBar;

    @BindView(R.id.tv_invite)
    TextView tvInvite;

    @BindView(R.id.v_auth)
    View vAuthView;
    @BindView(R.id.tv_value_1)
    TextView tvValue1;
    @BindView(R.id.tv_value_2)
    TextView tvValue2;
    @BindView(R.id.tv_modify)
    TextView tvModify;
    @BindView(R.id.tv_bind_self)
    View tvBindSelf;
    @BindView(R.id.tv_bind_friend)
    View tvBindFriend;
    @BindView(R.id.recycler_view)
    RecyclerView recyclerView;

    @BindView(R.id.tv_empty_view)
    TextView tvEmptyView;

    @BindView(R.id.btn_confirm)
    View confirm;

    private InviteInfoModel model;

    private String url = "";

    private Adapter adapter;

    private List<InviteFriendInfoModel> list = new ArrayList<>();

    @Override
    protected void initDatas() {

    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_invite_layout1;
    }


    private final AgreementController controller = new AgreementController();


    @Override
    protected void initViews() {
        customBar.setTitleText("邀请有奖");
        customBar.setRightText("邀请须知");
        customBar.setAllColor(ContextCompat.getColor(this, R.color.white));

        customBar.addFunction(CustomActionBar.FUNCTION_TEXT_RIGHT);
        customBar.setOnActionBarClickListerner(function -> {
            if (function == CustomActionBar.FUNCTION_BUTTON_LEFT) {

                InviteActivity1.this.finish();
                return true;
            } else if (function == CustomActionBar.FUNCTION_TEXT_RIGHT) {
                controller.getAccordByNum(this, 4);
                return true;
            }
            return false;
        });

        adapter = new Adapter();
        adapter.setItems(list);

        recyclerView.setNestedScrollingEnabled(false);
        recyclerView.setAdapter(adapter);

        presenter.getInviteInfo();
        presenter.getInviteFriends();
        presenter.getDownloadUrl();
    }


    @Override
    protected InvitePresenter1 setPresenter() {
        return new InvitePresenter1(this);
    }


    @Override
    public FragmentActivity context() {
        return this;
    }


    @SuppressLint({"NotifyDataSetChanged", "NonConstantResourceId"})
    @OnClick({R.id.tv_copy, R.id.tv_modify, R.id.tv_renzheng, R.id.btn_confirm, R.id.tv_bind_friend, R.id.tv_bind_self})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.tv_copy:
                String code = tvInvite.getText().toString();
                if (model == null) {
                    Toaster.show("复制失败～");
                    return;
                }
                if (!TextUtils.equals("1", model.getIsName())) {
                    Toaster.show("请先完成实名认证");
                    return;
                }

                if (!TextUtils.isEmpty(code)) {
                    ClipboardHelper helper = new ClipboardHelper(this);
                    helper.copyText(code);
                    Toaster.show("复制成功～");
                }
                break;
            case R.id.tv_renzheng:
                MyAuthenticationUI.start(this);
                break;
            case R.id.tv_modify:
                InviteCodeDialog.Build builder = new InviteCodeDialog.Build(this);
                builder.setCancelBtn("取消").setSureBtn("确定").setTitle("修改邀请码").setItemCallBack(code1 -> {
                    showProgressDialog(R.string.app_loadding);
                    presenter.modify(code1);
                }).create().show();
                break;
            case R.id.tv_bind_self:
                InviteBindSelfActivity.startAct(this);
                break;
            case R.id.tv_bind_friend:
                InviteCodeDialog.Build build = new InviteCodeDialog.Build(this);
                build.setCancelBtn("取消").setSureBtn("确定").setTitle("好友绑定").setItemCallBack(code2 -> {
                    showProgressDialog(R.string.app_loadding);
                    presenter.bindFriend(code2);
                }).create().show();
                break;

            case R.id.btn_confirm:
                if (TextUtils.isEmpty(url)) {
                    Toaster.show("分享连接获取失败～");
                    return;
                }
                BottomInviteShareDialog dialog = new BottomInviteShareDialog(this, url);
                dialog.show();
                break;
        }
    }

    @SuppressLint({"NotifyDataSetChanged", "SetTextI18n"})
    @Override
    public void getInfoSucceed(InviteInfoModel data) {
        this.model = data;

        tvInvite.setText(data.getInvitationCode());

        tvValue1.setText(data.getTotalRechargeNum() + "");
        tvValue2.setText(data.getTotalSocializeNum() + "");

        boolean modifyAble = TextUtils.equals("1", data.getIsChangeCode());
        tvModify.setVisibility(modifyAble ? View.VISIBLE : View.GONE);

        boolean bindFriend = TextUtils.equals("1", data.getIsBindingFriend());
        tvBindFriend.setVisibility(bindFriend ? View.VISIBLE : View.GONE);

        boolean bindSelf = TextUtils.equals("1", data.getIsMyBinding());
        tvBindSelf.setVisibility(bindSelf ? View.VISIBLE : View.GONE);

        boolean hasAuthed = TextUtils.equals("1", data.getIsName());
        vAuthView.setVisibility(hasAuthed ? View.GONE : View.VISIBLE);

        boolean invite = TextUtils.equals("1", data.getInviteNow());
        confirm.setVisibility(invite ? View.VISIBLE : View.GONE);

    }

    @Override
    public void getInfoFailed() {

    }

    @SuppressLint("NotifyDataSetChanged")
    @Override
    public void getFriendsSucceed(List<InviteFriendInfoModel> list) {
        this.list = list;
        adapter.setItems(list);
        adapter.notifyDataSetChanged();
        tvEmptyView.setVisibility(list.isEmpty() ? View.VISIBLE : View.GONE);
    }

    @Override
    public void getFriendsFailed() {

    }

    @Override
    public void modifySucceed() {
        presenter.getInviteInfo();
        presenter.getInviteFriends();
        dismissProgressDialog();
    }

    @Override
    public void modifyFailed() {
        dismissProgressDialog();
    }

    @Override
    public void bindFriendSucceed() {
        presenter.getInviteInfo();
        presenter.getInviteFriends();
        dismissProgressDialog();
    }

    @Override
    protected boolean isUseEventBus() {
        return true;
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void updatePageState(InviteEvent event){
        presenter.getInviteInfo();
        presenter.getInviteFriends();
    }

    @Override
    public void bindFriendFailed() {
        dismissProgressDialog();
    }

    @Override
    public void getDownUrlSucceed(String url) {
        this.url = url;
    }

    private static class Adapter extends BaseQuickAdapter<InviteFriendInfoModel, QuickViewHolder> {

        @Override
        protected void onBindViewHolder(@NonNull QuickViewHolder helper, int i, @Nullable InviteFriendInfoModel item) {

            helper.setText(R.id.tv_name, item.getNickname());

            helper.setText(R.id.tv_time, item.getBindingDate());
        }

        @NonNull
        @Override
        protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int i) {
            return new QuickViewHolder(R.layout.adapter_invite_list_layout, viewGroup);
        }
    }

}