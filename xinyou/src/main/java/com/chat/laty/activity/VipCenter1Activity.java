package com.chat.laty.activity;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Handler;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.hjq.toast.Toaster;
import com.chat.laty.R;
import com.chat.laty.adapter.VipCardAdapter;
import com.chat.laty.base.XYApplication;
import com.chat.laty.controllers.VipCenterController;
import com.chat.laty.entity.PayResultEntity;
import com.chat.laty.entity.RyUserInfo;
import com.chat.laty.entity.VipInfoModel;
import com.chat.laty.entity.WeiChatPayInfo;
import com.chat.laty.greenDao.RyUserInfoDao;
import com.chat.laty.mvp.BasePresenterActivity;
import com.chat.laty.presenters.VipCenterPresenter;
import com.chat.laty.utils.PicassoUtils;
import com.chat.laty.view.CustomActionBar;
import com.youth.banner.Banner;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;

public class VipCenter1Activity extends BasePresenterActivity<VipCenterPresenter> implements VipCenterController.IInfoView {


    public static void startAct(Context context) {
        Intent intent = new Intent(context, VipCenter1Activity.class);
        context.startActivity(intent);
    }
    @BindView(R.id.custom_bar)
    CustomActionBar customBar;
    @BindView(R.id.tv_user_name)
    TextView tvUserName;
    @BindView(R.id.iv_avatar)
    ImageView ivAvatar;
    @BindView(R.id.v_vip_layout)
    View vVipLayout;
    @BindView(R.id.tv_vip_level)
    TextView tvVipLevel;
    @BindView(R.id.tv_vip_label)
    TextView tvVipLabel;
    @BindView(R.id.recycler_view)
    RecyclerView rvVipCard;
    @BindView(R.id.banner)
    Banner banner;


    @Override
    protected void initDatas() {
//        userInfo = XYApplication.getDaoInstant().getRyUserInfoDao().queryBuilder().where(RyUserInfoDao.Properties.Id.eq(data.getUserId())).unique();
    }


    @Override
    protected void onResume() {
        super.onResume();
        if (payed) {
            presenter.getVipInfo();
            payed = false;
        }
    }

    boolean payed = false;

    @Override
    protected boolean isUseEventBus() {
        return true;
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void payResult(PayResultEntity event) {
        if (event.code == 0) {
            payed = true;
            Toaster.show("开通成功");
            new Handler().postDelayed(() -> presenter.getVipInfo(), 3000);
        }
    }


    VipCardAdapter vipCardAdapter;
//    VipCardAdapterV2 bannerAdapter;

    @Override
    protected int getContentViewId() {
        return R.layout.activity_vip_center1;
    }

    VipInfoModel model;
    RyUserInfo userInfo;
    VipInfoModel.VipTypeModel vipTypeModel;
    List<VipInfoModel.VipTypeModel> list = new ArrayList<>();
    int index = 0;

    int payTypeIndex = -1;

    @Override
    protected void initViews() {

        customBar.setTitleText("会员中心");
        customBar.setRightText("vip开通记录");
        customBar.setAllColor(ContextCompat.getColor(this, R.color.white));

        customBar.setLeftBackground(ContextCompat.getDrawable(this, R.mipmap.icon_back_white));

        customBar.addFunction(CustomActionBar.FUNCTION_TEXT_RIGHT);
        customBar.setOnActionBarClickListerner(function -> {
            if (function == CustomActionBar.FUNCTION_BUTTON_LEFT) {
                VipCenter1Activity.this.finish();
                return true;
            } else if (function == CustomActionBar.FUNCTION_TEXT_RIGHT) {
                VipDetailActivity.startAct(VipCenter1Activity.this);
                return true;
            }
            return false;
        });

        vipCardAdapter = new VipCardAdapter();
        rvVipCard.setAdapter(vipCardAdapter);
        rvVipCard.setLayoutManager(new LinearLayoutManager(this));
        vipCardAdapter.setOnItemClickListener((baseQuickAdapter, view, i) -> {
            index = i;
            vipTypeModel = model.getVipList().get(i);
            //带参跳转到VipCenterActivity
            VipCenterActivity.startAct(VipCenter1Activity.this, vipTypeModel.getId());
            // TODO: 2024/10/31 会员分类带参 toggle();
        });
//        vipCardAdapter.setItems(list);

        /*bannerAdapter = new VipCardAdapterV2(list);
        banner.setAdapter(bannerAdapter);
//        banner.setPageTransformer(new ScaleInTransformer());
        banner.setPageTransformer(new MZScaleInTransformer(0.95f));
        banner.addPageTransformer(new DepthPageTransformer());
        banner.addPageTransformer(new ZoomOutPageTransformer());
//        banner.setBannerGalleryEffect(22, 6);
        banner.setBannerGalleryEffect(0, 60, 1);
        banner.addBannerLifecycleObserver(this);
        banner.addOnPageChangeListener(new OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

            }

            @Override
            public void onPageSelected(int i) {
                index = i;
                vipTypeModel = model.getVipList().get(i);
                toggle();
            }

            @Override
            public void onPageScrollStateChanged(int state) {

            }
        });*/

        presenter.getVipInfo();
    }

    @Override
    protected VipCenterPresenter setPresenter() {
        return new VipCenterPresenter(this, null);
    }

    @SuppressLint("NotifyDataSetChanged")
    @Override
    public void getInfoSucceed(VipInfoModel data) {

        this.model = data;

        tvUserName.setText(data.getNickname());
        PicassoUtils.showImage(ivAvatar, data.getAvatar());

        if (userInfo == null) {
            userInfo = XYApplication.getDaoInstant().getRyUserInfoDao().queryBuilder().where(RyUserInfoDao.Properties.Id.eq(data.getUserId())).unique();
        }

        if (userInfo != null && TextUtils.equals("2", userInfo.getSex())) {
            tvVipLabel.setVisibility(View.GONE);
            vVipLayout.setVisibility(View.GONE);
        } else {
            String label = "你还没有成为会员～";
            vVipLayout.setVisibility(View.GONE);

            if (!TextUtils.equals("0", data.getVipLevel())) {
                label = "到期时间：" + data.getVipEndTime();
                vVipLayout.setVisibility(View.VISIBLE);
                int color = Color.parseColor(getVipColors(data.getVipLevel()));
                tvVipLevel.setText(data.getVipName());
                tvVipLevel.setTextColor(color);
            }
            tvVipLabel.setText(label);
        }

        list = data.getVipList();
        vipCardAdapter.setItems(list);
        vipCardAdapter.notifyDataSetChanged();
//        bannerAdapter.setDatas(list);

        index = 0;
        vipTypeModel = data.getVipList().get(index);
    }

    @Override
    public void getInfoFailed() {
    }

    @Override
    public void buySucceed(WeiChatPayInfo info) {

    }

    private String getVipColors(String level) {
        switch (level) {
            case "1":
                return "#824D2F";
            case "2":
                return "#5A7AB4";
            case "3":
                return "#7E496C";
            case "4":
                return "#953D4C";
        }
        return "#824D2F";
    }


    @Override
    public void failed() {
        dismissProgressDialog();
    }

    @Override
    public FragmentActivity context() {
        return this;
    }

    @Override
    protected void onDestroy() {
        banner.destroy();
        super.onDestroy();
    }
}