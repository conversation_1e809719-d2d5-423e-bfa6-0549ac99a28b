package com.chat.laty.activity;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.chat.laty.R;
import com.chat.laty.controllers.VipCenterController;
import com.chat.laty.entity.VipDetailModel;
import com.chat.laty.mvp.BasePresenterActivity;
import com.chat.laty.presenters.VipCenterPresenter;
import com.chat.laty.view.CustomActionBar;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;

public class VipDetailActivity extends BasePresenterActivity<VipCenterPresenter> implements VipCenterController.IDetailView {


    public static void startAct(Context context) {
        Intent intent = new Intent(context, VipDetailActivity.class);
        context.startActivity(intent);
    }


    @BindView(R.id.custom_bar)
    CustomActionBar customBar;
    @BindView(R.id.recycler_view)
    RecyclerView recyclerView;

    List<VipDetailModel> list = new ArrayList<>();

    VipRecordAdapter adapter;

    @Override
    protected void initDatas() {

    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_vip_recharge_records;
    }

    @Override
    protected void initViews() {
        customBar.setTitleText("开通记录");
        customBar.setAllColor(ContextCompat.getColor(this, R.color.black));

        customBar.setOnActionBarClickListerner(function -> {
            if (function == CustomActionBar.FUNCTION_BUTTON_LEFT) {
                VipDetailActivity.this.finish();
                return true;
            }
            return false;
        });

        adapter = new VipRecordAdapter();
        recyclerView.setAdapter(adapter);
        adapter.setItems(list);

        showProgressDialog(R.string.app_loadding);
        presenter.getVipRecords();
    }

    @Override
    protected VipCenterPresenter setPresenter() {
        return new VipCenterPresenter(null, this);
    }


    @Override
    public FragmentActivity context() {
        return this;
    }

    @SuppressLint("NotifyDataSetChanged")
    @Override
    public void getDetailSucceed(List<VipDetailModel> list) {
        this.list.addAll(list);
        adapter.setStateView(getLayoutInflater().inflate(R.layout.community_empty_layout, null));
        adapter.setStateViewEnable(true);
        adapter.notifyDataSetChanged();
        dismissProgressDialog();
    }

    @Override
    public void getDetailFailed() {
        dismissProgressDialog();
    }

    static class VipRecordAdapter extends BaseQuickAdapter<VipDetailModel, QuickViewHolder> {

        @Override
        protected void onBindViewHolder(@NonNull QuickViewHolder helper, int i, @Nullable VipDetailModel item) {
            helper.setText(R.id.tv_name, item.getVipName());
            helper.setText(R.id.tv_time, "开通时间：" + item.getPayTime());
            helper.setText(R.id.tv_price, item.getPrice() + "元");
        }

        @NonNull
        @Override
        protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int i) {
            return new QuickViewHolder(R.layout.adapter_vip_records_layout, viewGroup);
        }
    }
}