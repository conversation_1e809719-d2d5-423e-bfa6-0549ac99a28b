package com.chat.laty.activity;

import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;
import android.widget.ImageView;

import androidx.fragment.app.FragmentActivity;

import com.chat.laty.entity.UserCenterInfo;
import com.chat.laty.utils.PicassoUtils;
import com.hjq.toast.Toaster;
import com.chat.laty.R;
import com.chat.laty.controllers.AuthenticationController;
import com.chat.laty.entity.BaseBean;
import com.chat.laty.entity.BaseEntity;
import com.chat.laty.entity.UploadImgInfo;
import com.chat.laty.mvp.BasePresenterActivity;
import com.chat.laty.presenters.AuthenticationPresenter;
import com.chat.laty.view.CustomActionBar;

import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * <AUTHOR>
 * @date 2024/1/12 10:10
 * @description:
 */
public class MyAuthenticationUI extends BasePresenterActivity<AuthenticationPresenter> implements AuthenticationController.View {

    @BindView(R.id.custom_bar)
    CustomActionBar customBar;

    @BindView(R.id.shimin_tv)
    TextView mShiMinTv;

    @BindView(R.id.zhenren_tv)
    TextView mZhenRenTv;

    @BindView(R.id.tv_name)
    TextView tvName;

    @BindView(R.id.iv_avatar)
    ImageView ivAvatar;

    BaseEntity mStatuInfo;

    public static void start(Context context) {
        Intent intent = new Intent(context, MyAuthenticationUI.class);
        context.startActivity(intent);
    }

    @Override
    protected void initDatas() {

    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_my_authentication_layout;
    }

    @Override
    protected void initViews() {
        customBar.setTitleText("我的认证");
    }

    @OnClick({R.id.zhenren_tv, R.id.shimin_tv})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.shimin_tv:
                if (null != mStatuInfo) {
                    if (TextUtils.equals("1", mStatuInfo.getIsName())) {
                        Toaster.show("已实名认证通过，无需再实名了");
                        return;
                    }
                    MyShiMinAuthenticationUI.start(MyAuthenticationUI.this);
                }

                break;

            case R.id.zhenren_tv:
                if (null != mStatuInfo) {
                    if (TextUtils.equals("1", mStatuInfo.getIsReal())) {
                        Toaster.show("已真人认证通过，无需再认证了");
                        return;
                    }
                    MyRealPersonAuthenticationUI.start(MyAuthenticationUI.this);
                }

                break;
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        presenter.getUserVerify();
        presenter.getUserCenterInfo();
    }

    @Override
    protected AuthenticationPresenter setPresenter() {
        return new AuthenticationPresenter(this);
    }

    @Override
    public FragmentActivity context() {
        return this;
    }

    @Override
    public void showRealNameCallback(BaseBean base) {

    }

    @Override
    public void showUploadResult(List<UploadImgInfo> result) {

    }

    @Override
    public void showRealPersonCallback(BaseBean base) {

    }

    @Override
    public void showUserVerifyStatus(BaseEntity statuInfo) {
        mStatuInfo = statuInfo;
        if (null != statuInfo) {
            if (TextUtils.equals("1", statuInfo.getIsReal())) {
//                mZhenRenTv.setText("已真人认证");
                mZhenRenTv.setText("已认证");
            } else {
                mZhenRenTv.setText("认证");
            }

            if (TextUtils.equals("1", statuInfo.getIsName())) {
//                mShiMinTv.setText("已实名认证");
                mShiMinTv.setText("已认证");
            } else {
                mShiMinTv.setText("认证");
            }
        }
    }

    @Override
    public void showUserCenterDetails(UserCenterInfo data) {
        tvName.setText(data.getNickname());
        PicassoUtils.showImage(ivAvatar, data.getAvatar());
    }
}
