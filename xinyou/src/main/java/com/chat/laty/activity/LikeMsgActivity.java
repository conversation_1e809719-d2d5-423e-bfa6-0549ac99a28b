package com.chat.laty.activity;

import android.content.Context;
import android.content.Intent;

import androidx.annotation.NonNull;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.RecyclerView;

import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshLoadMoreListener;
import com.chat.laty.R;
import com.chat.laty.adapter.LikeMsgAdapter;
import com.chat.laty.base.XYApplication;
import com.chat.laty.controllers.SystemMsgController;
import com.chat.laty.entity.ReportMessageInfo;
import com.chat.laty.entity.SystemMessageInfo;
import com.chat.laty.mvp.BasePresenterActivity;
import com.chat.laty.presenters.SystemMsgPresenter;
import com.chat.laty.view.CustomActionBar;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import cn.xjc_soft.lib_utils.LibCollections;

/**
 * <AUTHOR>
 * @date 2024/1/25 11:18
 * @description:
 */
public class LikeMsgActivity extends BasePresenterActivity<SystemMsgPresenter> implements SystemMsgController.View {
    @BindView(R.id.custom_bar)
    CustomActionBar mActionBar;
    @BindView(R.id.base_rv)
    RecyclerView mRecyclerView;
    @BindView(R.id.activity_srl)
    SmartRefreshLayout mSmartRefreshLayout;

    LikeMsgAdapter mAdapter;

    List<SystemMessageInfo> mDatas = new ArrayList<>();

    private static final String EXTRA_KEY_TYPE = "extra_key_type";

    public static void start(Context context) {
        Intent intent = new Intent(context, LikeMsgActivity.class);
        context.startActivity(intent);
    }

    @Override
    protected void initDatas() {

    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_base_smartrefresh_recyclerview_layout;
    }

    @Override
    protected void initViews() {

        mActionBar.setTitleText("点赞消息");
        mActionBar.setTitleTextColor(getResources().getColor(R.color.color_333333));
        mAdapter = new LikeMsgAdapter();
//        mRecyclerView.setLayoutManager(manager);
        mAdapter.setStateViewEnable(true);

        mRecyclerView.setAdapter(mAdapter);

        mAdapter.addOnItemChildClickListener(R.id.remove_button, (baseQuickAdapter, view, position) -> {
            if (XYApplication.appLogin(true)) {
                SystemMessageInfo info = baseQuickAdapter.getItem(position);
                mAdapter.remove(info);
            }
        });

        mSmartRefreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                PAGE_INDEX++;
                presenter.getLikeByType(PAGE_INDEX);
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                PAGE_INDEX = MIN_PAGE_INDEX_1;
                presenter.getLikeByType(PAGE_INDEX);
            }
        });
        mSmartRefreshLayout.autoRefresh();
    }

    @Override
    protected SystemMsgPresenter setPresenter() {
        return new SystemMsgPresenter(this);
    }

    @Override
    public void showMessages(List<SystemMessageInfo> list) {

    }

    @Override
    public void showLikeMessages(List<SystemMessageInfo> list) {
        if (LibCollections.isEmpty(list) || list.size() < PAGE_SIZE) {
            mSmartRefreshLayout.setEnableLoadMore(false);
        }

        if (PAGE_INDEX == MIN_PAGE_INDEX_1) {
            mDatas = list;
            mSmartRefreshLayout.finishRefresh();
        } else {
            mDatas.addAll(list);
            mSmartRefreshLayout.finishLoadMore();
        }

        mAdapter.setStateView(createEmptyListView("暂无数据", R.mipmap.icon_nodata));
        mAdapter.setItems(mDatas);
        mAdapter.notifyDataSetChanged();
    }

    @Override
    public void showReportMessages(List<ReportMessageInfo> data) {

    }

    @Override
    public FragmentActivity context() {
        return this;
    }
}
