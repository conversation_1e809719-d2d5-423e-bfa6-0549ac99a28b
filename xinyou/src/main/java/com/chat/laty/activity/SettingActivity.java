package com.chat.laty.activity;

import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.SharedPreferences;
import android.text.InputType;
import android.view.View;
import android.widget.CompoundButton;
import android.widget.EditText;
import android.widget.Switch;
import android.widget.Toast;

import androidx.fragment.app.FragmentActivity;

import com.google.android.material.dialog.MaterialAlertDialogBuilder;
import com.chat.laty.R;
import com.chat.laty.base.XYApplication;
import com.chat.laty.controllers.GeneralSettingController;
import com.chat.laty.entity.BaseBean;
import com.chat.laty.entity.FilterBean;
import com.chat.laty.entity.GeneralSettingInfo;
import com.chat.laty.entity.RyUserInfo;
import com.chat.laty.entity.SimiInfo;
import com.chat.laty.entity.WebBeanInfo;
import com.chat.laty.greenDao.RyUserInfoDao;
import com.chat.laty.mvp.BasePresenterActivity;
import com.chat.laty.presenters.GeneralSettingPresenter;
import com.chat.laty.utils.XYSPUtils;
import com.chat.laty.utils.net.Common;
import com.chat.laty.view.CustomActionBar;

import org.greenrobot.eventbus.EventBus;

import butterknife.BindView;
import butterknife.OnClick;
import io.rong.imkit.RongIM;
import io.rong.imlib.RongIMClient;

/**
 * <AUTHOR>
 * @date 2023/12/26 0:14
 * @description:
 */
public class SettingActivity extends BasePresenterActivity<GeneralSettingPresenter> implements GeneralSettingController.View {
    @BindView(R.id.custom_bar)
    CustomActionBar mActionBar;

    @BindView(R.id.cost_settings_stv)
    View mCostStv;
    @BindView(R.id.accost_settings_stv)
    View accost_settings_stv;


    @BindView(R.id.teenager_mode_switch)
    Switch teenagerModeSwitch;

    RyUserInfo mUserInfo;

    public static void start(Context context) {
        Intent intent = new Intent(context, SettingActivity.class);
        context.startActivity(intent);
    }

    @Override
    protected void initDatas() {

    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_setting_layout;
    }

    @Override
    protected void initViews() {
        mActionBar.setTitleText("设置");
        mActionBar.setTitleTextColor(getResources().getColor(R.color.color_333333));
        String userId = XYSPUtils.getString(Common.KEY_APP_USER_RY_ID);
        mUserInfo = XYApplication.getDaoInstant().getRyUserInfoDao().queryBuilder().where(RyUserInfoDao.Properties.Id.eq(userId)).unique();

        if (null != mUserInfo && "1".equals(mUserInfo.getSex())) {
            mCostStv.setVisibility(View.GONE);
            accost_settings_stv.setVisibility(View.GONE);
        } else {
            mCostStv.setVisibility(View.VISIBLE);
            accost_settings_stv.setVisibility(View.VISIBLE);
        }

        //获取当前青少年模式状态
        teenagerModeSwitch.setChecked(getStatusFromCache());
        //监听青少年状态存储到缓存中
        teenagerModeSwitch.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                handleTeenModeClick(isChecked);
            }
        });


    }


    @OnClick({R.id.general_settings_stv, R.id.privacy_settings_stv, R.id.cost_settings_stv, R.id.accost_settings_stv, R.id.beauty_settings_stv, R.id.disturb_settings_stv, R.id.secure_settings_stv, R.id.aboutme_stv, R.id.convention_stv, R.id.logout_button})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.general_settings_stv://通用设置
                GeneralSettingsUI.start(this);
                break;

            case R.id.privacy_settings_stv://隐私设置
                AgreementUI.start(this);
                break;

            case R.id.cost_settings_stv://费用设置
                CostSettingUI.start(this);
                break;

            case R.id.accost_settings_stv://搭讪设置
                if (null != mUserInfo) {
                    if ("1".equals(mUserInfo.getSex())) {
                        AccostLanguageListUI.start(this);
                    } else {
                        AccostLanguageSettingUI.start(this);
                    }
                }

                break;

            case R.id.beauty_settings_stv://美颜设置
                BeautySetUI.start(SettingActivity.this);
                break;

            case R.id.disturb_settings_stv://免打扰设置
                DisturbSettingsUI.start(SettingActivity.this);
                break;

            case R.id.secure_settings_stv://账号安全设置
                AcountSettingUI.start(SettingActivity.this);
                break;

            case R.id.aboutme_stv://关于我们
                presenter.getAccordByNum(2);
                break;

            case R.id.convention_stv://平台公约
                presenter.getAccordByNum(6);
                break;

            case R.id.logout_button://退出登录
                RongIM.getInstance().logout();
                presenter.logOutApp();
                break;
        }
    }


    private static final String PREFS_NAME = "TeenModePrefs";
    private static final String PREF_PASSWORD = "TeenModePassword";

    private void handleTeenModeClick(boolean isChecked) {
        SharedPreferences prefs = getSharedPreferences(PREFS_NAME, MODE_PRIVATE);
        String password = prefs.getString(PREF_PASSWORD, null);
        if (password == null) {
            showSetPasswordDialog(isChecked);
        } else {
            showVerifyPasswordDialog(password, isChecked);
        }
    }

    private void showSetPasswordDialog(boolean isChecked) {
        MaterialAlertDialogBuilder builder = new MaterialAlertDialogBuilder(this);
        builder.setTitle("设置青少年模式密码");

        final EditText input = new EditText(this);
        input.setInputType(InputType.TYPE_CLASS_TEXT | InputType.TYPE_TEXT_VARIATION_PASSWORD);
        builder.setView(input);

        builder.setPositiveButton("确定", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                String password = input.getText().toString();
                if (!password.isEmpty()) {
                    savePassword(password);
                    Toast.makeText(SettingActivity.this, "密码已设置", Toast.LENGTH_SHORT).show();
                    saveStatusToCache(isChecked);
                    EventBus.getDefault().post(new FilterBean());
                    if (isChecked) {
                        RongIM.getInstance().setNotificationQuietHours("00:00:00", 1439, new RongIMClient.OperationCallback() {
                            @Override
                            public void onSuccess() {
                                Toast.makeText(SettingActivity.this, "消息免打扰已开启", Toast.LENGTH_SHORT).show();
                            }

                            @Override
                            public void onError(RongIMClient.ErrorCode errorCode) {
                                Toast.makeText(SettingActivity.this, "消息免打扰开启失败", Toast.LENGTH_SHORT).show();
                            }
                        });
                    } else {
                        RongIM.getInstance().removeNotificationQuietHours(new RongIMClient.OperationCallback() {
                            @Override
                            public void onSuccess() {
                                Toast.makeText(SettingActivity.this, "消息免打扰已关闭", Toast.LENGTH_SHORT).show();
                            }

                            @Override
                            public void onError(RongIMClient.ErrorCode errorCode) {
                                Toast.makeText(SettingActivity.this, "消息免打扰关闭失败", Toast.LENGTH_SHORT).show();
                            }
                        });
                    }
                } else {
                    Toast.makeText(SettingActivity.this, "密码不能为空", Toast.LENGTH_SHORT).show();
                    teenagerModeSwitch.setChecked(getStatusFromCache());
                }
            }
        });
        builder.setNegativeButton("取消", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                teenagerModeSwitch.setChecked(getStatusFromCache());
                dialog.cancel();
            }
        });

        builder.show();
    }


    private void showVerifyPasswordDialog(final String correctPassword, boolean isChecked) {
        MaterialAlertDialogBuilder builder = new MaterialAlertDialogBuilder(this);
        builder.setTitle("验证青少年模式密码");

        final EditText input = new EditText(this);
        input.setInputType(InputType.TYPE_CLASS_TEXT | InputType.TYPE_TEXT_VARIATION_PASSWORD);
        builder.setView(input);

        builder.setPositiveButton("确定", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                String password = input.getText().toString();
                if (password.equals(correctPassword)) {
                    Toast.makeText(SettingActivity.this, "密码正确", Toast.LENGTH_SHORT).show();
                    // 这里可以添加密码验证成功后的逻辑
                    saveStatusToCache(isChecked);
                    EventBus.getDefault().post(new FilterBean());
                    if (isChecked) {

                        RongIM.getInstance().setNotificationQuietHours("00:00:00", 1439, new RongIMClient.OperationCallback() {
                            @Override
                            public void onSuccess() {
                                Toast.makeText(SettingActivity.this, "消息免打扰已开启", Toast.LENGTH_SHORT).show();
                            }

                            @Override
                            public void onError(RongIMClient.ErrorCode errorCode) {
                                Toast.makeText(SettingActivity.this, "消息免打扰开启失败", Toast.LENGTH_SHORT).show();
                            }
                        });
                    } else {
                        RongIM.getInstance().removeNotificationQuietHours(new RongIMClient.OperationCallback() {
                            @Override
                            public void onSuccess() {
                                Toast.makeText(SettingActivity.this, "消息免打扰已关闭", Toast.LENGTH_SHORT).show();
                            }

                            @Override
                            public void onError(RongIMClient.ErrorCode errorCode) {
                                Toast.makeText(SettingActivity.this, "消息免打扰关闭失败", Toast.LENGTH_SHORT).show();
                            }
                        });
                    }
                } else {
                    Toast.makeText(SettingActivity.this, "密码错误", Toast.LENGTH_SHORT).show();
                    teenagerModeSwitch.setChecked(getStatusFromCache());
                }
            }
        });
        builder.setNegativeButton("取消", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                teenagerModeSwitch.setChecked(getStatusFromCache());
                dialog.cancel();
            }
        });

        builder.show();
    }


    private void savePassword(String password) {
        SharedPreferences.Editor editor = getSharedPreferences(PREFS_NAME, MODE_PRIVATE).edit();
        editor.putString(PREF_PASSWORD, password);
        editor.apply();
    }

    @Override
    protected GeneralSettingPresenter setPresenter() {
        return new GeneralSettingPresenter(this);
    }

    @Override
    public void showGeneralSetting(GeneralSettingInfo info) {

    }

    @Override
    public void showWebInfo(WebBeanInfo info) {
//        AppShowOnePicActivity.startActivity(SettingActivity.this,  info.getUrl());
        WebViewActivity.startActivity(SettingActivity.this, "", info.getUrl());

    }

    @Override
    public void showDeleteCallback(BaseBean baseBean) {

    }

    @Override
    public void showLogoutCallback(BaseBean uploadBean) {
        XYSPUtils.delete(Common.KEY_APP_TOKEN);
        XYSPUtils.delete(Common.KEY_APP_USER_RY_ID);
        XYSPUtils.delete(Common.KEY_APP_USER_GENDER);
        XYSPUtils.delete(Common.KEY_APP_USER_PHONE);
        XYSPUtils.delete(Common.KEY_APP_USER_OPENID);

        LoginByWeiChatUI.startNew(SettingActivity.this);
        finish();
    }

    @Override
    public void getSimiInfo(SimiInfo simiInfo) {

    }

    @Override
    public FragmentActivity context() {
        return this;
    }

    /**
     * 保存状态到缓存
     *
     * @param status
     */
    private void saveStatusToCache(boolean status) {
        SharedPreferences sharedPreferences = getSharedPreferences("app_prefs", Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = sharedPreferences.edit();
        editor.putBoolean("qsnms_status", status);
        editor.apply(); // 或者使用 editor.commit();
    }

    /**
     * 从缓存中获取状态
     *
     * @return
     */
    private boolean getStatusFromCache() {
        SharedPreferences sharedPreferences = getSharedPreferences("app_prefs", Context.MODE_PRIVATE);
        return sharedPreferences.getBoolean("qsnms_status", false); // 默认值为 false
    }
}
