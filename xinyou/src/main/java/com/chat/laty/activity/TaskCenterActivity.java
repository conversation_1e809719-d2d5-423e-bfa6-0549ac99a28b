package com.chat.laty.activity;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.os.Handler;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.google.android.material.tabs.TabLayout;
import com.chat.laty.MainActivity;
import com.chat.laty.R;
import com.chat.laty.controllers.TaskCenterController;
import com.chat.laty.entity.TaskCenterInfoModel;
import com.chat.laty.entity.TaskSignInfoModel;
import com.chat.laty.entity.event.MainTabChange;
import com.chat.laty.fragment.GoldCoinDetailFragment;
import com.chat.laty.mvp.BasePresenterActivity;
import com.chat.laty.presenters.TaskCenterPresenter;
import com.chat.laty.view.CustomActionBar;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;

public class TaskCenterActivity extends BasePresenterActivity<TaskCenterPresenter> implements TaskCenterController.IInfoView {


    public static void startAct(Context context) {
        Intent intent = new Intent(context, TaskCenterActivity.class);
        context.startActivity(intent);
    }

    @BindView(R.id.custom_bar)
    CustomActionBar customBar;

    @BindView(R.id.tv_value)
    TextView tvValue;
    @BindView(R.id.tv_sign)
    TextView tvSign;
    @BindView(R.id.tab_layout)
    TabLayout tabLayout;

    @BindView(R.id.recycler_view)
    RecyclerView recyclerView;
    @BindView(R.id.rv_task)
    RecyclerView rvTask;

    TaskCenterInfoModel model;
    List<Fragment> fragments = new ArrayList<>();

    ArrayList<String> titles = new ArrayList<>(Arrays.asList("新手任务", "每日任务"));

    List<TaskSignInfoModel> signs = new ArrayList<>();

    List<TaskCenterInfoModel.TaskInfoModel> dayTasks = new ArrayList<>();
    List<TaskCenterInfoModel.TaskInfoModel> newUserTasks = new ArrayList<>();

    SignAdapter adapter;

    TaskAdapter taskAdapter;

    int index = 0;

    private boolean opened = false;

    @Override
    protected void initDatas() {
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_task_center_layout;
    }

    @Override
    protected void initViews() {
        customBar.setTitleText("任务中心");
//        customBar.setTitleTextColor(ContextCompat.getColor(this, R.color.white));
        customBar.setOnActionBarClickListerner(function -> {
            if (function == CustomActionBar.FUNCTION_BUTTON_LEFT) {
                TaskCenterActivity.this.finish();
                return true;
            }
            return false;
        });

        String[] ts = new String[]{"星期一", "星期二", "星期三", "星期四", "星期五", "星期六", "星期日"};
        for (int i = 1; i <= 7; i++) {
            TaskSignInfoModel it = new TaskSignInfoModel();
            it.setTitle(ts[i - 1]);
            signs.add(it);
        }
        adapter = new SignAdapter();
        adapter.setItems(signs);
        recyclerView.setAdapter(adapter);

        taskAdapter = new TaskAdapter();
        taskAdapter.addOnItemChildClickListener(R.id.tv_status, (baseQuickAdapter, view, i) -> {
            opened = true;
            TaskCenterInfoModel.TaskInfoModel item = baseQuickAdapter.getItem(i);
            if (TextUtils.equals("1", item.getIsComplete())) return;
            String name = item.getTaskName();

            if (name.contains("完善个人资料") || name.contains("语音签名")) {
                ImproveInformationUI.start(getThisActivity());
            } else if (name.contains("真人认证")) {
                MyAuthenticationUI.start(getThisActivity());
            } else if (name.contains("首发动态") || name.contains("发布动态")) {
                MineDynamicReleaseUI.startAct(getThisActivity());
            } else if (name.contains("首次搭讪") || name.contains("私信聊天") || name.contains("语音通话") || name.contains("视频通话") || name.contains("搭讪二十次")) {
                EventBus.getDefault().post(new MainTabChange(0));
                MainActivity.start(getThisActivity());
            }
        });
        rvTask.setAdapter(taskAdapter);

        for (int i = 0; i < titles.size(); i++) {
            View view = getLayoutInflater().inflate(R.layout.tab_layout_item_view1, null);

            TextView textView = view.findViewById(R.id.tv_title);
            ImageView imageView = view.findViewById(R.id.iv_indicator);
            if (textView != null) {
                textView.setText(titles.get(i));
                toggleTextStyle(textView, i == 0);
            }
            if (imageView != null && i == 0) {
                imageView.setVisibility(View.VISIBLE);
            }

            TabLayout.Tab tab = tabLayout.newTab();
            tab.setCustomView(view);
            tabLayout.addTab(tab);

            fragments.add(GoldCoinDetailFragment.newInstance(i + 1));
        }

        tabLayout.addOnTabSelectedListener(new TabLayout.OnTabSelectedListener() {
            @Override
            public void onTabSelected(TabLayout.Tab tab) {
                toggleTextStyle(tab.getCustomView(), true);
                toggleList(tab.getPosition());
            }

            @Override
            public void onTabUnselected(TabLayout.Tab tab) {
                toggleTextStyle(tab.getCustomView(), false);
            }

            @Override
            public void onTabReselected(TabLayout.Tab tab) {

            }
        });

        presenter.getTaskCenterInfo();
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (opened) {
            presenter.getTaskCenterInfo();
        }
    }

    @OnClick({R.id.tv_sign})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.tv_sign:
                if (view.isSelected()) return;
                showProgressDialog(R.string.app_loadding);
                presenter.sign();
                break;
        }
    }


    private void toggleTextStyle(View view, boolean selected) {

        TextView textView = view.findViewById(R.id.tv_title);
        ImageView imageView = view.findViewById(R.id.iv_indicator);

        if (textView != null) {
            textView.setSelected(selected);
            textView.setTextSize(selected ? 18 : 15);
        }
        if (imageView != null) {
            imageView.setVisibility(selected ? View.VISIBLE : View.INVISIBLE);
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    @Override
    public void getInfoSucceed(TaskCenterInfoModel data) {
        this.model = data;

        tvValue.setText(data.getIntegralBalance() + "");

        Calendar calendar = Calendar.getInstance();
        int day = calendar.get(Calendar.DAY_OF_WEEK) - 1;

        for (int i = 1; i <= 7; i++) {
            boolean signed = TextUtils.equals("1", data.getWeeklySignVo().get("day" + (i)).toString());
            TaskSignInfoModel it = signs.get(i - 1);
            it.setSigned(signed);
            it.setReward("积分+10");
            if (day == i && signed) {
                it.setReward("已签到");
                tvSign.setText("已签到");
                tvSign.setSelected(true);
            }
        }

        adapter.notifyDataSetChanged();
        new Handler().postDelayed(() -> recyclerView.scrollToPosition(day), 1000);

        this.dayTasks = model.getEverydayTask();
        this.newUserTasks = model.getNewcomerRewards();

        toggleList(index);

    }

    @SuppressLint("NotifyDataSetChanged")
    private void toggleList(int index) {
        this.index = index;
        if (index == 0) {
            taskAdapter.setItems(newUserTasks);
        } else {
            taskAdapter.setItems(dayTasks);
        }
        taskAdapter.notifyDataSetChanged();
    }

    @Override
    public void getInfoFailed() {

    }

    @Override
    public void signSucceed() {
        presenter.getTaskCenterInfo();
        dismissProgressDialog();
    }

    @Override
    public void signFailed() {
        dismissProgressDialog();
    }

    @Override
    protected TaskCenterPresenter setPresenter() {
        return new TaskCenterPresenter(this);
    }

    @Override
    public FragmentActivity context() {
        return this;
    }


    static class SignAdapter extends BaseQuickAdapter<TaskSignInfoModel, QuickViewHolder> {

        @Override
        protected void onBindViewHolder(@NonNull QuickViewHolder helper, int i, @Nullable TaskSignInfoModel item) {
            helper.setText(R.id.tv_value, item.getReward());
            helper.setText(R.id.tv_date, item.getTitle());

            helper.itemView.setSelected(!item.isSigned());
            helper.getView(R.id.tv_value).setSelected(!item.isSigned());
            helper.getView(R.id.tv_date).setSelected(!item.isSigned());

//            helper.setImageDrawable(R.id.iv_icon, ContextCompat.getDrawable(getContext(), item.isSigned() ? R.mipmap.signed : R.mipmap.jifen123));
            helper.setImageResource(R.id.iv_icon, R.mipmap.jifen);
        }

        @NonNull
        @Override
        protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int i) {
            return new QuickViewHolder(R.layout.adapter_task_center_sign_layout, viewGroup);
        }
    }

    static class TaskAdapter extends BaseQuickAdapter<TaskCenterInfoModel.TaskInfoModel, QuickViewHolder> {

        @Override
        protected void onBindViewHolder(@NonNull QuickViewHolder helper, int i, @Nullable TaskCenterInfoModel.TaskInfoModel item) {
            helper.setText(R.id.tv_title, item.getTaskName());
            helper.setText(R.id.tv_reward, "+ " + item.getAddIntegral());
            helper.setText(R.id.tv_desc, item.getReachCondition());

            boolean selected = TextUtils.equals("1", item.getIsComplete());
            helper.setText(R.id.tv_status, !selected ? "去完成" : "已完成");
            helper.getView(R.id.tv_status).setSelected(!selected);
        }

        @NonNull
        @Override
        protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int i) {
            return new QuickViewHolder(R.layout.adapter_task_item_layout, viewGroup);
        }
    }
}