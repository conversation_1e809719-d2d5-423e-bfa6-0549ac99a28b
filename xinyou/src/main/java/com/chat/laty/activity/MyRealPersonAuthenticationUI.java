package com.chat.laty.activity;

import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.view.View;

import androidx.fragment.app.FragmentActivity;

import com.chat.laty.entity.UserCenterInfo;
import com.hjq.permissions.OnPermissionCallback;
import com.hjq.permissions.Permission;
import com.hjq.permissions.XXPermissions;
import com.hjq.toast.Toaster;
import com.luck.picture.lib.basic.PictureSelector;
import com.luck.picture.lib.config.SelectMimeType;
import com.luck.picture.lib.entity.LocalMedia;
import com.luck.picture.lib.interfaces.OnResultCallbackListener;
import com.makeramen.roundedimageview.RoundedImageView;
import com.chat.laty.R;
import com.chat.laty.controllers.AuthenticationController;
import com.chat.laty.entity.BaseBean;
import com.chat.laty.entity.BaseEntity;
import com.chat.laty.entity.UploadImgInfo;
import com.chat.laty.entity.event.InviteEvent;
import com.chat.laty.mvp.BasePresenterActivity;
import com.chat.laty.presenters.AuthenticationPresenter;
import com.chat.laty.utils.PermissionInterceptor;
import com.chat.laty.utils.PicassoUtils;
import com.chat.laty.view.CustomActionBar;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;
import cn.xjc_soft.lib_utils.LibCollections;

/**
 * <AUTHOR>
 * @date 2024/1/12 10:10
 * @description:真人
 */
public class MyRealPersonAuthenticationUI extends BasePresenterActivity<AuthenticationPresenter> implements AuthenticationController.View {

    @BindView(R.id.custom_bar)
    CustomActionBar customBar;
    @BindView(R.id.user_head_riv)
    RoundedImageView mUserPicRiv;
    String avatarUrl;

    public static void start(Context context) {
        Intent intent = new Intent(context, MyRealPersonAuthenticationUI.class);
        context.startActivity(intent);
    }

    @Override
    protected void initDatas() {

    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_my_real_person_authentication_layout;
    }

    @Override
    protected void initViews() {
        customBar.setTitleText("真人认证");
    }

    @OnClick({R.id.submit_btn, R.id.user_head_riv})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.submit_btn:
                submitData();
                break;

            case R.id.user_head_riv:

                XXPermissions.with(MyRealPersonAuthenticationUI.this).permission(Permission.CAMERA).interceptor(new PermissionInterceptor()).interceptor(new PermissionInterceptor()).request(new OnPermissionCallback() {
                    @Override
                    public void onGranted(List<String> permissions, boolean all) {
                        if (!all) {
                            return;
                        }
                        PictureSelector.create(MyRealPersonAuthenticationUI.this).openCamera(SelectMimeType.ofImage()).forResult(new OnResultCallbackListener<LocalMedia>() {
                            @Override
                            public void onResult(ArrayList<LocalMedia> result) {
                                showProgressDialog(R.string.app_uploadding);
                                presenter.uploadFiles(result, "userHead");
                            }

                            @Override
                            public void onCancel() {

                            }
                        });
                    }
                });
                break;
        }
    }

    private void submitData() {
        if (TextUtils.isEmpty(avatarUrl)) {
            Toaster.show("请上传实人照片，点击上方的最大图标进行上传");
            return;
        }

        showProgressDialog(R.string.app_downloadding);
        presenter.submitRealPerson(avatarUrl);
    }


    @Override
    protected AuthenticationPresenter setPresenter() {
        return new AuthenticationPresenter(this);
    }

    @Override
    public FragmentActivity context() {
        return this;
    }

    @Override
    public void showRealNameCallback(BaseBean base) {
    }

    @Override
    public void showUploadResult(List<UploadImgInfo> result) {
        dismissProgressDialog();
        avatarUrl = "";
        if (!LibCollections.isEmpty(result)) {
            avatarUrl = result.get(0).getTumhImgUrl();
            PicassoUtils.showImage(mUserPicRiv, avatarUrl);
        }
    }

    @Override
    public void showRealPersonCallback(BaseBean base) {
        dismissProgressDialog();
        if (200 != base.getCode()) {
            Toaster.show(base.getMessage());
        } else {
            Toaster.show("真人认证成功!");
            EventBus.getDefault().post(new InviteEvent());
            finish();
        }
    }

    @Override
    public void showUserVerifyStatus(BaseEntity statuInfo) {

    }

    @Override
    public void showUserCenterDetails(UserCenterInfo data) {

    }
}
