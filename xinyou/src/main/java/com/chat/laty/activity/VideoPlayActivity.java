package com.chat.laty.activity;

import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;

import androidx.annotation.NonNull;

import com.google.android.exoplayer2.MediaItem;
import com.google.android.exoplayer2.SimpleExoPlayer;
import com.google.android.exoplayer2.ui.PlayerView;
import com.chat.laty.R;
import com.chat.laty.base.BaseActivity;
import com.chat.laty.view.CustomActionBar;

import butterknife.BindView;

/**
 * <AUTHOR>
 * @date 2023/2/13.
 * description： 图片集预览
 */
public class VideoPlayActivity extends BaseActivity {
    @BindView(R.id.player)
    PlayerView player;

    @BindView(R.id.custom_bar)
    CustomActionBar activityPicBrowseTitleBar;

    private static final String KEY_TO_PIC_LIST = "_KEY_TO_PIC_LIST_";
    private String url;

    public static void start(Context context, String url) {
        Intent intent = new Intent(context, VideoPlayActivity.class);
        intent.putExtra(KEY_TO_PIC_LIST, url);
        context.startActivity(intent);
    }

    @Override
    protected void initDatas() {
        url = getIntent().getStringExtra(KEY_TO_PIC_LIST);
    }

    private SimpleExoPlayer mPlayer;


    @Override
    protected int getContentViewId() {
        return R.layout.act_display_video;
    }

    @Override
    protected void initViews() {
        mPlayer = new SimpleExoPlayer.Builder(this).build();
        player.setPlayer(mPlayer);
        MediaItem mediaItem = MediaItem.fromUri(url);
        mPlayer.setMediaItem(mediaItem);
        mPlayer.setPlayWhenReady(true);
        mPlayer.prepare();
    }


    @Override
    protected void onStart() {
        player.onResume();
        super.onStart();
    }

    @Override
    protected void onStop() {
        super.onStop();
        player.onPause();
    }

    @Override
    protected void onDestroy() {
        mPlayer.release();
        super.onDestroy();
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
    }
}
