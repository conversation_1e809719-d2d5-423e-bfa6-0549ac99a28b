package com.chat.laty.activity;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.chat.laty.R;
import com.chat.laty.adapter.NineGridViewAdapter;
import com.chat.laty.controllers.InviteController;
import com.chat.laty.entity.InviteBindRecordsInfoModel;
import com.chat.laty.entity.MediaFileModel;
import com.chat.laty.mvp.BasePresenterActivity;
import com.chat.laty.presenters.InvitePresenter;
import com.chat.laty.view.CustomActionBar;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import butterknife.BindView;

public class InviteBindRecordsActivity extends BasePresenterActivity<InvitePresenter> implements InviteController.IBindRecordsView {

    public static void startAct(Context context) {
        Intent intent = new Intent(context, InviteBindRecordsActivity.class);
        context.startActivity(intent);
    }

    @BindView(R.id.custom_bar)
    CustomActionBar customBar;

    @BindView(R.id.recycler_view)
    RecyclerView recyclerView;

    Adapter adapter;
    List<InviteBindRecordsInfoModel> list = new ArrayList<>();

    @Override
    protected void initDatas() {

    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_vip_recharge_records;
    }

    @Override
    protected void initViews() {
        customBar.setTitleText("申请记录");
        customBar.setAllColor(ContextCompat.getColor(this, R.color.black));

        customBar.setOnActionBarClickListerner(function -> {
            if (function == CustomActionBar.FUNCTION_BUTTON_LEFT) {
                InviteBindRecordsActivity.this.finish();
                return true;
            }
            return false;
        });

        adapter = new Adapter();
        adapter.setItems(list);
        recyclerView.setAdapter(adapter);
        presenter.getBindRecordsList();
    }

    @Override
    protected InvitePresenter setPresenter() {
        InvitePresenter presenter = new InvitePresenter(null);
        presenter.addView(this);
        return presenter;
    }


    @Override
    public FragmentActivity context() {
        return this;
    }


    @SuppressLint("NotifyDataSetChanged")
    @Override
    public void getRecordsSucceed(List<InviteBindRecordsInfoModel> list) {
        this.list = list;
        adapter.setItems(list);

        adapter.setStateView(getLayoutInflater().inflate(R.layout.community_empty_layout, null));
        adapter.setStateViewEnable(true);

        adapter.notifyDataSetChanged();
    }

    @Override
    public void getRecordsFailed() {
        adapter.setStateView(getLayoutInflater().inflate(R.layout.community_empty_layout, null));
        adapter.setStateViewEnable(true);
    }

    private static class Adapter extends BaseQuickAdapter<InviteBindRecordsInfoModel, QuickViewHolder> {

        @Override
        protected void onBindViewHolder(@NonNull QuickViewHolder helper, int i, @Nullable InviteBindRecordsInfoModel item) {
            helper.setText(R.id.tv_time, item.getCreateTime());
            helper.setText(R.id.tv_status, getStatus(item.getAuditStatus()));
            helper.setText(R.id.tv_user_id, item.getBindUserId());
            helper.setText(R.id.tv_note, item.getRemark());
//            NineGridImageView imageView = helper.getView(R.id.iv_nine_grid);
//            List<String> urls = new ArrayList<>();
//            if (!TextUtils.isEmpty(item.getFileUrl())) {
//                urls = Arrays.asList(item.getFileUrl().split(","));
//            }
//            imageView.setUrlList(urls);

            RecyclerView recycler = helper.getView(R.id.nine_grid);

            List<String> urls = new ArrayList<>();

            if (!TextUtils.isEmpty(item.getFileUrl())) {
                urls = Arrays.asList(item.getFileUrl().split(","));
            }

            final List<MediaFileModel> li = new ArrayList<>();

            for (String url : urls) {
                MediaFileModel model = new MediaFileModel(url, url);
                li.add(model);
            }

            GridLayoutManager manager = new GridLayoutManager(getContext(), li.size() > 2 ? 3 : li.size());
            recycler.setLayoutManager(manager);
            recycler.setNestedScrollingEnabled(false);

            NineGridViewAdapter nineGridViewAdapter = new NineGridViewAdapter(getContext());
            nineGridViewAdapter.setOnItemClickListener((baseQuickAdapter, view, index) -> {
                MediaFileModel model = li.get(index);
                nineGridViewAdapter.onClickListener(getContext(), model, li);
            });
            nineGridViewAdapter.setList(li);

            recycler.setAdapter(nineGridViewAdapter);
        }

        private String getStatus(String type) {
            switch (type) {
                case "2":
                    return "审核通过";
                case "3":
                    return "审核拒绝";
                default:
                    return "审核中";
            }
        }

        @NonNull
        @Override
        protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int i) {
            return new QuickViewHolder(R.layout.adapter_invite_bind_records_layout, viewGroup);
        }
    }
}