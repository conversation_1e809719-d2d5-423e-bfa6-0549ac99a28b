package com.chat.laty.activity;

import android.content.Context;

import androidx.recyclerview.widget.RecyclerView;

import com.allen.library.helper.ShapeBuilder;
import com.allen.library.shape.ShapeButton;
import com.chat.laty.R;
import com.chat.laty.adapter.GoldCoinRechargeGradeAdapter;
import com.chat.laty.adapter.GoldCoinRechargeTypeAdapter;
import com.chat.laty.base.BaseActivity;
import com.chat.laty.entity.RechargeGradeInfo;
import com.chat.laty.view.CustomActionBar;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;

/**
 * <AUTHOR>
 * @date 2023/12/16 19:59
 * @description:金币充值界面
 */
public class GoldCoinRechargeUI extends BaseActivity {

    @BindView(R.id.custom_bar)
    CustomActionBar mActionBar;

    @BindView(R.id.recharge_grade_rv)
    RecyclerView mRechargeGradeRv;

    @BindView(R.id.recharge_type_rv)
    RecyclerView mRechargeTypeRv;
    @BindView(R.id.submit_btn)
    ShapeButton mSubShapeButton;

    List<String> mRechargeTypeDatas = new ArrayList<>();
    List<RechargeGradeInfo> mRechargeGradeDatas = new ArrayList<>();

    GoldCoinRechargeTypeAdapter mRechargeTypeAdapter;
    GoldCoinRechargeGradeAdapter mRechargeGradeAdapter;

    public static void start(Context context) {
//        Intent intent = new Intent(context, GoldCoinRechargeUI.class);
//        context.startActivity(intent);
        GoldCoinActivity.startAct(context);
    }

    @Override
    protected void initDatas() {

    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_gold_coin_recharge_activity;
    }

    @Override
    protected void initViews() {
        mActionBar.setTitleText("金币充值");

        mRechargeTypeDatas.add("微信");
        mRechargeTypeDatas.add("支付宝");
        mRechargeTypeDatas.add("银联支付");
        mRechargeTypeDatas.add("回单支付");

        mRechargeTypeAdapter = new GoldCoinRechargeTypeAdapter();
        mRechargeTypeRv.setAdapter(mRechargeTypeAdapter);
        mRechargeTypeAdapter.setItems(mRechargeTypeDatas);

        mRechargeTypeAdapter.setOnItemClickListener((adapter, view, position) -> {
            mRechargeTypeAdapter.setSelectItem(position);
        });


        mRechargeGradeDatas.add(new RechargeGradeInfo("80金币", "¥ 8", "赠：80积分"));
        mRechargeGradeDatas.add(new RechargeGradeInfo("380金币", "¥ 38", "赠：380积分"));
        mRechargeGradeDatas.add(new RechargeGradeInfo("680金币", "¥ 68", "赠：680积分"));
        mRechargeGradeDatas.add(new RechargeGradeInfo("1680金币", "¥ 168", "赠：1680积分"));
        mRechargeGradeDatas.add(new RechargeGradeInfo("2880金币", "¥ 288", "赠：2880积分"));
        mRechargeGradeDatas.add(new RechargeGradeInfo("5880金币", "¥ 588", "赠：5880积分"));
        mRechargeGradeDatas.add(new RechargeGradeInfo("18880金币", "¥ 1888", "赠：18880积分"));
        mRechargeGradeDatas.add(new RechargeGradeInfo("28880金币", "¥ 2888", "赠：28880积分"));
        mRechargeGradeDatas.add(new RechargeGradeInfo("88888金币", "¥ 8888", "赠：88880积分"));
        mRechargeGradeAdapter = new GoldCoinRechargeGradeAdapter();
        mRechargeGradeRv.setAdapter(mRechargeGradeAdapter);

        mRechargeGradeAdapter.setItems(mRechargeGradeDatas);

        mRechargeGradeAdapter.setOnItemClickListener((adapter, view, position) -> {
            mRechargeGradeAdapter.setSelectItem(position);
        });

        ShapeBuilder shapeBuilder = mSubShapeButton.getShapeBuilder().setShapeSolidColor(R.color.red).setShapeUseSelector(true);
        mSubShapeButton.setShapeBuilder(shapeBuilder);


    }
}
