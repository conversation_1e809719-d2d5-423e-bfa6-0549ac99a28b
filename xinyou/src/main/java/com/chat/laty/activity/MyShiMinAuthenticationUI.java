package com.chat.laty.activity;

import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.view.View;

import androidx.fragment.app.FragmentActivity;

import com.chat.laty.entity.UserCenterInfo;
import com.hjq.toast.Toaster;
import com.chat.laty.R;
import com.chat.laty.controllers.AuthenticationController;
import com.chat.laty.entity.BaseBean;
import com.chat.laty.entity.BaseEntity;
import com.chat.laty.entity.UploadImgInfo;
import com.chat.laty.entity.event.InviteEvent;
import com.chat.laty.mvp.BasePresenterActivity;
import com.chat.laty.presenters.AuthenticationPresenter;
import com.chat.laty.view.ClearEditText;
import com.chat.laty.view.CustomActionBar;

import org.greenrobot.eventbus.EventBus;

import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * <AUTHOR>
 * @date 2024/1/12 10:10
 * @description:实名认证
 */
public class MyShiMinAuthenticationUI extends BasePresenterActivity<AuthenticationPresenter> implements AuthenticationController.View {
    @BindView(R.id.custom_bar)
    CustomActionBar customBar;
    @BindView(R.id.identifynum_cet)
    ClearEditText mIdentifyNumCet;

    @BindView(R.id.username_cet)
    ClearEditText mUserNameCet;

    public static void start(Context context) {
        Intent intent = new Intent(context, MyShiMinAuthenticationUI.class);
        context.startActivity(intent);
    }

    @Override
    protected void initDatas() {

    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_my_shimin_authentication_layout;
    }

    @Override
    protected void initViews() {
        customBar.setTitleText("实名认证");
    }

    @OnClick({R.id.submit_btn})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.submit_btn:
                submitData();
                break;
        }
    }

    private void submitData() {
        if (TextUtils.isEmpty(mIdentifyNumCet.getText().toString())) {
            Toaster.show("请输入身份证号码");
            return;
        }

        if (TextUtils.isEmpty(mUserNameCet.getText().toString())) {
            Toaster.show("请输入名字");
            return;
        }

        presenter.submitRealName(mIdentifyNumCet.getText().toString(), mUserNameCet.getText().toString());

    }

    @Override
    protected AuthenticationPresenter setPresenter() {
        return new AuthenticationPresenter(this);
    }

    @Override
    public FragmentActivity context() {
        return this;
    }

    @Override
    public void showRealNameCallback(BaseBean base) {
        dismissProgressDialog();
        if (200 != base.getCode()) {
            Toaster.show(base.getMessage());
        } else {
            Toaster.show("实名成功!");
            EventBus.getDefault().post(new InviteEvent());
            finish();
        }
    }

    @Override
    public void showUploadResult(List<UploadImgInfo> result) {

    }

    @Override
    public void showRealPersonCallback(BaseBean base) {

    }

    @Override
    public void showUserVerifyStatus(BaseEntity statuInfo) {

    }

    @Override
    public void showUserCenterDetails(UserCenterInfo data) {

    }
}
