package com.chat.laty.activity;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentActivity;

import com.hjq.toast.Toaster;
import com.chat.laty.R;
import com.chat.laty.controllers.CreditsController;
import com.chat.laty.entity.CreditsStoreInfoModel;
import com.chat.laty.mvp.BasePresenterActivity;
import com.chat.laty.presenters.CreditsStorePresenter;
import com.chat.laty.utils.PicassoUtils;
import com.chat.laty.view.CustomActionBar;

import butterknife.BindView;
import butterknife.OnClick;

public class CreditsStoreDetailActivity extends BasePresenterActivity<CreditsStorePresenter> implements CreditsController.ICreditsStoreDetailView {

    public static final String KEY_ARGS = "key_args_model";

    public static void startAct(Context context, CreditsStoreInfoModel.CreditStoreItemModel data) {

        Intent intent = new Intent(context, CreditsStoreDetailActivity.class);
        intent.putExtra(KEY_ARGS, data);
        context.startActivity(intent);
    }

    @BindView(R.id.custom_bar)
    CustomActionBar customBar;

    @BindView(R.id.tv_value)
    TextView tvValue;
    @BindView(R.id.tv_label)
    TextView tvLabel;
    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.tv_detail)
    TextView tvDetail;
    @BindView(R.id.tv_price)
    TextView tv_price;

    @BindView(R.id.iv_bg)
    ImageView imageView;
    @BindView(R.id.btn_confirm)
    Button confim;


    CreditsStoreInfoModel.CreditStoreItemModel model;

    @Override
    protected void initDatas() {
        if (getIntent() != null) {
            model = getIntent().getParcelableExtra(KEY_ARGS);
        }
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_credits_store_detail_layout;
    }


    @SuppressLint("SetTextI18n")
    @Override
    protected void initViews() {

        customBar.setTitleText("商品详情");
        customBar.setAllColor(ContextCompat.getColor(this, R.color.black));

        customBar.setOnActionBarClickListerner(function -> {
            if (function == CustomActionBar.FUNCTION_BUTTON_LEFT) {
                CreditsStoreDetailActivity.this.finish();
                return true;
            }
            return false;
        });


        if (model == null) return;
        tvValue.setText(model.getNumber() + "条");
        tvLabel.setText(model.msgType);
        tvTitle.setText(model.getName());
        tv_price.setText(model.getPrice() + " 积分");
        tvDetail.setText(model.getDetail());
        PicassoUtils.showImageWithGlide(this, imageView, model.getProductImg(), 4);
    }

    @Override
    protected CreditsStorePresenter setPresenter() {
        return new CreditsStorePresenter(null, null, this);
    }


    @Override
    public FragmentActivity context() {
        return this;
    }

    @OnClick(R.id.btn_confirm)
    public void onClick(View view) {
        if (model == null) return;
        showProgressDialog(R.string.app_loadding);
        presenter.exchange(model.getId());
    }


    @Override
    public void exchangeSucceed() {
        Toaster.show("兑换成功");
        confim.setText("继续兑换");
        setResult(RESULT_OK);
        dismissProgressDialog();
    }

    @Override
    public void exchangeFailed() {
        dismissProgressDialog();
    }
}