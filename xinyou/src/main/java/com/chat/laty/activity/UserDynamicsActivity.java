package com.chat.laty.activity;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;

import androidx.annotation.NonNull;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.hjq.toast.Toaster;
import com.lxj.xpopup.XPopup;
import com.lxj.xpopup.interfaces.OnConfirmListener;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshLoadMoreListener;
import com.chat.laty.R;
import com.chat.laty.adapter.UserDynamicsAdapter;
import com.chat.laty.base.XYApplication;
import com.chat.laty.controllers.CommunityController;
import com.chat.laty.entity.BaseBean;
import com.chat.laty.entity.CommunityInfoModel;
import com.chat.laty.mvp.BasePresenterActivity;
import com.chat.laty.presenters.CommunityPresenter;
import com.chat.laty.utils.KeyboardUtil;
import com.chat.laty.view.CustomActionBar;
import com.chat.laty.view.RecyclerItemDecoration;
import com.chat.laty.view.RotateAnimator;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import cn.xjc_soft.lib_utils.LibCollections;
import io.rong.imkit.utils.RouteUtils;
import io.rong.imlib.model.Conversation;
import io.rong.imlib.model.ConversationIdentifier;

/**
 * <AUTHOR>
 * @date 2024/1/27
 * @description:动态
 */
public class UserDynamicsActivity extends BasePresenterActivity<CommunityPresenter> implements CommunityController.IListView, CommunityController.IAccostView {
    private static final String KEY_TO_USER_ID = "_KEY_TO_USER_ID_";

    @BindView(R.id.custom_bar)
    CustomActionBar mActionBar;
    @BindView(R.id.smart_refresh_layout)
    SmartRefreshLayout refreshLayout;
    @BindView(R.id.recycler_view)
    RecyclerView recyclerView;

    @BindView(R.id.v_input)
    View vInput;
    @BindView(R.id.et_input)
    EditText etInput;
    @BindView(R.id.btn_send)
    Button send;

    UserDynamicsAdapter adapter;

    static final int request_code = 1008;

    private String mUserId;

    private int mDeletePosition = -1;

    List<CommunityInfoModel> list = new ArrayList<>();

    public static void start(Context context, String userId) {
        Intent intent = new Intent(context, UserDynamicsActivity.class);
        intent.putExtra(KEY_TO_USER_ID, userId);
        context.startActivity(intent);
    }

    @Override
    protected void initDatas() {

    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_user_dynamics_layout;
    }

    @Override
    public void initViews() {

        mUserId = getIntent().getStringExtra(KEY_TO_USER_ID);
        mActionBar.setTitleText("动态");
        mActionBar.setTitleTextColor(getResources().getColor(R.color.color_333333));

        PAGE_INDEX = MIN_PAGE_INDEX_1;
        adapter = new UserDynamicsAdapter(0);

        adapter.setAvatar(XYApplication.getCurrentUserInfo().getAvatar());

        adapter.setCallback(new UserDynamicsAdapter.OnItemCommentCallback() {
            @Override
            public void delete(CommunityInfoModel model, int index1, CommunityInfoModel.CommentInfoModel data) {
            }

            @Override
            public void replay(CommunityInfoModel model, int index1, CommunityInfoModel.CommentInfoModel data) {
                showInputDialogWithComment(model, index1, 1, data);
            }
        });
        recyclerView.setAdapter(adapter);
        recyclerView.addItemDecoration(new RecyclerItemDecoration());

        recyclerView.setOnTouchListener((v, event) -> {
            if (vInput.getVisibility() == View.VISIBLE) {
                toggleEditVisibility(View.GONE);
                return true;
            }
            return false;
        });

        adapter.setItems(list);
        adapter.setItemAnimation(BaseQuickAdapter.AnimationType.SlideInRight);
        adapter.setStateView(getLayoutInflater().inflate(R.layout.community_empty_layout, null));
        adapter.setStateViewEnable(true);


//        refreshLayout.setOnRefreshListener(refreshLayout -> presenter.getCommunityList(PAGE_INDEX, PAGE_SIZE, lat, lng, false, true, false));
//        refreshLayout.setOnLoadMoreListener(refreshLayout -> presenter.getCommunityList(PAGE_INDEX, PAGE_SIZE, lat, lng, false, false, true));
        refreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                PAGE_INDEX++;
                presenter.getUserCommunityList(PAGE_INDEX, mUserId);
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                PAGE_INDEX = MIN_PAGE_INDEX_1;
                presenter.getUserCommunityList(PAGE_INDEX, mUserId);
            }
        });

        adapter.setOnItemClickListener((baseQuickAdapter, view, i) -> {
            CommunityInfoModel item = baseQuickAdapter.getItem(i);
            MineDynamicDetailUI.startActForResult(this, item, 0, request_code);
            toggleEditVisibility(View.GONE);
        });

        // 头像
        adapter.addOnItemChildClickListener(R.id.iv_avatar, (baseQuickAdapter, view, i) -> {
            CommunityInfoModel model = baseQuickAdapter.getItem(i);
            UserCenterUI.start(this, model.getUserId());
        });

        // 私聊 / 搭讪
        adapter.addOnItemChildClickListener(R.id.v_interact, (baseQuickAdapter, view, i) -> {
            CommunityInfoModel model = baseQuickAdapter.getItem(i);
            String status = model.getIsAccost();
            if (TextUtils.equals("1", status)) {
//                ChatActivity.start(this, model.getUserId());
                ConversationIdentifier conversationIdentifier = new ConversationIdentifier(Conversation.ConversationType.PRIVATE, model.getUserId());
                RouteUtils.routeToConversationActivity(this, conversationIdentifier, false, null);

            } else {
                presenter.accostToUser(model.getUserId(), model, i);
            }
        });

        // 点赞
        adapter.addOnItemChildClickListener(R.id.v_collect, (baseQuickAdapter, view, i) -> {
            CommunityInfoModel model = baseQuickAdapter.getItem(i);
            presenter.likeToUser(model, i);
        });

        // 评论
        adapter.addOnItemChildClickListener(R.id.v_chat, (baseQuickAdapter, view, i) -> {
            CommunityInfoModel item = baseQuickAdapter.getItem(i);
            showInputDialogWithComment(item, i, 0, null);
        });

        adapter.addOnItemChildClickListener(R.id.tv_input, (baseQuickAdapter, view, i) -> {
            CommunityInfoModel item = baseQuickAdapter.getItem(i);
            showInputDialogWithComment(item, i, 0, null);
        });
        adapter.addOnItemChildClickListener(R.id.tv_delete, (baseQuickAdapter, view, position) -> {
            if (XYApplication.appLogin(true)) {

                new XPopup.Builder(UserDynamicsActivity.this)
                        .isDestroyOnDismiss(true) //对于只使用一次的弹窗，推荐设置这个
                        .customAnimator(new RotateAnimator())
                        .asConfirm("删除提示", "您正在删除本条动态，是否确定删除动态?", new OnConfirmListener() {
                            @Override
                            public void onConfirm() {
                                mDeletePosition = position;
                                CommunityInfoModel info = (CommunityInfoModel) baseQuickAdapter.getItem(position);
                                presenter.deleteDynamicsById(info.getId());
                            }
                        })
                        .show();

            }
        });

        presenter.getUserCommunityList(PAGE_INDEX, mUserId);
    }

    private void toggleEditVisibility(int status) {
        if (status == View.GONE) {
            if (vInput.getVisibility() == View.GONE) return;
            etInput.setText("");
            KeyboardUtil.hideSoftInput(this);
        } else if (status == View.VISIBLE) {
            if (vInput.getVisibility() == View.VISIBLE) return;
            etInput.requestFocus();
            KeyboardUtil.showSoftInput(this);
        }
        vInput.setVisibility(status);
    }


    // v_input
    // type: 0,直接评论 ，1：回复具体评论
    private void showInputDialogWithComment(CommunityInfoModel model, int index, int type, CommunityInfoModel.CommentInfoModel data) {
        if (vInput.getVisibility() == View.VISIBLE) return;
        String hint = "";
        if (type == 1) {
            hint = "回复 " + data.getUserName();
        }
        etInput.setHint(hint);
        toggleEditVisibility(View.VISIBLE);
        send.setOnClickListener(v -> {
            String content = etInput.getText().toString();
            if (content.isEmpty()) {
                Toaster.show("请输入评论内容");
                return;
            }
//            showProgressDialog(R.string.app_downloadding);
            presenter.reply(content, model, index, type, data);
        });
    }

    @SuppressLint("NotifyDataSetChanged")
    @Override
    public void getListSucceed(List<CommunityInfoModel> list, boolean load, boolean refresh, boolean loadmore) {

    }

    @Override
    public void getListFailed(String msg, boolean load, boolean refresh, boolean loadmore) {
        Toaster.show(msg);
        if (refresh) {
            refreshLayout.finishRefresh();
        } else if (loadmore) {
            refreshLayout.finishLoadMore();
        }
    }

    @Override
    public void getUserCommunitys(List<CommunityInfoModel> data) {
        refreshLayout.finishRefresh();
        refreshLayout.finishLoadMore();
        if (!LibCollections.isEmpty(data)) {
            if (!LibCollections.isEmpty(data)) {
                if (PAGE_INDEX == MIN_PAGE_INDEX_1) {
                    this.list = data;
                } else {
                    this.list.addAll(data);
                }

                adapter.setItems(this.list);
                adapter.notifyDataSetChanged();
            }
        }
        adapter.notifyDataSetChanged();
    }

    @Override
    public FragmentActivity context() {
        return this;
    }

    @Override
    public void accostSucceed(CommunityInfoModel data, int index) {
        data.setIsAccost("1");
        adapter.notifyItemChanged(index);
    }

    @Override
    public void likeSucceed(CommunityInfoModel data, int index) {
        if (TextUtils.equals("1", data.getIsLike())) {
            data.setIsLike("0");
            data.setLikeNum(data.getLikeNum() > 0 ? data.getLikeNum() - 1 : 0);
        } else {
            data.setIsLike("1");
            data.setLikeNum(data.getLikeNum() + 1);
        }
        adapter.notifyItemChanged(index);
    }

    @Override
    public void replySucceed(CommunityInfoModel model, int index, CommunityInfoModel.CommentInfoModel data) {
        presenter.update(model, model.getId(), index);
        toggleEditVisibility(View.GONE);


//        dismissProgressDialog();
//        sendCommentMsg(model.getUserId(), new XinYouCommentInfo(model.getTextContent(), "评论了你", "1", LibCollections.isEmpty(model.uploadImgInfos) ? "" : model.uploadImgInfos.get(0).getImgUrl(), model.getId()));
    }

    @Override
    public void updateSucceed(CommunityInfoModel model, int index) {
        if (this.list.size() > index) {
            this.list.set(index, model);
            adapter.notifyItemChanged(index);
            refreshLayout.autoRefresh();
        }
    }

    @Override
    public void failed() {
        dismissProgressDialog();
    }

    @Override
    public void deleteCallback(BaseBean callbackBean) {
        if (200 == callbackBean.getCode()) {
            adapter.removeAt(mDeletePosition);
        }
    }

    @Override
    protected CommunityPresenter setPresenter() {
        return new CommunityPresenter(this, this);
    }
}
