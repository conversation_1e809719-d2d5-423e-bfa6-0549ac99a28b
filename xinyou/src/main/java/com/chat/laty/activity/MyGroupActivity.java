package com.chat.laty.activity;

import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.viewpager2.adapter.FragmentStateAdapter;
import androidx.viewpager2.widget.ViewPager2;

import com.google.android.material.tabs.TabLayout;
import com.chat.laty.R;
import com.chat.laty.adapter.MyGroupMoreAdapter;
import com.chat.laty.controllers.MyGroupController;
import com.chat.laty.dialog.BottomDatePickerDialog;
import com.chat.laty.entity.MyGroupInfoModel;
import com.chat.laty.fragment.MyGroupFragment;
import com.chat.laty.mvp.BasePresenterActivity;
import com.chat.laty.presenters.MyGroupPresenter;
import com.chat.laty.utils.PicassoUtils;
import com.chat.laty.view.CustomActionBar;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * K修改日志 2024年10月17日
 */
public class MyGroupActivity extends BasePresenterActivity<MyGroupPresenter> implements MyGroupController.IInfoView {

    public static void startAct(Context context) {
        Intent intent = new Intent(context, MyGroupActivity.class);
        context.startActivity(intent);
    }

    @BindView(R.id.custom_bar)
    CustomActionBar customBar;

    @BindView(R.id.iv_avatar)
    ImageView ivAvatar;
    @BindView(R.id.tv_user_name)
    TextView tvUserName;
    @BindView(R.id.tv_vip_label)
    TextView tvVipLabel;
    @BindView(R.id.tv_filter)
    TextView tvFilter;

    @BindView(R.id.tv_dividedAmountNum)
    TextView tvVidedAmountNum;

    @BindView(R.id.userAmountNumLayout)
    LinearLayout userAmountNumLayout;

    @BindView(R.id.tv_divteamRechargeTotalAmount)
    TextView tvTeamRechargeTotalAmount;

    @BindView(R.id.userAmountNumLayout2)
    LinearLayout userAmountNumLayout2;

    @BindView(R.id.tab_layout)
    TabLayout tabLayout;

    @BindView(R.id.view_pager)
    ViewPager2 viewPager2;

    @Override
    protected void initDatas() {

    }

    private int index = 0;

    @OnClick({R.id.v_search_view, R.id.tv_filter})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.v_search_view:
                MyGroupSearchActivity.startAct(this, index + 1, model.getDbnLevel(), model.getUserId());
                break;
            case R.id.tv_filter:
                BottomDatePickerDialog dialog = new BottomDatePickerDialog(this);
                dialog.setOnDatePickerListener(date -> update(date));
                dialog.show();
                break;
        }
    }

    public void update(String date) {
        if (date != null) {
            tvFilter.setText(date);
            showProgressDialog(R.string.app_loadding);
            presenter.getMyGroupInfo(date);
        } else {
            if (TextUtils.equals("今天", tvFilter.getText().toString())) return;
            tvFilter.setText("今天");
            presenter.getMyGroupInfo(null);
        }
    }


    @Override
    protected int getContentViewId() {
        return R.layout.activity_my_group_layout;
    }

    MyGroupInfoModel model;

    List<MyGroupFragment> fragments = new ArrayList<>();

    ArrayList<String> titles = new ArrayList<>(Arrays.asList("直推", "团队"));

    @Override
    protected void initViews() {

        customBar.setTitleText("我的团队");
        customBar.setAllColor(ContextCompat.getColor(this, R.color.white));

        customBar.setOnActionBarClickListerner(function -> {
            if (function == CustomActionBar.FUNCTION_BUTTON_LEFT) {
                MyGroupActivity.this.finish();
                return true;
            }
            return false;
        });
        presenter.getMyGroupInfo(null);
    }

    private void toggleTextStyle(View view, boolean selected) {

        TextView textView = view.findViewById(R.id.tv_title);
        ImageView imageView = view.findViewById(R.id.iv_indicator);

        if (textView != null) {
            textView.setSelected(selected);
            textView.setTextSize(selected ? 18 : 15);
        }
        if (imageView != null) {
            imageView.setVisibility(selected ? View.VISIBLE : View.INVISIBLE);
        }
    }

    @Override
    protected MyGroupPresenter setPresenter() {
        return new MyGroupPresenter(this, null);
    }

    @Override
    public void getInfoSucceed(MyGroupInfoModel data, boolean filter, String date) {
        this.model = data;

        if (filter) {
            for (MyGroupFragment fragment : fragments) {
                fragment.setData(data, date);
            }
            dismissProgressDialog();
            return;
        }

        PicassoUtils.showImage(ivAvatar, data.getAvatar());
        tvUserName.setText(data.getNickname());
        tvVipLabel.setText(MyGroupMoreAdapter.getLabel(data.getDbnLevel()));

        if (TextUtils.isEmpty(data.getTheTotalAmountRechargedByTheTeamToday()) && TextUtils.isEmpty(data.getTeamRechargeTotalAmount())) {
            userAmountNumLayout.setVisibility(View.GONE);
            userAmountNumLayout2.setVisibility(View.GONE);
        } else {
            userAmountNumLayout.setVisibility(View.VISIBLE);
            userAmountNumLayout2.setVisibility(View.VISIBLE);
            tvVidedAmountNum.setText("¥:" + data.getTheTotalAmountRechargedByTheTeamToday());
            tvTeamRechargeTotalAmount.setText("¥:" + data.getTeamRechargeTotalAmount());
        }


        titles.set(0, "直推(" + data.getTotalDirectNum() + ")");
        titles.set(1, "团队(" + data.getTotalTeamNum() + ")");

        tabLayout.removeAllTabs();
        fragments.clear();

        for (int i = 0; i < titles.size(); i++) {
            View view = getLayoutInflater().inflate(R.layout.tab_layout_item_view, null);

            TextView textView = view.findViewById(R.id.tv_title);
            ImageView imageView = view.findViewById(R.id.iv_indicator);
            if (textView != null) {
                textView.setText(titles.get(i));
                toggleTextStyle(textView, i == 0);
            }
            if (imageView != null && i == 0) {
                imageView.setVisibility(View.VISIBLE);
            }

            TabLayout.Tab tab = tabLayout.newTab();
            tab.setCustomView(view);
            tabLayout.addTab(tab);

            fragments.add(MyGroupFragment.newInstance(i + 1, data));
        }

        tabLayout.addOnTabSelectedListener(new TabLayout.OnTabSelectedListener() {
            @Override
            public void onTabSelected(TabLayout.Tab tab) {
                toggleTextStyle(tab.getCustomView(), true);
                viewPager2.setCurrentItem(tab.getPosition());
                index = tab.getPosition();
            }

            @Override
            public void onTabUnselected(TabLayout.Tab tab) {
                toggleTextStyle(tab.getCustomView(), false);
            }

            @Override
            public void onTabReselected(TabLayout.Tab tab) {

            }
        });

        viewPager2.setAdapter(new Adapter(this));
        viewPager2.setUserInputEnabled(false);
    }

    @Override
    public void getInfoFailed(boolean filter) {
        dismissProgressDialog();
    }

    @Override
    public FragmentActivity context() {
        return this;
    }

    private class Adapter extends FragmentStateAdapter {


        public Adapter(@NonNull FragmentActivity fragmentActivity) {
            super(fragmentActivity);
        }

        @NonNull
        @Override
        public Fragment createFragment(int position) {
            return fragments.get(position);
        }

        @Override
        public int getItemCount() {
            return fragments.size();
        }
    }
}