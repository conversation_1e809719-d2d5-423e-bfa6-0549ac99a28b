package com.chat.laty.activity;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.RecyclerView;

import com.allen.library.SuperTextView;
import com.hjq.permissions.OnPermissionCallback;
import com.hjq.permissions.Permission;
import com.hjq.permissions.XXPermissions;
import com.hjq.toast.Toaster;
import com.luck.picture.lib.basic.PictureSelector;
import com.luck.picture.lib.config.PictureConfig;
import com.luck.picture.lib.entity.LocalMedia;
import com.makeramen.roundedimageview.RoundedImageView;
import com.chat.laty.utils.PermissionInterceptor;
import com.yalantis.ucrop.UCrop;
import com.chat.laty.R;
import com.chat.laty.adapter.AddPicAdapter;
import com.chat.laty.base.AppHelper;
import com.chat.laty.controllers.UserOptController;
import com.chat.laty.dialog.BottomAdressDialog;
import com.chat.laty.dialog.BottomBirthdayDialog;
import com.chat.laty.dialog.BottomChoiceDialog;
import com.chat.laty.dialog.BottomRecordSoundDialog;
import com.chat.laty.dialog.BottomSignWheelViewDialog;
import com.chat.laty.dialog.BottomStageDialog;
import com.chat.laty.entity.AddressInfo;
import com.chat.laty.entity.BaseBean;
import com.chat.laty.entity.DiaplayPicInfo;
import com.chat.laty.entity.EditUserInfo;
import com.chat.laty.entity.LabelInfo;
import com.chat.laty.entity.PickerBean;
import com.chat.laty.entity.PickerViewBean;
import com.chat.laty.entity.ShenHeInfo;
import com.chat.laty.entity.UploadImgInfo;
import com.chat.laty.mvp.BasePresenterActivity;
import com.chat.laty.presenters.UserOptPresenter;
import com.chat.laty.utils.BgmPlayer;
import com.chat.laty.utils.LogUtil;
import com.chat.laty.utils.PicassoUtils;
import com.chat.laty.view.ClearEditText;
import com.chat.laty.view.LableLineBreak;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

import butterknife.BindView;
import butterknife.OnClick;
import cn.xjc_soft.lib_utils.LibCollections;
import io.rong.imkit.RongIM;
import io.rong.imlib.model.UserInfo;

/**
 * <AUTHOR>
 * @date 2023/12/15 9:00
 * @description:个人信息
 */
public class ImproveInformationUI extends BasePresenterActivity<UserOptPresenter> implements UserOptController.View {

    @BindView(R.id.edit_nickname)
    ClearEditText mUserNickNameCet;
    @BindView(R.id.user_name_tv)
    AppCompatTextView mUserNameTv;

    @BindView(R.id.user_shenhe_tv)
    AppCompatTextView mUserShenHeTv;
    @BindView(R.id.yuyin_status)
    AppCompatTextView mYuyinShenHeTv;

    @BindView(R.id.friendly_voice_tv)
    AppCompatTextView mFriendlyVoiceTv;
    @BindView(R.id.user_head_riv)
    RoundedImageView mUserHeadRiv;

    @BindView(R.id.stage_lb)
    LableLineBreak mLableLineBreak;

    @BindView(R.id.make_friends_llb)
    LableLineBreak mMakeFriendsLLB;

    @BindView(R.id.birthday_stv)
    SuperTextView mBirthdayStv;
    @BindView(R.id.height_stv)
    SuperTextView mHeightStv;
    @BindView(R.id.weight_stv)
    SuperTextView mWeightStv;

    @BindView(R.id.education_stv)
    SuperTextView mEducationStv;

    @BindView(R.id.career_stv)
    SuperTextView mCareerStv;

    @BindView(R.id.annual_income_stv)
    SuperTextView mAnnualIncomeStv;

    @BindView(R.id.emotional_state_stv)
    SuperTextView mEmotionalStateStv;
    @BindView(R.id.residential_situation_stv)
    SuperTextView mResidentialSituationStv;

    @BindView(R.id.address_stv)
    SuperTextView mAddressStv;

    @BindView(R.id.user_audio_stv)
    SuperTextView mUserAudioStv;

    @BindView(R.id.declaration_edit)
    ClearEditText mDeclarationDt;

    @BindView(R.id.rv_images)
    RecyclerView rvImages;

    @BindView(R.id.pic_status_tv)
    AppCompatTextView picStatusTv;
    @BindView(R.id.declaration_statu_tv)
    AppCompatTextView declarationStatusTv;

    AddPicAdapter mAdapter;


    BottomStageDialog mBottomStageDialog;//个性标签

    BottomSignWheelViewDialog mBottomWheelDialog;
    BottomChoiceDialog mBottomChoiceDialog;
    List<PickerBean> mSignWheelDatas = new ArrayList<>();

    String avatarUrl;
    String coverUrl;

    PickerViewBean mPickerViewInfo;

    List<String> mSelectList = new ArrayList<>();

    private int mPicType = -1;

    String mAudioPath;
    String mAudioLength;

    private BgmPlayer mBgmPlayer;

    private EditUserInfo mUserInfo;

    private boolean updateStatus = false;

    private List<String> mChoiceImpressionLabels = new ArrayList<>();
    private List<String> mmMakeFriendsLabels = new ArrayList<>();

    private boolean mChanged = false;//

    public static void start(Context context) {
        Intent intent = new Intent(context, ImproveInformationUI.class);
        context.startActivity(intent);
    }

    @Override
    protected void initDatas() {

    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_improve_infomation_layout;
    }

    @Override
    protected void initViews() {
        presenter.getEditInfo();
        mAdapter = new AddPicAdapter();
        rvImages.setAdapter(mAdapter);

        mSelectList.add("");
        mAdapter.setItems(mSelectList);

        mAdapter.addOnItemChildClickListener(R.id.add_layout, (baseQuickAdapter, view, position) -> {
            XXPermissions.with(ImproveInformationUI.this).permission(Permission.READ_MEDIA_IMAGES).interceptor(new PermissionInterceptor()).interceptor(new PermissionInterceptor()).request(new OnPermissionCallback() {
                @Override
                public void onGranted(List<String> permissions, boolean all) {
                    if (!all) {
                        return;
                    }
                    mPicType = 2;
                    cropPic();
                }
            });
        });
        mAdapter.showDelete(true);

        mAdapter.addOnItemChildClickListener(R.id.iv_thum, (baseQuickAdapter, view, position) -> {
            setPicCoverDialog(position);
        });
        mAdapter.addOnItemChildClickListener(R.id.delete_tv, (baseQuickAdapter, view, position) -> {
            mSelectList.remove(position);

            mSelectList = LibCollections.removeEmptyStrings(mSelectList);
            if (mSelectList.size() < 9) {
                mSelectList.add("");
            }
            mAdapter.setItems(mSelectList);
            mAdapter.notifyDataSetChanged();
        });
        mUserNickNameCet.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void afterTextChanged(Editable editable) {
                mChanged = !TextUtils.equals(mUserInfo.getNickname(), editable.toString());
            }
        });

        mDeclarationDt.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void afterTextChanged(Editable editable) {
                mChanged = !TextUtils.equals(mUserInfo.getMakeFriendsDetail(), editable.toString());
            }
        });

    }

    @OnClick({R.id.address_stv, R.id.make_friends_stv, R.id.residential_situation_stv, R.id.emotional_state_stv, R.id.user_head_riv, R.id.submit_btn, R.id.personality_tag_stv, R.id.birthday_stv, R.id.height_stv, R.id.weight_stv, R.id.education_stv, R.id.career_stv, R.id.annual_income_stv, R.id.user_audio_stv, R.id.friendly_voice_tv})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.user_head_riv:
                XXPermissions.with(ImproveInformationUI.this).permission(Permission.READ_MEDIA_IMAGES,Permission.CAMERA).interceptor(new PermissionInterceptor()).request(new OnPermissionCallback() {
                    @Override
                    public void onGranted(List<String> permissions, boolean all) {
                        if (!all) {
                            return;
                        }
                        mPicType = 1;
                        cropPic();
                    }
                });
                break;

            case R.id.address_stv:
                presenter.getAddress(ImproveInformationUI.this);
                break;

            case R.id.personality_tag_stv:
                showStageDialog(1, mLableLineBreak);
                break;

            case R.id.make_friends_stv:
                showStageDialog(2, mMakeFriendsLLB);
                break;

            case R.id.birthday_stv:
                showBirthdayDialog(mBirthdayStv);
                break;

            case R.id.height_stv:
                showSignWheelDialog(1, mHeightStv);
                break;

            case R.id.weight_stv:
                showSignWheelDialog(2, mWeightStv);
                break;

            case R.id.education_stv:
                showSignWheelDialog(3, mEducationStv);
                break;
            case R.id.career_stv:
                showSignWheelDialog(4, mCareerStv);
                break;

            case R.id.annual_income_stv:
                showSignWheelDialog(5, mAnnualIncomeStv);
                break;

            case R.id.emotional_state_stv:
                showSignWheelDialog(6, mEmotionalStateStv);
                break;

            case R.id.residential_situation_stv:
                showSignWheelDialog(7, mResidentialSituationStv);
                break;

            case R.id.user_audio_stv:
                showRecordSoundDialog();
                break;

            case R.id.submit_btn:
                submitUserData();
                break;

            case R.id.friendly_voice_tv:
                if (!TextUtils.isEmpty(mAudioPath)) {
                    mBgmPlayer = BgmPlayer.getInstance(ImproveInformationUI.this);
                    if (mBgmPlayer.isPlay())
                        mBgmPlayer.stop();
                    mBgmPlayer.playNet(mAudioPath, mFriendlyVoiceTv);
                }
                break;
        }
    }

    private void submitUserData() {
        if (!mChanged) {
            Toaster.show("内容无修改");
            return;
        }
        HashMap<String, Object> params = new HashMap<>();
        if (TextUtils.isEmpty(mUserNickNameCet.getText().toString())) {
            Toaster.show("请输入新的昵称");
            return;
        }
        params.put("nickname", mUserNickNameCet.getText().toString().replace("(审核中)", "").replace("(已通过)", "").replace("(违规被拒)", ""));

        if (!LibCollections.isEmpty(mLableLineBreak.getAllLabels())) {
            params.put("impressionLabels", mLableLineBreak.getAllLabels().toString().replace("[", "").replace("]", "").replace(" ", ""));
        }

        if (!LibCollections.isEmpty(mMakeFriendsLLB.getAllLabels())) {
            params.put("loveFriendLabels", mMakeFriendsLLB.getAllLabels().toString().replace("[", "").replace("]", "").replace(" ", ""));
        }

        if (TextUtils.isEmpty(mBirthdayStv.getRightString()) && !"请选择".equals(mBirthdayStv.getRightString())) {
            Toaster.show("请选择出生日期");
            return;
        }

        params.put("birthday", mBirthdayStv.getRightString());

        if (!TextUtils.isEmpty(mHeightStv.getRightString()) && !"请选择".equals(mHeightStv.getRightString())) {
            params.put("height", mHeightStv.getRightString());
        }
        if (!TextUtils.isEmpty(mWeightStv.getRightString()) && !"请选择".equals(mWeightStv.getRightString())) {
            params.put("weight", mWeightStv.getRightString());
        }
        if (!TextUtils.isEmpty(mEducationStv.getRightString()) && !"请选择".equals(mEducationStv.getRightString())) {
            params.put("educationName", mEducationStv.getRightString());
        }
        if (!TextUtils.isEmpty(mCareerStv.getRightString()) && !"请选择".equals(mCareerStv.getRightString())) {
            params.put("careerName", mCareerStv.getRightString());
        }
        if (!TextUtils.isEmpty(mAnnualIncomeStv.getRightString()) && !"请选择".equals(mAnnualIncomeStv.getRightString())) {
            params.put("incomeName", mAnnualIncomeStv.getRightString());
        }
        if (!TextUtils.isEmpty(mEmotionalStateStv.getRightString()) && !"请选择".equals(mEmotionalStateStv.getRightString())) {
            params.put("emotionStatus", mEmotionalStateStv.getRightString());
        }
        if (!TextUtils.isEmpty(mResidentialSituationStv.getRightString()) && !"请选择".equals(mResidentialSituationStv.getRightString())) {
            params.put("liveStatus", mResidentialSituationStv.getRightString());
        }
        if (!TextUtils.isEmpty(avatarUrl)) {
            params.put("avatar", avatarUrl);
        }
        if (!TextUtils.isEmpty(mDeclarationDt.getText().toString())) {
            params.put("makeFriendsDetail", mDeclarationDt.getText().toString());
        }

        if (!LibCollections.isEmpty(mSelectList)) {
            params.put("photoList", LibCollections.removeEmptyStrings(mSelectList).toString().replace("[", "").replace("]", "").replace(" ", ""));
//                    params.put("photoList", mDeclarationDt.getText().toString());
        }
        if (!TextUtils.isEmpty(coverUrl)) {
            params.put("coverUrl", coverUrl);
        }

        if (!TextUtils.isEmpty(mAddressStv.getRightString()) && !"请选择".equals(mAddressStv.getRightString())) {
            params.put("liveAddress", mAddressStv.getRightString());
        }
        if (!TextUtils.isEmpty(mAudioPath)) {
            params.put("audioUrl", mAudioPath);
            params.put("audioLength", mAudioLength);
        }

        updateStatus = true;
        presenter.updateUserInfo(params);
    }


    @Override
    public void onActivityResult(int requestCode, int resultCode, @Nullable @org.jetbrains.annotations.Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK) {
            switch (requestCode) {
                case UCrop.REQUEST_CROP:

//                    showProgressDialog(R.string.app_uploadding);
//                    final Uri resultUri = UCrop.getOutput(data);
//                    LogUtil.i("TAG", "裁剪回调的地址===》 " + resultUri);
//                    presenter.uploadHeadImgs(resultUri.getPath());
                    break;
                case PictureConfig.CHOOSE_REQUEST:
                    if (mPicType == 1) {
                        showProgressDialog(R.string.app_uploadding);
                        final Uri resultUri = UCrop.getOutput(data);
                        LogUtil.i("TAG", "裁剪回调的地址===》 " + resultUri);
                        List<LocalMedia> selectList = PictureSelector.obtainSelectorList(data);
                        LogUtil.i("TAG", "裁剪回调的地址1111111111===》 " + resultUri);
                        presenter.uploadFiles(selectList);
                    } else {
                        showProgressDialog(R.string.app_uploadding);
                        List<LocalMedia> selectList = PictureSelector.obtainSelectorList(data);
                        presenter.uploadFiles(selectList);
                    }
                    break;
            }
        }
    }

    @Override
    protected UserOptPresenter setPresenter() {
        return new UserOptPresenter(this);
    }

    @Override
    public FragmentActivity context() {
        return getThisActivity();
    }

    @Override
    public void showUploadResult(List<UploadImgInfo> result) {

    }

    @RequiresApi(api = Build.VERSION_CODES.N)
    @Override
    public void showLabels(int type, LabelInfo data) {
        List<String> labelListTepm = data.getLabelList();
        if (1 == type) {
            labelListTepm.addAll(mChoiceImpressionLabels);
        } else if (2 == type) {
            labelListTepm.addAll(mmMakeFriendsLabels);
        }
        mBottomStageDialog.setNewDatas(labelListTepm.stream().distinct().collect(Collectors.toList()));
    }

    @Override
    public void showDetails(PickerViewBean data) {
        mPickerViewInfo = data;
        dismissProgressDialog();

        showUserDetailsInfo(data.getUserInfo());
    }

    @Override
    public void updateCallback(BaseBean callbackBean) {
        if (updateStatus) {
            Toaster.show(callbackBean.getMessage());
            RongIM.getInstance().refreshUserInfoCache(new UserInfo(mUserInfo.getUserId(), mUserInfo.getNickname(), Uri.parse(mUserInfo.getAvatar())));
            if (200 == callbackBean.getCode())
                finish();
        } else {
            Toaster.show(callbackBean.getMessage());
            presenter.getEditInfo();
        }
    }

    @Override
    public void showAddress(List<AddressInfo> addressInfos) {
        showBottomAddressDialog(addressInfos, mAddressStv);
    }

    @Override
    public void showUploadAudioResult(List<String> result) {
        if (!LibCollections.isEmpty(result) && !TextUtils.isEmpty(result.get(0))) {
            mChanged = true;
            mAudioPath = result.get(0);
            mUserAudioStv.setRightString("已上传");

            HashMap<String, Object> params = new HashMap<>();
            if (!TextUtils.isEmpty(mAudioPath)) {
                params.put("audioUrl", mAudioPath);
                params.put("audioLength", mAudioLength);
            }
            updateStatus = false;
            presenter.updateUserInfo(params);
        }
    }

    @Override
    public void showUploadResults(List<String> result) {
        dismissProgressDialog();
        mChanged = true;
        if (mPicType == 1) {
            avatarUrl = result.get(0);
            PicassoUtils.showImage(mUserHeadRiv, avatarUrl);
            RongIM.getInstance().refreshUserInfoCache(new UserInfo(mUserInfo.getUserId(), mUserInfo.getNickname(), Uri.parse(avatarUrl)));
            HashMap<String, Object> params = new HashMap<>();
            params.put("avatar", avatarUrl);
            updateStatus = false;
            presenter.updateUserInfo(params);
        } else if (mPicType == 2) {
            mSelectList = LibCollections.removeEmptyStrings(mSelectList);
            for (String info : result) {
                mSelectList.add(info);
            }
            if (mSelectList.size() < 9) {
                mSelectList.add("");
            }
            mAdapter.setItems(mSelectList);
            mAdapter.notifyDataSetChanged();

//            HashMap<String, Object> params = new HashMap<>();
//            updateStatus = false;
//            if (!LibCollections.isEmpty(mSelectList)) {
//                params.put("photoList", LibCollections.removeEmptyStrings(mSelectList).toString().replace("[", "").replace("]", "").replace(" ", ""));
////                    params.put("photoList", mDeclarationDt.getText().toString());
//            }
//            presenter.updateUserInfo(params);
        }
    }

    private void showUserDetailsInfo(EditUserInfo userInfo) {
        if (null != userInfo) {
            mUserInfo = userInfo;
//            mUserNameTv.setText(userInfo.getNickname());
            mUserNickNameCet.setText(userInfo.getNickname());
            if (!TextUtils.isEmpty(userInfo.getImpressionLabels())) {
                for (String impressionLabel : userInfo.getImpressionLabels().split(",")) {

                    if (!mChoiceImpressionLabels.contains(impressionLabel)) {
                        mChoiceImpressionLabels.add(impressionLabel);
                    }
                }
//                ArrayList<String>  labDataTemp= new ArrayList<>();
//                List<String> labTemp = Arrays.asList(userInfo.getImpressionLabels().split(","));
//                for (String temp:labTemp ) {
//                    labDataTemp.add(temp);
//                }
                mLableLineBreak.setLabels(Arrays.asList(userInfo.getImpressionLabels().split(",")), false);

            }

            if (!TextUtils.isEmpty(userInfo.getLoveFriendLabels())) {
                for (String loveFriend : userInfo.getLoveFriendLabels().split(",")) {
                    if (!mmMakeFriendsLabels.contains(loveFriend)) {
                        mmMakeFriendsLabels.add(loveFriend);
                    }
                }
                mMakeFriendsLLB.setLabels(Arrays.asList(userInfo.getLoveFriendLabels().split(",")), false);
            }

            if (!TextUtils.isEmpty(userInfo.getBirthday()))
                mBirthdayStv.setRightString(userInfo.getBirthday());

            if (!TextUtils.isEmpty(userInfo.getHeight()))
                mHeightStv.setRightString(userInfo.getHeight());

            if (!TextUtils.isEmpty(userInfo.getWeight()))
                mWeightStv.setRightString(userInfo.getWeight());

            if (!TextUtils.isEmpty(userInfo.getEducationName()))
                mEducationStv.setRightString(userInfo.getEducationName());

            if (!TextUtils.isEmpty(userInfo.getCareerName()))
                mCareerStv.setRightString(userInfo.getCareerName());

            if (!TextUtils.isEmpty(userInfo.getIncomeName()))
                mAnnualIncomeStv.setRightString(userInfo.getIncomeName());

            if (!TextUtils.isEmpty(userInfo.getEmotionStatus()))
                mEmotionalStateStv.setRightString(userInfo.getEmotionStatus());

            if (!TextUtils.isEmpty(userInfo.getLiveStatus()))
                mResidentialSituationStv.setRightString(userInfo.getLiveStatus());

            if (!TextUtils.isEmpty(userInfo.getLiveAddress()))
                mAddressStv.setRightString(userInfo.getLiveAddress());

            if (!TextUtils.isEmpty(userInfo.getMakeFriendsDetail()))
                mDeclarationDt.setText(userInfo.getMakeFriendsDetail());

            if (!TextUtils.isEmpty(userInfo.getHideLocation()) && "1".equals(userInfo.getHideLocation())) {
                mAddressStv.setVisibility(View.GONE);
            } else {
                mAddressStv.setVisibility(View.VISIBLE);
            }

            PicassoUtils.showImage(mUserHeadRiv, userInfo.getAvatar());


            if (!LibCollections.isEmpty(mUserInfo.getAuditList())) {
                for (ShenHeInfo sheheInfo : mUserInfo.getAuditList()) {
                    if (TextUtils.equals("1", sheheInfo.getAuditType())) {
                        mUserShenHeTv.setText((TextUtils.equals("1", sheheInfo.getStatus()) ? "(审核中)" : TextUtils.equals("2", sheheInfo.getStatus()) ? "(已通过)" : "(违规被拒)"));
                    }

                    if (TextUtils.equals("3", sheheInfo.getAuditType())) {
                        mYuyinShenHeTv.setText(TextUtils.equals("1", sheheInfo.getStatus()) ? "(审核中)" : TextUtils.equals("2", sheheInfo.getStatus()) ? "(已通过)" : "(违规被拒)");
                    }

                    if (TextUtils.equals("2", sheheInfo.getAuditType())) {
                        mUserNickNameCet.setText(mUserNickNameCet.getText() + (TextUtils.equals("1", sheheInfo.getStatus()) ? "(审核中)" : TextUtils.equals("2", sheheInfo.getStatus()) ? "(已通过)" : "(违规被拒)"));
                    }

                    if (TextUtils.equals("4", sheheInfo.getAuditType())) {
                        picStatusTv.setText((TextUtils.equals("1", sheheInfo.getStatus()) ? "(审核中)" : TextUtils.equals("2", sheheInfo.getStatus()) ? "(已通过)" : "(违规被拒)"));
                    }

                    if (TextUtils.equals("5", sheheInfo.getAuditType())) {
                        declarationStatusTv.setText((TextUtils.equals("1", sheheInfo.getStatus()) ? "(审核中)" : TextUtils.equals("2", sheheInfo.getStatus()) ? "(已通过)" : "(违规被拒)"));
                    }
                }
            }

            if (!TextUtils.isEmpty(userInfo.getPhotoList())) {
                List<String> photosTemp = Arrays.asList(userInfo.getPhotoList().split(","));
                mSelectList = LibCollections.removeEmptyStrings(mSelectList);
                for (String photo : photosTemp) {
                    mSelectList.add(photo);
                }
                if (mSelectList.size() < 9) {
                    mSelectList.add("");
                }
                mAdapter.setItems(mSelectList);
                mAdapter.notifyDataSetChanged();
            }

            if (!TextUtils.isEmpty(userInfo.getAudioUrl())) {
                mAudioPath = userInfo.getAudioUrl();
                mAudioLength = userInfo.getAudioLength();
                mFriendlyVoiceTv.setText(mAudioLength + "s");
                mUserAudioStv.setRightString("已上传");
            }
        }
    }

    private void showStageDialog(int type, LableLineBreak lableLineBreak) {
        mBottomStageDialog = new BottomStageDialog(ImproveInformationUI.this);

        mBottomStageDialog.setOnDialogCallbackListener(new BottomStageDialog.OnDialogCallbackListener() {
            @Override
            public void onDialogRefresh() {
                presenter.getLabelList(type);
            }

            @Override
            public void onChoiceCallback(List<String> selects) {
                lableLineBreak.setLabels(selects, true);

                mChanged = true;
//                for (String impressionLabel : selects) {
                if (1 == type) {
                    mChoiceImpressionLabels.clear();
                    mChoiceImpressionLabels.addAll(selects);
//                        if (!mChoiceImpressionLabels.contains(impressionLabel)) {
//                            mChoiceImpressionLabels.add(impressionLabel);
//                        }
                    mLableLineBreak.setLabels(mChoiceImpressionLabels, false);
                } else if (2 == type) {
                    mmMakeFriendsLabels.clear();
                    mmMakeFriendsLabels.addAll(selects);
//                        if (!mmMakeFriendsLabels.contains(impressionLabel)) {
//                            mmMakeFriendsLabels.add(impressionLabel);
//                        }
                    mMakeFriendsLLB.setLabels(mmMakeFriendsLabels, false);
                }
//                }
            }
        });

        mBottomStageDialog.setTitleTv("请选择");
        mBottomStageDialog.show();

        if (1 == type) {
            mBottomStageDialog.setSelects(mChoiceImpressionLabels);
        } else if (2 == type) {
            mBottomStageDialog.setSelects(mmMakeFriendsLabels);
        }
    }

    private void showBirthdayDialog(SuperTextView tv) {
        BottomBirthdayDialog dialog = new BottomBirthdayDialog(ImproveInformationUI.this);
        dialog.setOnDialogCallbackListener(new BottomBirthdayDialog.OnDialogCallbackListener() {
            @Override
            public void onDialogSelectItem(int year, int month, int day) {
                mChanged = true;
                tv.setRightString(year + "-" + month + "-" + day);
            }
        });
        dialog.show();
    }

    private void showBottomAddressDialog(List<AddressInfo> address, SuperTextView tv) {
        BottomAdressDialog dialog = new BottomAdressDialog(ImproveInformationUI.this);
        dialog.setOnDialogCallbackListener(new BottomAdressDialog.OnDialogCallbackListener() {
            @Override
            public void onDialogSelectItem(String year, String month, String day) {
                mChanged = true;
                tv.setRightString(year + month + day);
            }
        });

        dialog.show();
        dialog.setPickerData(address);
    }

    private void showSignWheelDialog(int type, SuperTextView textView) {
        mSignWheelDatas.clear();

        mChanged = true;
        if (1 == type) {
            for (int i = 0; i < 130; i++) {
                mSignWheelDatas.add(new PickerBean(i, (155 + i) + "cm"));
            }

        } else if (2 == type) {
            for (int i = 0; i < 120; i++) {
                mSignWheelDatas.add(new PickerBean(i, (30 + i) + "kg"));
            }
        } else if (3 == type) {
            for (LabelInfo lable : mPickerViewInfo.getEducationList()) {
                mSignWheelDatas.add(new PickerBean(0, lable.getText()));
            }
        } else if (4 == type) {

            for (LabelInfo lable : mPickerViewInfo.getCareerList()) {
                mSignWheelDatas.add(new PickerBean(0, lable.getText()));
            }
        } else if (5 == type) {

            for (LabelInfo lable : mPickerViewInfo.getYearIncomeList()) {
                mSignWheelDatas.add(new PickerBean(0, lable.getText()));
            }
        } else if (6 == type) {
            for (LabelInfo lable : mPickerViewInfo.getEmotionStatusList()) {
                mSignWheelDatas.add(new PickerBean(0, lable.getText()));
            }
        } else if (7 == type) {
            for (LabelInfo lable : mPickerViewInfo.getLiveStatusList()) {
                mSignWheelDatas.add(new PickerBean(0, lable.getText()));
            }
        }

        if (null == mBottomWheelDialog) {
            mBottomWheelDialog = new BottomSignWheelViewDialog(ImproveInformationUI.this);
        }

        mBottomWheelDialog.setOnDialogCallbackListener(new BottomSignWheelViewDialog.OnDialogCallbackListener() {
            @Override
            public void onDialogSelectItem(PickerBean info) {
                textView.setRightString(info.getName());
                AppHelper.hideSoftPad(ImproveInformationUI.this);
            }
        });

        mBottomWheelDialog.show();
        mBottomWheelDialog.setNewDatas(mSignWheelDatas);
    }

    private void showRecordSoundDialog() {
        XXPermissions.with(ImproveInformationUI.this).permission(Permission.RECORD_AUDIO, Permission.CAMERA).interceptor(new PermissionInterceptor()).request(new OnPermissionCallback() {
            @Override
            public void onGranted(List<String> permissions, boolean all) {
                if (!all) {
                    return;
                }

                BottomRecordSoundDialog mRecordSoundDialog = new BottomRecordSoundDialog(ImproveInformationUI.this);
                mRecordSoundDialog.setOnDialogCallbackListener(new BottomRecordSoundDialog.OnDialogCallbackListener() {
                    @Override
                    public void onDialogSelectItem(String path, long time) {
                        presenter.uploadAudio(path, "audio");
                        mAudioLength = time + "";
                        mFriendlyVoiceTv.setText(time + "s");
                    }
                });
                mRecordSoundDialog.show();
            }
        });

    }

    private void setPicCoverDialog(int position) {
        if (null == mBottomChoiceDialog) {
            mBottomChoiceDialog = new BottomChoiceDialog(ImproveInformationUI.this);
        }

        mBottomChoiceDialog.setOnDialogCallbackListener(new BottomChoiceDialog.OnDialogCallbackListener() {
            @Override
            public void onDialogSelectItem(int type) {
                if (1 == type) {
                    coverUrl = mSelectList.get(position);
                } else if (2 == type) {
                    mSelectList.remove(position);

                    mSelectList = LibCollections.removeEmptyStrings(mSelectList);
                    if (mSelectList.size() < 9) {
                        mSelectList.add("");
                    }
                    mAdapter.setItems(mSelectList);
                    mAdapter.notifyDataSetChanged();
                } else if (3 == type) {
                    ArrayList<DiaplayPicInfo> mDiaplayPicInfos = new ArrayList<>();
                    List<String> temp = LibCollections.removeEmptyStrings(mSelectList);
                    for (String picurl : temp) {
                        mDiaplayPicInfos.add(new DiaplayPicInfo(picurl));
                    }

                    DischargedPicBrowserActivity.start(ImproveInformationUI.this, mDiaplayPicInfos, position);

                }
            }
        });
        mBottomChoiceDialog.show();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (null != mBgmPlayer)
            mBgmPlayer.release();
    }
}
