package com.chat.laty.activity;

import android.content.Context;
import android.content.Intent;
import android.text.InputType;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.RadioGroup;

import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.fragment.app.FragmentActivity;

import com.allen.library.SuperTextView;
import com.fm.openinstall.OpenInstall;
import com.fm.openinstall.listener.AppInstallAdapter;
import com.fm.openinstall.model.AppData;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.hjq.toast.Toaster;
import com.luck.picture.lib.basic.PictureSelector;
import com.luck.picture.lib.config.PictureConfig;
import com.luck.picture.lib.entity.LocalMedia;
import com.makeramen.roundedimageview.RoundedImageView;
import com.chat.laty.MainActivity;
import com.chat.laty.R;
import com.chat.laty.contants.Constant;
import com.chat.laty.controllers.UserInfoController;
import com.chat.laty.dialog.BottomAdressDialog;
import com.chat.laty.dialog.BottomBirthdayDialog;
import com.chat.laty.entity.AddressInfo;
import com.chat.laty.entity.BaseBean;
import com.chat.laty.entity.LoinEntity;
import com.chat.laty.entity.RYTokenInfo;
import com.chat.laty.entity.RyUserInfo;
import com.chat.laty.entity.UserFreeInfo;
import com.chat.laty.entity.XYUserInfo;
import com.chat.laty.mvp.BasePresenterActivity;
import com.chat.laty.presenters.UserInfoPresenter;
import com.chat.laty.utils.PicassoUtils;
import com.chat.laty.utils.XYSPUtils;
import com.chat.laty.view.ClearEditText;

import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * <AUTHOR>
 * @date 2023/12/15 9:00
 * @description:补充信息注册
 */
public class ComplementUserInfoUI extends BasePresenterActivity<UserInfoPresenter> implements UserInfoController.View {

    @BindView(R.id.edit_nickname)
    ClearEditText mUserNickNameCet;

    @BindView(R.id.edit_invitation_code)
    ClearEditText mInvitationCodeCet;

    @BindView(R.id.user_name_tv)
    AppCompatTextView mUserNameTv;
    @BindView(R.id.user_head_riv)
    RoundedImageView mUserHeadRiv;

    @BindView(R.id.gender_rg)
    RadioGroup mUserSexRg;

    @BindView(R.id.address_stv)
    SuperTextView mAddressStv;

    @BindView(R.id.birthday_stv)
    SuperTextView mBirthdayStv;

    String avatarUrl;
    private boolean mIsMale = true;

//    private String mInvitationCode;

    public static void start(Context context, String invitationCode) {
        Intent intent = new Intent(context, ComplementUserInfoUI.class);
        intent.putExtra("invitationCode", invitationCode);
        context.startActivity(intent);
    }

    @Override
    protected void initDatas() {
//        presenter.getVipInfo();
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_complement_userinfo_layout;
    }

    @Override
    protected void initViews() {

//        mInvitationCode = getIntent().getStringExtra("invitationCode");
        mUserSexRg.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(RadioGroup group, int checkedId) {
                if (checkedId == R.id.gender_male_rb) {
                    mIsMale = true;
                } else if (checkedId == R.id.gender_female_rb) {
                    mIsMale = false;
                }
            }
        });


        OpenInstall.init(this);
        OpenInstall.getInstall(new AppInstallAdapter() {
            @Override
            public void onInstall(AppData appData) {
                // 打印数据便于调试
                Log.d("OpenInstall", "getInstall : installData = " + appData.toString());
//                Toast.makeText(getThisActivity(), "app传值--》" + appData.getData(), Toast.LENGTH_SHORT).show();
                if (TextUtils.isEmpty(appData.getData())) {
                    return;
                }

                Map<String, Object> params = new Gson().fromJson(appData.getData(), new TypeToken<Map<String, Object>>() {
                }.getType());
                if (params.containsKey("code")) {
                    Object code = params.get("code");
                    if (code != null) {
                        mInvitationCodeCet.setText(String.format("%s", params.get("code")));
                        mInvitationCodeCet.setEnabled(false);
                        mInvitationCodeCet.setFocusable(false);
                        mInvitationCodeCet.setFocusableInTouchMode(false);
                        mInvitationCodeCet.setInputType(InputType.TYPE_NULL);
                        mInvitationCodeCet.setClickable(false);
                        mInvitationCodeCet.setLongClickable(false);
                    }
                }
            }
        });
    }

    @OnClick({R.id.user_head_riv, R.id.submit_btn, R.id.birthday_stv, R.id.address_stv})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.user_head_riv:
//                choicePic(1);
                break;
            case R.id.address_stv:
                presenter.getAddress(ComplementUserInfoUI.this);
                break;

            case R.id.birthday_stv:
                showBirthdayDialog();
                break;

            case R.id.submit_btn:
                if (TextUtils.isEmpty(mUserNickNameCet.getText().toString())) {
                    Toaster.show("请输入新的昵称");
                    break;
                }

                if ("请选择".equals(mAddressStv.getRightString()) || TextUtils.isEmpty(mAddressStv.getRightString())) {
                    Toaster.show("请选择地址");
                    break;
                }

                if ("请选择".equals(mBirthdayStv.getRightString()) || TextUtils.isEmpty(mBirthdayStv.getRightString())) {
                    Toaster.show("请选择生日");
                    break;
                }
                showProgressDialog(R.string.app_uploadding);
                presenter.updateUserInfo(mUserNickNameCet.getText().toString(), mIsMale, mAddressStv.getRightString(), mBirthdayStv.getRightString(), mInvitationCodeCet.getText().toString());
                break;
        }
    }

    private void showBirthdayDialog() {
        BottomBirthdayDialog dialog = new BottomBirthdayDialog(ComplementUserInfoUI.this);
        dialog.setOnDialogCallbackListener(new BottomBirthdayDialog.OnDialogCallbackListener() {
            @Override
            public void onDialogSelectItem(int year, int month, int day) {
                // 获取当前日期
                mBirthdayStv.setRightString(year + "-" + month + "-" + day);
            }
        });
        dialog.show();
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, @Nullable @org.jetbrains.annotations.Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK) {
            switch (requestCode) {
                case PictureConfig.CHOOSE_REQUEST:
                    List<LocalMedia> selectList = PictureSelector.obtainSelectorList(data);
                    PicassoUtils.showImage(mUserHeadRiv, selectList.get(0).getCompressPath());
                    presenter.uploadFiles(selectList, "userHead");
                    break;
            }
        }
    }

    @Override
    protected UserInfoPresenter setPresenter() {
        return new UserInfoPresenter(this);
    }

    @Override
    public FragmentActivity context() {
        return getThisActivity();
    }

    @Override
    public void showUploadResult(List<String> result) {
        avatarUrl = result.get(0);
    }

    @Override
    public void updateCallback(BaseBean callbackBean) {
        if (null != callbackBean) {
            presenter.getUserInfo();
        }
    }

    @Override
    public void showUserInfo(RyUserInfo userInfo) {
        if (null != userInfo) {
            Toaster.show("修改成功!");
            PicassoUtils.showImage(mUserHeadRiv, userInfo.getAvatar());
            mUserNameTv.setText(userInfo.getNickname());
        }

    }

    @Override
    public void complementCallback(LoinEntity data) {
        if (null != data) {
//            EventBus.getDefault().post(new RYTokenEvent("getToken"));
//            finish();
            presenter.getRongCloudToken();
        }
    }

    @Override
    public void showUserDetails(XYUserInfo data) {

    }

    @Override
    public void showRyToken(RYTokenInfo data) {
        if (null != data && !TextUtils.isEmpty(data.getToken())) {
            XYSPUtils.put(Constant.KEY_USER_RONGYUN_TOKEN, data.getToken());
            finish();
        }
    }

    @Override
    public void showFollwoCallback() {

    }

    @Override
    public void showAddress(List<AddressInfo> addressInfos) {
        showBottomAddressDialog(addressInfos, mAddressStv);
    }

    @Override
    public void showLoginImCallback() {
        dismissProgressDialog();
        MainActivity.start(ComplementUserInfoUI.this);
        finish();
    }

    @Override
    public void showUserFreeInfo(UserFreeInfo userFreeInfo) {

    }


    private void showBottomAddressDialog(List<AddressInfo> address, SuperTextView tv) {
        BottomAdressDialog dialog = new BottomAdressDialog(ComplementUserInfoUI.this);
        dialog.setOnDialogCallbackListener(new BottomAdressDialog.OnDialogCallbackListener() {
            @Override
            public void onDialogSelectItem(String year, String month, String day) {
                tv.setRightString(year + month + day);
            }
        });

        dialog.show();
        dialog.setPickerData(address);
    }
}

