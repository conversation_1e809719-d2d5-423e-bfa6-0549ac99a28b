package com.chat.laty.activity;

import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.view.View;
import android.widget.EditText;

import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.RecyclerView;

import com.hjq.permissions.OnPermissionCallback;
import com.hjq.permissions.Permission;
import com.hjq.permissions.XXPermissions;
import com.hjq.toast.Toaster;
import com.luck.picture.lib.basic.PictureSelector;
import com.luck.picture.lib.config.PictureConfig;
import com.luck.picture.lib.entity.LocalMedia;
import com.chat.laty.R;
import com.chat.laty.adapter.AddPicAdapter;
import com.chat.laty.adapter.ReportItemAdapter;
import com.chat.laty.controllers.ReportController;
import com.chat.laty.entity.BaseBean;
import com.chat.laty.entity.DiaplayPicInfo;
import com.chat.laty.mvp.BasePresenterActivity;
import com.chat.laty.presenters.ReportPresenter;
import com.chat.laty.utils.PermissionInterceptor;
import com.chat.laty.view.CustomActionBar;
import com.chat.laty.view.RecyclerItemDecoration;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;
import cn.xjc_soft.lib_utils.LibCollections;

/**
 * <AUTHOR>
 * @date 2024/1/28 16:12
 * @description:
 */
public class ReportUserActivity extends BasePresenterActivity<ReportPresenter> implements ReportController.View {
    private static final String EXTRA_KEY_USER_ID = "extra_key_user_id";

    @BindView(R.id.custom_bar)
    CustomActionBar mActionBar;

    @BindView(R.id.rv_items)
    RecyclerView mReportItemRV;

    @BindView(R.id.rv_images)
    RecyclerView rvImages;

    @BindView(R.id.content_et)
    EditText mReportDetailET;

    ReportItemAdapter mReportItemAdapter;

    AddPicAdapter mAdapter;

    private List<String> mOptItems = new ArrayList<>();
    List<String> mSelectList = new ArrayList<>();

    private String mUserId;

    public static void start(Context context, String userId) {
        Intent intent = new Intent(context, ReportUserActivity.class);
        intent.putExtra(EXTRA_KEY_USER_ID, userId);
        context.startActivity(intent);
    }

    @Override
    protected void initDatas() {

    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_report_user_layout;
    }

    @Override
    protected void initViews() {
        mActionBar.setTitleText("举报");
        mActionBar.setTitleTextColor(getResources().getColor(R.color.color_333333));

        mUserId = getIntent().getStringExtra(EXTRA_KEY_USER_ID);

        mReportItemAdapter = new ReportItemAdapter();
        mReportItemRV.addItemDecoration(new RecyclerItemDecoration());
        mReportItemRV.setAdapter(mReportItemAdapter);

        mReportItemAdapter.addOnItemChildClickListener(R.id.select_statu, (baseQuickAdapter, view, position) -> {
            String reportType = (String) baseQuickAdapter.getItem(position);
            mReportItemAdapter.setSelectValue(reportType);
        });

        mOptItems.add("垃圾广告/骗子");
        mOptItems.add("虚假信息/盗用图片");
        mOptItems.add("色情低俗");
        mOptItems.add("骚扰谩骂");
        mOptItems.add("骗子");
        mOptItems.add("其他");

        mReportItemAdapter.setItems(mOptItems);

        mAdapter = new AddPicAdapter();
        rvImages.setAdapter(mAdapter);

        mSelectList.add("");
        mAdapter.setItems(mSelectList);
        mAdapter.showDelete(true);

        mAdapter.addOnItemChildClickListener(R.id.add_layout, (baseQuickAdapter, view, position) -> {
            XXPermissions.with(this).permission(Permission.READ_MEDIA_IMAGES).interceptor(new PermissionInterceptor()).interceptor(new PermissionInterceptor()).request(new OnPermissionCallback() {
                @Override
                public void onGranted(List<String> permissions, boolean all) {
                    if (!all) {
                        return;
                    }
                    choicePic(10 - mSelectList.size());
                }
            });
        });

        mAdapter.addOnItemChildClickListener(R.id.iv_thum, (baseQuickAdapter, view, position) -> {
//            choicePic(10 - mSelectList.size());
            ArrayList<DiaplayPicInfo> mDiaplayPicInfos = new ArrayList<>();
            for (String picUrl:mSelectList) {
                mDiaplayPicInfos.add(new DiaplayPicInfo(picUrl));
            }
            DischargedPicBrowserActivity.start(ReportUserActivity.this, mDiaplayPicInfos, position);

        });

//        mAdapter.addOnItemChildLongClickListener(R.id.iv_thum, (baseQuickAdapter, view, position) -> {
//            mAdapter.showDelete(true);
//            return true;
//        });

        mAdapter.addOnItemChildClickListener(R.id.delete_tv, (baseQuickAdapter, view, position) -> {
            mSelectList.remove(position);
//            mSelectList.add("");
            mAdapter.setItems(mSelectList);
            mAdapter.notifyDataSetChanged();
        });
    }

    @OnClick({R.id.submit_button})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.submit_button:
                HashMap<String, Object> params = new HashMap<>();
                params.put("fileUrl", LibCollections.removeEmptyStrings(mSelectList).toString().replace("[", "").replace("]", "").replace(" ", ""));
                params.put("reportDetail", mReportDetailET.getText().toString());
                if (!TextUtils.isEmpty(mReportItemAdapter.getSelects()))
                    params.put("reportType", mReportItemAdapter.getSelects().substring(0, mReportItemAdapter.getSelects().length() - 1));
                params.put("reportUserId", mUserId);
                presenter.addReport(params);
                break;
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, @Nullable @org.jetbrains.annotations.Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK) {
            switch (requestCode) {
                case PictureConfig.CHOOSE_REQUEST:
                    List<LocalMedia> selectList = PictureSelector.obtainSelectorList(data);
//                    PicassoUtils.showImage(mUserHeadRiv, selectList.get(0).getCompressPath());
                    presenter.uploadFiles(selectList, "report");
                    break;
            }
        }
    }

    @Override
    protected ReportPresenter setPresenter() {
        return new ReportPresenter(this);
    }


    @Override
    public FragmentActivity context() {
        return this;
    }

    @Override
    public void showUploadResults(List<String> result) {
        if (!LibCollections.isEmpty(result)) {
            mSelectList = LibCollections.removeEmptyStrings(mSelectList);
            for (String info : result) {
                mSelectList.add(info);
            }
            if (mSelectList.size() < 9) {
                mSelectList.add("");
            }
            mAdapter.setItems(mSelectList);
            mAdapter.notifyDataSetChanged();
        }
    }

    @Override
    public void showReportCallback(BaseBean callbackBean) {
        if (null != callbackBean) {
            Toaster.show(callbackBean.getMessage());
            if (200 == callbackBean.getCode()) {
                finish();
            }
        }
    }
}
