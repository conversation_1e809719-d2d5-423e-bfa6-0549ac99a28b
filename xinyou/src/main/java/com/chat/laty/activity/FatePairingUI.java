package com.chat.laty.activity;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;

import androidx.fragment.app.FragmentActivity;

import com.hjq.permissions.OnPermissionCallback;
import com.hjq.permissions.Permission;
import com.hjq.permissions.XXPermissions;
import com.hjq.toast.Toaster;
import com.makeramen.roundedimageview.RoundedImageView;
import com.chat.laty.utils.PermissionInterceptor;
import com.yhao.floatwindow.FloatWindow;
import com.yhao.floatwindow.MoveType;
import com.yhao.floatwindow.Screen;
import com.chat.laty.R;
import com.chat.laty.base.XYApplication;
import com.chat.laty.contants.Constant;
import com.chat.laty.controllers.FatePairController;
import com.chat.laty.entity.FatePairingInfo;
import com.chat.laty.entity.RyUserInfo;
import com.chat.laty.entity.WebBeanInfo;
import com.chat.laty.entity.event.FatePairEvent;
import com.chat.laty.entity.event.FatePairingEvent;
import com.chat.laty.mvp.BasePresenterActivity;
import com.chat.laty.presenters.FatePairPresenter;
import com.chat.laty.service.FatePairingBackgroundService;
import com.chat.laty.utils.LogUtil;
import com.chat.laty.utils.PicassoUtils;
import com.chat.laty.utils.XYSPUtils;
import com.chat.laty.utils.net.Common;
import com.chat.laty.view.CustomActionBar;
import com.chat.laty.view.RippleView;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * <AUTHOR>
 * @date 2023/12/20 14:45
 * @description:缘分配对界面
 */
public class FatePairingUI extends BasePresenterActivity<FatePairPresenter> implements FatePairController.View, CustomActionBar.OnActionBarClickListerner {
    @BindView(R.id.custom_bar)
    CustomActionBar mActionBar;
    @BindView(R.id.fatepairing_rv)
    RippleView mRippleView;

    @BindView(R.id.user_head_riv)
    RoundedImageView mUserIcon;

    RyUserInfo mUserInfo;

    public static void start(Context context) {
        Intent intent = new Intent(context, FatePairingUI.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        context.startActivity(intent);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onFatePairEvent(FatePairEvent sessionEvent) {
        LogUtil.e("响应结果", "FatePairEvent--》");
        presenter.stopWebSocket();
        XYSPUtils.put(Constant.KEY_USER_FATE_PAIRING, false);
        FloatWindow.destroy("fatePairing");
        finish();
    }

    @Override
    protected void initDatas() {

    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_fatepairing_layout;
    }

    @Override
    protected void initViews() {
        mActionBar.setTitleText("缘分配对");
        mActionBar.removeFunction(CustomActionBar.FUNCTION_BUTTON_LEFT);
        mActionBar.addFunction(CustomActionBar.FUNCTION_TEXT_RIGHT);
        mActionBar.setRightText("配对技巧");
        mRippleView.stop(1);
        mUserInfo = XYApplication.getCurrentUserInfo();
        String userId = XYSPUtils.getString(Common.KEY_APP_USER_RY_ID);
        if (null != mUserInfo && !TextUtils.isEmpty(mUserInfo.getAvatar()))
            PicassoUtils.showImage(mUserIcon, mUserInfo.getAvatar());
        presenter.startPair(userId);
        mActionBar.setOnActionBarClickListerner(this);
    }

    @SuppressLint("RestrictedApi")
    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        if (event.getAction() == KeyEvent.ACTION_DOWN && event.getKeyCode() == KeyEvent.KEYCODE_BACK) {
            Toaster.show("请选择返回模式");
            return true;
        }
        return super.dispatchKeyEvent(event);
    }

    @OnClick({R.id.backend_waiting_btn, R.id.exit_btn,R.id.fateTips})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.fateTips:
                Toaster.show("温馨提示：视频匹配可快速找到你的有缘人，消费金币500金币/分");
                break;

            case R.id.backend_waiting_btn:

                XXPermissions.with(FatePairingUI.this).permission(Permission.SYSTEM_ALERT_WINDOW).interceptor(new PermissionInterceptor()).request(new OnPermissionCallback() {
                    @Override
                    public void onGranted(List<String> permissions, boolean all) {
                        if (!all) {
                            return;
                        }

                        XYSPUtils.put(Constant.KEY_USER_FATE_PAIRING, true);
                        EventBus.getDefault().post(new FatePairEvent(true));
                        View rootView = getLayoutInflater().inflate(R.layout.custom_fatepair_floatview, null);
                        rootView.setOnClickListener(new View.OnClickListener() {
                            @Override
                            public void onClick(View view) {
                                FatePairingUI.start(XYApplication.getAppApplicationContext());
                            }
                        });
                        if (null == FloatWindow.get("fatePairing")) {
                            FloatWindow
                                    .with(XYApplication.getAppApplicationContext())
                                    .setView(rootView)
//                            .setHeight(Screen.width, 0.2f)
                                    .setX(100)//设置控件初始位置
                                    .setY(Screen.height, 0.3f)
                                    .setDesktopShow(true)//桌面显示
                                    .setMoveType(MoveType.slide)
                                    .setTag("fatePairing")
                                    .build();
                        } else {
                            FloatWindow.get("fatePairing").show();
                        }

                        moveTaskToBack(false);//true对任何Activity都适用
                        presenter.destroy();
                        Intent serviceIntent = new Intent(FatePairingUI.this, FatePairingBackgroundService.class);
                        startService(serviceIntent);
//                finish();
                        finish();
                    }
                });

                break;

            case R.id.exit_btn:
                XYSPUtils.put(Constant.KEY_USER_FATE_PAIRING, false);
                EventBus.getDefault().post(new FatePairEvent(false));
                presenter.stopWebSocket();
//                if (null != FloatWindow.get("fatePairing"))
//                    FloatWindow.get("fatePairing").hide();
                FloatWindow.destroy("fatePairing");
                finish();
                break;
        }
    }

    @Override
    protected FatePairPresenter setPresenter() {
        return new FatePairPresenter(this);
    }

    @Override
    public FragmentActivity context() {
        return this;
    }

    @Override
    public void onFatePairMessageCallback(FatePairingInfo base,String extra) {
        if (null != base && TextUtils.equals("1", base.getType()) && !TextUtils.isEmpty(base.getUserId())) {
            XXPermissions.with(FatePairingUI.this).permission(Permission.RECORD_AUDIO, Permission.CAMERA).interceptor(new PermissionInterceptor()).request(new OnPermissionCallback() {
                @Override
                public void onGranted(List<String> permissions, boolean all) {
                    if (!all) {
                        return;
                    }
                    presenter.stopWebSocket();
                    EventBus.getDefault().postSticky(new FatePairingEvent(base));
                    CallPhoneActivity.start(FatePairingUI.this, base.getUserId(), 0, 1, extra);
                    finish();
                }
            });
        }
    }

    @Override
    public void showWebInfo(WebBeanInfo data) {
        WebViewActivity.startActivity(FatePairingUI.this, "", data.getUrl());
    }

    @Override
    protected boolean isUseEventBus() {
        return true;
    }

    @Override
    public boolean onActionBarClickListener(int function) {
        switch (function) {
            case CustomActionBar.FUNCTION_TEXT_RIGHT:
                presenter.getAccordByNum(1);
                break;
        }
        return false;
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (null != FloatWindow.get("fatePairing"))
            FloatWindow.get("fatePairing").hide();
    }
}
