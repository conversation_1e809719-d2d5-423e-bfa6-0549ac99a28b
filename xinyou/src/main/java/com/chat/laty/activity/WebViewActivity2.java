package com.chat.laty.activity;

import android.content.Context;
import android.content.Intent;
import android.view.View;
import android.webkit.WebSettings;
import android.webkit.WebView;

import androidx.annotation.NonNull;

import com.chat.laty.R;
import com.chat.laty.base.BaseActivity;

import butterknife.BindView;

/**
 * <AUTHOR>
 * @date 2023/6/10.
 * email：<EMAIL>
 * description： Webview界面
 */
public class WebViewActivity2 extends BaseActivity {
    private static final String EXTRA_KEY_TITLE = "extra_key_title";
    private static final String EXTRA_KEY_URL = "extra_key_url";
    private static final String EXTRA_KEY_SHOULD_SHOW_DIALOG = "extra_key_should_show_dialog";



    @BindView(R.id.web_view)
    WebView web_view;

    @Override
    protected void initDatas() {

    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_web_view_2;
    }

    @Override
    protected void initViews() {
        String title = getIntent().getStringExtra(EXTRA_KEY_TITLE);
        String url = getIntent().getStringExtra(EXTRA_KEY_URL);

        initWebView();

        web_view.loadUrl(url);
    }


    private void initWebView(){
        WebSettings webSettings = web_view.getSettings();
        web_view.setOverScrollMode(View.OVER_SCROLL_NEVER);
        webSettings.setJavaScriptEnabled(true);

        //设置自适应屏幕，两者合用
        webSettings.setUseWideViewPort(true);  //将图片调整到适合webView的大小
        webSettings.setLoadWithOverviewMode(true); // 缩放至屏幕的大小
        webSettings.setDefaultTextEncodingName("UTF-8");
        webSettings.setSupportZoom(true);
        webSettings.setBuiltInZoomControls(true);
        webSettings.setDomStorageEnabled(true);
        webSettings.setAllowUniversalAccessFromFileURLs(true);
        webSettings.setDisplayZoomControls(false);
        webSettings.setLayoutAlgorithm(WebSettings.LayoutAlgorithm.SINGLE_COLUMN); //支持内容重新布局

        web_view.getSettings().setAllowFileAccess(true);
        web_view.requestFocus();

//        web_view.setWebViewClient(new WebViewActivity.SimpleWebViewClient());
//        web_view.setWebChromeClient(new WebViewActivity.SimpleWebChromeClient());
//        web_view.addJavascriptInterface(this, "native");
    }
//
//    private class SimpleWebViewClient extends WebViewClient {
//
//        @Override
//        public boolean shouldOverrideUrlLoading(WebView view, String url) {
//            mWebView.loadUrl(url);
//            return true;
//        }
//
//        @Override
//        public void onPageStarted(WebView view, String url, Bitmap favicon) {
//            super.onPageStarted(view, url, favicon);
//            if (shouldShowProgress) {
//                showProgressDialog(R.string.app_loadding);
//            }
//        }
//
//        @Override
//        public void onPageFinished(WebView view, String url) {
//            super.onPageFinished(view, url);
//            if (shouldShowProgress) {
//                dismissProgressDialog();
//            }
//            mWebView.setVisibility(View.VISIBLE);
//            isCanClick = true;
//        }
//
//        @Override
//        public void onReceivedError(WebView view, int errorCode, String description, String failingUrl) {
//            super.onReceivedError(view, errorCode, description, failingUrl);
//            mNoNet.setVisibility(View.VISIBLE);
//        }
//    }
//
//    private class SimpleWebChromeClient extends WebChromeClient {
//
//
//        @Override
//        public void onProgressChanged(WebView view, int newProgress) {
//            super.onProgressChanged(view, newProgress);
//            if (progressBar == null) {
//                return;
//            }
//            progressBar.setProgress(newProgress > 5 ? newProgress : 5);
//            if (newProgress == 100) {
//                progressBar.setVisibility(View.GONE);
//            }
//        }
//
//        @Override
//        public void onReceivedTitle(WebView view, String title) {
////            mTopBar.setTitleText(title);
//        }
//    }



    public static void startActivity(Context context, @NonNull String title, @NonNull String url) {
        Intent intent = new Intent(context, WebViewActivity2.class);
        intent.putExtra(EXTRA_KEY_TITLE, title);
        intent.putExtra(EXTRA_KEY_URL, url);
        context.startActivity(intent);
    }
}
