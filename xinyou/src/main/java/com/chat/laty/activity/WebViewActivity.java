package com.chat.laty.activity;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Build;
import android.view.KeyEvent;
import android.view.View;
import android.webkit.JavascriptInterface;
import android.webkit.PermissionRequest;
import android.webkit.ValueCallback;
import android.webkit.WebChromeClient;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.LinearLayout;
import android.widget.ProgressBar;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;

import com.chat.laty.R;
import com.chat.laty.base.BaseActivity;
import com.chat.laty.utils.LogUtil;
import com.chat.laty.view.CustomActionBar;

import org.json.JSONException;
import org.json.JSONObject;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * <AUTHOR>
 * @date 2023/6/10.
 * email：<EMAIL>
 * description： Webview界面
 */
public class WebViewActivity extends BaseActivity {
    private static final String EXTRA_KEY_TITLE = "extra_key_title";
    private static final String EXTRA_KEY_URL = "extra_key_url";
    private static final String EXTRA_KEY_SHOULD_SHOW_DIALOG = "extra_key_should_show_dialog";

    private static final int FILE_PICKER_REQUEST_CODE = 1001; // 可以选择任意整数值
    private ValueCallback<Uri[]> mUploadMessage;


    @BindView(R.id.custom_bar)
    CustomActionBar mTopBar;

    @BindView(R.id.web_view)
    WebView mWebView;

    @BindView(R.id.ll_no_net)
    LinearLayout mNoNet;

    @BindView(R.id.custom_webView_progress)
    ProgressBar progressBar;

    private String url;
    private String title;
    private boolean isCanClick;
    private boolean shouldShowProgress;

    public static void startActivity(Context context, @NonNull String title, @NonNull String url) {
        startActivity(context, title, url, true);
    }

    public static void startActivity(Context context, @NonNull String title, @NonNull String url, boolean shouldShowProgressDialog) {
        Intent intent = new Intent(context, WebViewActivity.class);
        intent.putExtra(EXTRA_KEY_TITLE, title);
        intent.putExtra(EXTRA_KEY_URL, url);
        intent.putExtra(EXTRA_KEY_SHOULD_SHOW_DIALOG, shouldShowProgressDialog);
        context.startActivity(intent);
    }

    public static void startActivityForResult(Activity activity, @NonNull String url, int requestCode) {
        Intent intent = new Intent(activity, WebViewActivity.class);
        intent.putExtra(EXTRA_KEY_TITLE, "");
        intent.putExtra(EXTRA_KEY_URL, url);
        intent.putExtra(EXTRA_KEY_SHOULD_SHOW_DIALOG, false);
        activity.startActivityForResult(intent, requestCode);
    }

    @Override
    protected void initDatas() {

    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_web_view;
    }

    @Override
    protected void initViews() {
        initData();
    }

    private void initData() {
        title = getIntent().getStringExtra(EXTRA_KEY_TITLE);
        url = getIntent().getStringExtra(EXTRA_KEY_URL);
        LogUtil.e("响应结果", "url==============" + url);
        shouldShowProgress = getIntent().getBooleanExtra(EXTRA_KEY_SHOULD_SHOW_DIALOG, false);
        mTopBar.setTitleText(title);
        mTopBar.setTitleTextColor(ContextCompat.getColor(this,R.color.black));
        configWebView();
        mWebView.loadUrl(url);
    }

    @OnClick({R.id.ll_no_net})
    public void onClick(View view) {
        if (isCanClick) {
            isCanClick = false;
            mNoNet.setVisibility(View.GONE);
            mWebView.setVisibility(View.VISIBLE);
            mWebView.reload();
        }
    }

    @SuppressLint({"SetJavaScriptEnabled", "JavascriptInterface", "AddJavascriptInterface"})
    private void configWebView() {
        WebSettings webSettings = mWebView.getSettings();
        mWebView.setOverScrollMode(View.OVER_SCROLL_NEVER);
        webSettings.setJavaScriptEnabled(true);

        //设置自适应屏幕，两者合用
        webSettings.setUseWideViewPort(true);  //将图片调整到适合webView的大小
        webSettings.setLoadWithOverviewMode(true); // 缩放至屏幕的大小
        webSettings.setDefaultTextEncodingName("UTF-8");
        webSettings.setSupportZoom(true);
        webSettings.setBuiltInZoomControls(true);
        webSettings.setDomStorageEnabled(true);
        webSettings.setAllowUniversalAccessFromFileURLs(true);
        webSettings.setDisplayZoomControls(false);
        webSettings.setLayoutAlgorithm(WebSettings.LayoutAlgorithm.SINGLE_COLUMN); //支持内容重新布局

        mWebView.getSettings().setAllowFileAccess(true);
        mWebView.getSettings().setJavaScriptEnabled(true);
        mWebView.requestFocus();

        mWebView.setWebViewClient(new SimpleWebViewClient());
        mWebView.setWebChromeClient(new SimpleWebChromeClient());
        mWebView.addJavascriptInterface(WebViewActivity.this, "native");
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            if (mWebView.canGoBack()) {
                mWebView.goBack();
                return true;
            } else {
                finish();
            }
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
    }

    @Override
    public void onBackPressed() {
        if (mWebView.canGoBack()) {
            mWebView.goBack();
        } else {
            super.onBackPressed();
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (requestCode == FILE_PICKER_REQUEST_CODE && resultCode == RESULT_OK) {
            Uri[] results = null;
            if (data != null) {
                String path = data.getDataString();
                results = new Uri[]{Uri.parse(path)};
            }

            if (mUploadMessage != null) {
                mUploadMessage.onReceiveValue(results); // 调用回调
                mUploadMessage = null; // 清空，避免重复调用
            }
        } else {
            if (mUploadMessage != null) {
                mUploadMessage.onReceiveValue(null); // 用户取消时返回 null
                mUploadMessage = null;
            }
        }
    }

    private class SimpleWebViewClient extends WebViewClient {

        @Override
        public boolean shouldOverrideUrlLoading(WebView view, String url) {
            mWebView.loadUrl(url);
            return true;
        }

        @Override
        public void onPageStarted(WebView view, String url, Bitmap favicon) {
            super.onPageStarted(view, url, favicon);
            if (shouldShowProgress) {
                showProgressDialog(R.string.app_loadding);
            }
        }

        @Override
        public void onPageFinished(WebView view, String url) {
            super.onPageFinished(view, url);
            if (shouldShowProgress) {
                dismissProgressDialog();
            }
            mWebView.setVisibility(View.VISIBLE);
            isCanClick = true;
        }

        @Override
        public void onReceivedError(WebView view, int errorCode, String description, String failingUrl) {
            super.onReceivedError(view, errorCode, description, failingUrl);
            mNoNet.setVisibility(View.VISIBLE);
        }
    }

    private class SimpleWebChromeClient extends WebChromeClient {


        @Override
        public void onProgressChanged(WebView view, int newProgress) {
            super.onProgressChanged(view, newProgress);
            if (progressBar == null) {
                return;
            }
            progressBar.setProgress(newProgress > 5 ? newProgress : 5);
            if (newProgress == 100) {
                progressBar.setVisibility(View.GONE);
            }
        }

        @Override
        public void onReceivedTitle(WebView view, String title) {
//            mTopBar.setTitleText(title);
        }

        // 允许文件选择回调
        public void onPermissionRequest(final PermissionRequest request) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                request.grant(request.getResources());
            }
        }

        // 文件选择回调
        public boolean onShowFileChooser(WebView webView, ValueCallback<Uri[]> filePathCallback,
                                         FileChooserParams fileChooserParams) {
            mUploadMessage = filePathCallback; // 保存回调
            Intent intent = new Intent(Intent.ACTION_GET_CONTENT);
            intent.addCategory(Intent.CATEGORY_OPENABLE);
            intent.setType("*/*");
            startActivityForResult(intent, FILE_PICKER_REQUEST_CODE);
            return true;
        }
    }

    @JavascriptInterface
    public void startFunction(final String data) {
        runOnUiThread(() -> {
            try {
                doFunction(data);
            } catch (JSONException e) {
                e.printStackTrace();
            }
        });
    }

    /**
     * @param data
     */
    private void doFunction(String data) throws JSONException {
        LogUtil.e("doFunction","data--->"+data);
        JSONObject jsonObject = new JSONObject(data);
        Intent intent = new Intent();
        boolean isClose = true;

        if (isClose) {
            setResult(Activity.RESULT_OK, intent);
            finish();
        }
    }

}
