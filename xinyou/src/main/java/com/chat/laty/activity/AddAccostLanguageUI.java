package com.chat.laty.activity;

import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.view.MotionEvent;
import android.view.View;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.Button;
import android.os.Handler;
import android.os.Looper;

import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.RecyclerView;

import com.hjq.permissions.OnPermissionCallback;
import com.hjq.permissions.Permission;
import com.hjq.permissions.XXPermissions;
import com.hjq.toast.Toaster;
import com.luck.picture.lib.basic.PictureSelector;
import com.luck.picture.lib.config.PictureConfig;
import com.luck.picture.lib.entity.LocalMedia;
import com.chat.laty.R;
import com.chat.laty.adapter.AddPicAdapter;
import com.chat.laty.base.AppHelper;
import com.chat.laty.controllers.AccostLanguageController;
import com.chat.laty.entity.AccostLanguageInfo;
import com.chat.laty.entity.AccostManLanguageInfo;
import com.chat.laty.entity.BaseBean;
import com.chat.laty.entity.DiaplayPicInfo;
import com.chat.laty.entity.UploadImgInfo;
import com.chat.laty.entity.event.AccostLanguageEvent;
import com.chat.laty.mvp.BasePresenterActivity;
import com.chat.laty.presenters.AccostLanguagePresenter;
import com.chat.laty.utils.AudioPlayer;
import com.chat.laty.utils.LogUtil;
import com.chat.laty.utils.PermissionInterceptor;
import com.chat.laty.view.CustomActionBar;
import com.chat.laty.view.DiffuseView;
import com.chat.laty.view.recordView.RecordManager;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;
import cn.xjc_soft.lib_utils.LibCollections;

/**
 * <AUTHOR>
 * @date 2023/12/25 19:32
 * @description:搭讪语设置
 */
public class AddAccostLanguageUI extends BasePresenterActivity<AccostLanguagePresenter> implements AccostLanguageController.View {
    @BindView(R.id.custom_bar)
    CustomActionBar mActionBar;

    @BindView(R.id.name_et)
    EditText mNameEt;
    @BindView(R.id.content_et)
    EditText mContentEt;

    @BindView(R.id.rv_images)
    RecyclerView rvImages;
    @BindView(R.id.diffuseView)
    DiffuseView mRippleView;

    @BindView(R.id.audio_long_tv)
    AppCompatTextView mAudioLongTv;
    @BindView(R.id.record_status_tv)
    AppCompatTextView mRecordStatusTv;
    @BindView(R.id.audio_control_layout)
    LinearLayout mAudioControlLayout;
    @BindView(R.id.play_audio_btn)
    Button mPlayAudioBtn;
    @BindView(R.id.delete_audio_btn)
    Button mDeleteAudioBtn;

    UploadImgInfo mUploadImgInfo;

    AddPicAdapter mAdapter;

    List<String> mSelectList = new ArrayList<>();

    String mAudioFilePath;
    String mAudioPath;
    private RecordManager manager;
    private AudioPlayer mAudioPlayer;

    private int mCountDownTravel;
    private boolean isRecording = false;
    private boolean isPlaying = false;
    private Handler mHandler = new Handler(Looper.getMainLooper());
    private Runnable mRecordTimeRunnable;
    private Runnable mPlayStatusCheckRunnable;
    private long mRecordStartTime;

    AccostLanguageInfo mAccostLanguageInfo;


    public static void start(Context context) {
        Intent intent = new Intent(context, AddAccostLanguageUI.class);
        context.startActivity(intent);
    }

    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    public void getAccostLanguageEvent(AccostLanguageEvent event) {
        mAccostLanguageInfo = event.getInfo();
        EventBus.getDefault().removeStickyEvent(AccostLanguageEvent.class);

    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_add_accost_language_layout;
    }

    @Override
    protected void initDatas() {

    }

    @OnClick({R.id.submit_button, R.id.play_audio_btn, R.id.delete_audio_btn})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.play_audio_btn:
                playOrStopAudio();
                break;
            case R.id.delete_audio_btn:
                deleteAudio();
                break;
            case R.id.submit_button:
                if (TextUtils.isEmpty(mNameEt.getText().toString())) {
                    Toaster.show("请输入名称");
                    return;
                }


                int nonEmptyCount = 0;

                if (TextUtils.isEmpty(mContentEt.getText().toString())) {
                    nonEmptyCount++;
                }

                if (null == mUploadImgInfo) {
                    nonEmptyCount++;
                }

                if (null == mAudioPath) {
                    nonEmptyCount++;
                }

                if (nonEmptyCount > 2) {
                    Toaster.show("至少两项");
                    break;
                }

                presenter.updateAccostWoman(mAccostLanguageInfo, mNameEt.getText().toString(), mContentEt.getText().toString(), mUploadImgInfo, mAudioPath, mCountDownTravel);
                break;
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, @Nullable @org.jetbrains.annotations.Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK) {
            switch (requestCode) {
                case PictureConfig.CHOOSE_REQUEST:
                    List<LocalMedia> selectList = PictureSelector.obtainSelectorList(data);
//                    PicassoUtils.showImage(mUserHeadRiv, selectList.get(0).getCompressPath());
                    presenter.uploadFiles(selectList, "accost");
                    break;
            }
        }
    }

    @Override
    protected void initViews() {
        mActionBar.setTitleText("搭讪语");
        mActionBar.setTitleTextColor(getResources().getColor(R.color.color_333333));
        mAdapter = new AddPicAdapter();
        rvImages.setAdapter(mAdapter);

        if (null != mAccostLanguageInfo) {
            mNameEt.setText(mAccostLanguageInfo.getTempName());
            mContentEt.setText(mAccostLanguageInfo.getTempText());
            mSelectList.add(mAccostLanguageInfo.getTempImg());
            mUploadImgInfo = new UploadImgInfo();
            mUploadImgInfo.setTumhImgUrl(mAccostLanguageInfo.getTempImgThum());
            mUploadImgInfo.setImgUrl(mAccostLanguageInfo.getTempImg());

            mAudioPath = mAccostLanguageInfo.getVoiceUrl();
            if (!TextUtils.isEmpty(mAccostLanguageInfo.getVoiceTimeLength())) {
                mCountDownTravel = Integer.valueOf(mAccostLanguageInfo.getVoiceTimeLength());
                updateAudioUI(true);
            }
        } else {
            mSelectList.add("");
        }
        
        // 初始化录音UI状态
        updateRecordStatus("长按录音", false);

        mAdapter.setItems(mSelectList);
        mAdapter.showDelete(true);

        mAdapter.addOnItemChildClickListener(R.id.add_layout, (baseQuickAdapter, view, position) -> {
            choicePic(1);
        });

//        mAdapter.addOnItemChildLongClickListener(R.id.iv_thum, (baseQuickAdapter, view, position) -> {
//            mAdapter.showDelete(true);
//            return true;
//        });

        mAdapter.addOnItemChildClickListener(R.id.iv_thum, (baseQuickAdapter, view, position) -> {
            String info = (String) baseQuickAdapter.getItem(position);
            if (!TextUtils.isEmpty(info)) {
                ArrayList<DiaplayPicInfo> mDiaplayPicInfos = new ArrayList<>();
                mDiaplayPicInfos.add(new DiaplayPicInfo(info));
                DischargedPicBrowserActivity.start(AddAccostLanguageUI.this, mDiaplayPicInfos, position);

            }
        });

        mAdapter.addOnItemChildClickListener(R.id.delete_tv, (baseQuickAdapter, view, position) -> {
            mUploadImgInfo = null;
            mSelectList.remove(position);
            mSelectList.add("");
            mAdapter.setItems(mSelectList);
            mAdapter.notifyDataSetChanged();
        });

        mAudioPlayer = new AudioPlayer(AddAccostLanguageUI.this);
        mAudioPlayer.setOnPlaybackListener(new AudioPlayer.OnPlaybackListener() {
            @Override
            public void onPlaybackStateChanged(boolean isPlaying, boolean isPaused) {
                runOnUiThread(() -> {
                    if (isPlaying && !isPaused) {
                        mPlayAudioBtn.setText("停止");
//                        isPlaying = true;
                    } else {
                        mPlayAudioBtn.setText("播放");
                        AddAccostLanguageUI.this.isPlaying = false;
                    }
                });
            }

            @Override
            public void onProgressUpdate(int currentSeconds, int totalSeconds, int remainingSeconds) {
                runOnUiThread(() -> {
                    // 显示剩余时间倒计时
                    mAudioLongTv.setText(String.format("%02d:%02d", remainingSeconds / 60, remainingSeconds % 60));
                });
            }

            @Override
            public void onPlaybackCompleted() {
                runOnUiThread(() -> {
                    mPlayAudioBtn.setText("播放");
                    AddAccostLanguageUI.this.isPlaying = false;
                    stopPlayStatusCheck();
                    // 恢复显示总时长
                    mAudioLongTv.setText(String.format("%02d:%02d", mCountDownTravel / 60, mCountDownTravel % 60));
                });
            }

            @Override
            public void onPlaybackError(String error) {
                runOnUiThread(() -> {
                    LogUtil.e("AudioPlayer", "播放错误: " + error);
                    Toaster.show("音频播放失败: " + error);
                    mPlayAudioBtn.setText("播放");
                    AddAccostLanguageUI.this.isPlaying = false;
                    stopPlayStatusCheck();
                });
            }
            
            @Override
            public void onBufferingUpdate(int percent) {
                // 网络音频缓冲进度更新，可以在这里显示缓冲进度
                LogUtil.e("AudioPlayer", "缓冲进度: " + percent + "%");
            }
        });
        mRippleView.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                switch (event.getAction()) {
                    case MotionEvent.ACTION_DOWN:
                        // 检查录音和存储权限
                        XXPermissions.with(AddAccostLanguageUI.this)
                                .permission(Permission.RECORD_AUDIO, Permission.WRITE_EXTERNAL_STORAGE)
                                .interceptor(new PermissionInterceptor())
                                .request(new OnPermissionCallback() {
                                    @Override
                                    public void onGranted(List<String> permissions, boolean all) {
                                        if (all) {
                                            mRippleView.start();
                                            startRecord();
                                        } else {
                                            Toaster.show("需要录音和存储权限才能使用此功能");
                                        }
                                    }

                                    @Override
                                    public void onDenied(List<String> permissions, boolean never) {
                                        if (never) {
                                            Toaster.show("权限被永久拒绝，请到设置中手动开启");
                                        } else {
                                            Toaster.show("需要录音和存储权限才能使用此功能");
                                        }
                                    }
                                });
                        break;
                    case MotionEvent.ACTION_UP:
                        if (isRecording) {
                            mRippleView.stop();
                            stopRecord();
                        }
                        break;
                }
                return true;
            }
        });
    }

    RecordManager.OnVolume onVolume = new RecordManager.OnVolume() {
        @Override
        public void onVolume(int db) {
            mRippleView.setDiffuseSpeed(db);

        }
    };

    private void startRecord() {
        try {
            // 确保外部缓存目录存在
            File cacheDir = getExternalCacheDir();
            if (cacheDir == null) {
                cacheDir = getCacheDir(); // 使用内部缓存目录作为备选
            }
            
            if (!cacheDir.exists()) {
                cacheDir.mkdirs();
            }
            
            mAudioFilePath = cacheDir.getPath() + "/" + System.currentTimeMillis() + ".aac";
            File audioFile = new File(mAudioFilePath);
            
            // 确保父目录存在
            File parentDir = audioFile.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                parentDir.mkdirs();
            }
            
            manager = new RecordManager(audioFile, onVolume);
            manager.startRecord();
            presenter.startRecord();
            
            // 开始录音状态更新
            isRecording = true;
            mRecordStartTime = System.currentTimeMillis();
            updateRecordStatus("录音中...", true);
            startRecordTimer();
            
        } catch (Exception e) {
            LogUtil.e("StartRecord Error: " + e.getMessage());
            Toaster.show("录音初始化失败，请重试");
            isRecording = false;
            updateRecordStatus("长按录音", false);
            mRippleView.stop();
        }
    }

    private void stopRecord() {
        try {
            presenter.stopRecord();
            isRecording = false;
            stopRecordTimer();
            
            if (null != manager) {
                manager.stopRecord(new RecordManager.OnRecordFinish() {
                    @Override
                    public void onRecordFinishCallback(long time, String filePath) {
                        try {
                            // 检查录音是否成功
                            if (filePath == null || TextUtils.isEmpty(filePath)) {
                                Toaster.show("录音失败，请重试");
                                updateRecordStatus("长按录音", false);
                                updateAudioUI(false);
                                // 清理可能存在的临时文件
                                if (!TextUtils.isEmpty(mAudioFilePath)) {
                                    AppHelper.delete(mAudioFilePath);
                                }
                                return;
                            }
                            
                            mCountDownTravel = (int) (time / 1000);
                            if (mCountDownTravel < 3) {
                                Toaster.show("时间太短");
                                if (!TextUtils.isEmpty(filePath))
                                    AppHelper.delete(filePath);
                                updateRecordStatus("长按录音", false);
                                updateAudioUI(false);
                            } else {
                                // 更新音频文件路径为实际录音文件路径
                                mAudioFilePath = filePath;
                                updateRecordStatus("录音完成", false);
                                updateAudioUI(true);
                                if (!TextUtils.isEmpty(mAudioFilePath)) {
                                    presenter.uploadAudio(mAudioFilePath, "audio");
                                }
                            }
                        } catch (Exception e) {
                            LogUtil.e("StopRecord Callback Error: " + e.getMessage());
                            updateRecordStatus("录音失败", false);
                            updateAudioUI(false);
                        }
                    }
                 });
            } else {
                // manager为null的情况
                updateRecordStatus("长按录音", false);
                updateAudioUI(false);
            }
        } catch (Exception e) {
            LogUtil.e("StopRecord Error: " + e.getMessage());
            isRecording = false;
            updateRecordStatus("录音失败", false);
            updateAudioUI(false);
        }
    }

    private void updateRecordStatus(String status, boolean recording) {
        mRecordStatusTv.setText(status);
        // 可以根据需要添加更多UI状态更新
    }

    private void updateAudioUI(boolean hasAudio) {
        mAudioControlLayout.setVisibility(hasAudio ? View.VISIBLE : View.GONE);
        mAudioLongTv.setVisibility(hasAudio ? View.VISIBLE : View.GONE);
        if (hasAudio) {
            mAudioLongTv.setText(String.format("%02d:%02d", mCountDownTravel / 60, mCountDownTravel % 60));
            mPlayAudioBtn.setText("播放");
        }
    }

    private void startRecordTimer() {
        mAudioLongTv.setVisibility(View.VISIBLE);
        mRecordTimeRunnable = new Runnable() {
            @Override
            public void run() {
                if (isRecording) {
                    long currentTime = System.currentTimeMillis();
                    int seconds = (int) ((currentTime - mRecordStartTime) / 1000);
                    mAudioLongTv.setText(String.format("%02d:%02d", seconds / 60, seconds % 60));
                    mHandler.postDelayed(this, 1000);
                }
            }
        };
        mHandler.post(mRecordTimeRunnable);
    }

    private void stopRecordTimer() {
        if (mRecordTimeRunnable != null) {
            mHandler.removeCallbacks(mRecordTimeRunnable);
        }
    }

    private void playOrStopAudio() {
        try {
            if (isPlaying) {
                // 停止播放
                if (null != mAudioPlayer) {
                    mAudioPlayer.stop();
                }
                stopPlayStatusCheck();
                mPlayAudioBtn.setText("播放");
                isPlaying = false;
                // 恢复显示总时长
                mAudioLongTv.setText(String.format("%02d:%02d", mCountDownTravel / 60, mCountDownTravel % 60));
            } else {
                // 开始播放
                String playPath = null;
                
                if (!TextUtils.isEmpty(mAudioPath)) {
                    playPath = mAudioPath;
                } else if (!TextUtils.isEmpty(mAudioFilePath)) {
                    playPath = mAudioFilePath;
                }
                
                if (!TextUtils.isEmpty(playPath)) {
                    if (null != mAudioPlayer) {
                        // 先停止之前的播放
                        mAudioPlayer.stop();
                        
                        // 使用智能播放方法，自动判断本地文件还是网络URL
                        LogUtil.e("PlayAudio", "尝试播放音频: " + playPath);
                        mAudioPlayer.play(playPath);
                    }
                } else {
                    Toaster.show("没有可播放的音频");
                }
            }
        } catch (Exception e) {
            LogUtil.e("PlayOrStopAudio Error: " + e.getMessage());
            Toaster.show("音频播放失败");
            mPlayAudioBtn.setText("播放");
            isPlaying = false;
            stopPlayStatusCheck();
        }
    }

    private void startPlayStatusCheck() {
        // AudioPlayer已经通过回调处理播放状态，这个方法保留为空以保持兼容性
    }

    private void stopPlayStatusCheck() {
        if (mPlayStatusCheckRunnable != null) {
            mHandler.removeCallbacks(mPlayStatusCheckRunnable);
        }
    }

    private void deleteAudio() {
        mAudioPath = null;
        mAudioFilePath = null;
        mCountDownTravel = 0;
        updateAudioUI(false);
        updateRecordStatus("长按录音", false);
        if (null != mAudioPlayer) {
            mAudioPlayer.stop();
        }
        stopPlayStatusCheck();
        isPlaying = false;
    }

    @Override
    protected AccostLanguagePresenter setPresenter() {
        return new AccostLanguagePresenter(this);
    }

    @Override
    public FragmentActivity context() {
        return this;
    }

    @Override
    public void showUploadImgResult(List<UploadImgInfo> data) {
        if (!LibCollections.isEmpty(data)) {
            mUploadImgInfo = data.get(0);
            mSelectList = LibCollections.removeEmptyStrings(mSelectList);
            mSelectList.add(mUploadImgInfo.getImgUrl());
            mAdapter.setItems(mSelectList);
            mAdapter.notifyDataSetChanged();
        }
    }

    @Override
    public void showAccostWomanList(List<AccostLanguageInfo> accostInfo) {

    }

    @Override
    public void showAccostManList(AccostManLanguageInfo accostInfo) {

    }

    @Override
    public void showupdateCallback(int code) {
        if (200 == code)
            finish();
    }

    @Override
    public void showDeleteCallback(Integer code) {

    }

    @Override
    public void showDefaultCallback(Integer code) {

    }

    @Override
    public void showCountDownFinish() {
        mRippleView.stop();
        if (null != mAudioPlayer) {
            mAudioPlayer.stop();
            if (!TextUtils.isEmpty(mAudioFilePath)) {
                mAudioPlayer.play(mAudioFilePath);
            }
        }
        manager.stopRecord(new RecordManager.OnRecordFinish() {
            @Override
            public void onRecordFinishCallback(long time, String filePath) {
                mCountDownTravel = (int) (time / 1000);
                presenter.uploadAudio(filePath, "audio");
            }
        });
    }

    @Override
    public void showUploadAudioResult(List<String> result) {
        mAudioPath = result.get(0);
    }

    @Override
    public void showCountDownTravel(int l) {
        mAudioLongTv.setText(String.format("%02d:%02d", l / 60, l % 60));
    }

    @Override
    public void showUpdateCallback(BaseBean base) {

    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        try {
            // 停止录音相关资源
            isRecording = false;
            stopRecordTimer();
            if (null != manager) {
                manager.cleanup(); // 使用新增的cleanup方法完全清理资源
                manager = null;
            }
            
            // 停止播放相关资源
            isPlaying = false;
            stopPlayStatusCheck();
            if (null != mAudioPlayer) {
                mAudioPlayer.release();
                mAudioPlayer = null;
            }
            
            // 清理Handler回调
            if (null != mHandler) {
                mHandler.removeCallbacksAndMessages(null);
            }
            
            // 停止动画
            if (null != mRippleView) {
                mRippleView.stop();
            }
        } catch (Exception e) {
            LogUtil.e("OnDestroy Error: " + e.getMessage());
        }
    }

    @Override
    protected boolean isUseEventBus() {
        return true;
    }
}
