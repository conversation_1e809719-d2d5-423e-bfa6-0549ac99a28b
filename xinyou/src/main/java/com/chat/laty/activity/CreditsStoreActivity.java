package com.chat.laty.activity;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.viewpager2.adapter.FragmentStateAdapter;
import androidx.viewpager2.widget.ViewPager2;

import com.google.android.material.tabs.TabLayout;
import com.chat.laty.R;
import com.chat.laty.controllers.CreditsController;
import com.chat.laty.entity.CreditsStoreInfoModel;
import com.chat.laty.fragment.CreditsStoreFragment;
import com.chat.laty.mvp.BasePresenterActivity;
import com.chat.laty.presenters.CreditsStorePresenter;
import com.chat.laty.view.CustomActionBar;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import butterknife.BindView;

public class CreditsStoreActivity extends BasePresenterActivity<CreditsStorePresenter> implements CreditsController.ICreditsStoreView {

    public static void startAct(Context context) {
        Intent intent = new Intent(context, CreditsStoreActivity.class);
        context.startActivity(intent);
    }

    @BindView(R.id.custom_bar)
    CustomActionBar customBar;

    @BindView(R.id.tv_value)
    TextView tvValue;

    @BindView(R.id.tv_text_value)
    TextView tvTextValue;
    @BindView(R.id.tv_audio_value)
    TextView tvAudioValue;
    @BindView(R.id.tv_video_value)
    TextView tvVideoValue;

    @BindView(R.id.tab_layout)
    TabLayout tabLayout;

    @BindView(R.id.view_pager)
    ViewPager2 viewPager2;


    private ActivityResultLauncher launcher;

    @Override
    protected void initDatas() {
        launcher = registerForActivityResult(new ActivityResultContracts.StartActivityForResult(), result -> {
            if (result.getResultCode() == RESULT_OK) {
                presenter.getCreditsStoreInfo();
            }
        });
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_credits_store_layout;
    }

    CreditsStoreInfoModel model;

    List<CreditsStoreFragment> fragments = new ArrayList<>();

    ArrayList<String> titles = new ArrayList<>(Arrays.asList("文字条数", "语音时长", "视频时长"));

    @Override
    protected void initViews() {

        customBar.setTitleText("积分商城");
        customBar.setRightText("兑换记录");
        customBar.setAllColor(ContextCompat.getColor(this, R.color.white));

        customBar.addFunction(CustomActionBar.FUNCTION_TEXT_RIGHT);
        customBar.setOnActionBarClickListerner(function -> {
            if (function == CustomActionBar.FUNCTION_BUTTON_LEFT) {
                CreditsStoreActivity.this.finish();
                return true;
            } else if (function == CustomActionBar.FUNCTION_TEXT_RIGHT) {
                CreditsRecordsActivity.startAct(CreditsStoreActivity.this);
                return true;
            }
            return false;
        });

        for (int i = 0; i < titles.size(); i++) {
            View view = getLayoutInflater().inflate(R.layout.tab_layout_item_view, null);

            TextView textView = view.findViewById(R.id.tv_title);
            ImageView imageView = view.findViewById(R.id.iv_indicator);
            if (textView != null) {
                textView.setText(titles.get(i));
                toggleTextStyle(textView, i == 0);
            }
            if (imageView != null && i == 0) {
                imageView.setVisibility(View.VISIBLE);
            }

            TabLayout.Tab tab = tabLayout.newTab();
            tab.setCustomView(view);
            tabLayout.addTab(tab);

            fragments.add(CreditsStoreFragment.newInstance(i + 1));
        }

        tabLayout.addOnTabSelectedListener(new TabLayout.OnTabSelectedListener() {
            @Override
            public void onTabSelected(TabLayout.Tab tab) {
                toggleTextStyle(tab.getCustomView(), true);
                viewPager2.setCurrentItem(tab.getPosition());
            }

            @Override
            public void onTabUnselected(TabLayout.Tab tab) {
                toggleTextStyle(tab.getCustomView(), false);
            }

            @Override
            public void onTabReselected(TabLayout.Tab tab) {

            }
        });

        viewPager2.setAdapter(new Adapter(this));
        viewPager2.setUserInputEnabled(false);

        presenter.getCreditsStoreInfo();
    }

    private void toggleTextStyle(View view, boolean selected) {

        TextView textView = view.findViewById(R.id.tv_title);
        ImageView imageView = view.findViewById(R.id.iv_indicator);

        if (textView != null) {
            textView.setSelected(selected);
            textView.setTextSize(selected ? 18 : 15);
        }
        if (imageView != null) {
            imageView.setVisibility(selected ? View.VISIBLE : View.INVISIBLE);
        }
    }

    @Override
    protected CreditsStorePresenter setPresenter() {
        return new CreditsStorePresenter(this, null, null);
    }


    @SuppressLint("SetTextI18n")
    @Override
    public void getInfoSucceed(CreditsStoreInfoModel data) {
        this.model = data;

        tvValue.setText(data.getIntegralBalance() + "");
        tvTextValue.setText(data.getFreeTextNum() + "");
        tvAudioValue.setText(data.getFreeVoiceNum() + "");
        tvVideoValue.setText(data.getFreeVideoNum() + "");

        fragments.get(0).addLauncher(launcher).setDataList(data.getTextList());
        fragments.get(1).addLauncher(launcher).setDataList(data.getVoiceList());
        fragments.get(2).addLauncher(launcher).setDataList(data.getVideoList());
    }

    @Override
    public void getInfoFailed() {
    }

    @Override
    public FragmentActivity context() {
        return this;
    }

    private class Adapter extends FragmentStateAdapter {


        public Adapter(@NonNull FragmentActivity fragmentActivity) {
            super(fragmentActivity);
        }

        @NonNull
        @Override
        public Fragment createFragment(int position) {
            return fragments.get(position);
        }

        @Override
        public int getItemCount() {
            return fragments.size();
        }
    }
}