package com.chat.laty.activity;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.alibaba.fastjson.JSONArray;
import com.hjq.toast.Toaster;
import com.chat.laty.R;
import com.chat.laty.adapter.CommentDetailAdapter;
import com.chat.laty.adapter.NineGridViewAdapter;
import com.chat.laty.controllers.CommunityController;
import com.chat.laty.entity.BaseBean;
import com.chat.laty.entity.CommunityInfoModel;
import com.chat.laty.entity.MediaFileModel;
import com.chat.laty.mvp.BasePresenterActivity;
import com.chat.laty.presenters.CommunityPresenter;
import com.chat.laty.utils.KeyboardUtil;
import com.chat.laty.utils.LogUtil;
import com.chat.laty.utils.PicassoUtils;
import com.chat.laty.view.CustomActionBar;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;
import cn.xjc_soft.lib_utils.LibCollections;

public class MineDynamicDetailUI extends BasePresenterActivity<CommunityPresenter> implements CommunityController.IAccostView {

    @BindView(R.id.custom_bar)
    CustomActionBar customBar;

    @BindView(R.id.tv_user_name)
    TextView tvUserName;

    @BindView(R.id.iv_avatar)
    ImageView ivAvatar;
    @BindView(R.id.v_real_name)
    View vRealName;
    @BindView(R.id.v_phone)
    View vPhone;

    @BindView(R.id.tv_age)
    TextView tvAge;

    @BindView(R.id.iv_sex)
    ImageView ivSex;
    @BindView(R.id.v_sex)
    View vSex;
    @BindView(R.id.tv_user_info)
    TextView tvInfo;
    @BindView(R.id.tv_chat)
    TextView tvChat;
    @BindView(R.id.tv_accost)
    TextView tvAccost;
    @BindView(R.id.tv_time_info)
    TextView tvTimeInfo;
    @BindView(R.id.tv_collect_num)
    TextView tvCollectNum;
    @BindView(R.id.tv_chat_num)
    TextView tvChatNum;
    @BindView(R.id.tv_label)
    TextView tvLabel;
    //    @BindView(R.id.layout_nine_grid)
//    NineGridImageView nineGridView;
//    @BindView(R.id.rv_nine_grid)
//    GridView gridView;
    @BindView(R.id.rv_records)
    RecyclerView rvRecords;//    GridView gridView;
    @BindView(R.id.nine_grid)
    RecyclerView recycler;
    @BindView(R.id.v_input)
    View vInput;
    @BindView(R.id.et_input)
    EditText etInput;
    @BindView(R.id.btn_send)
    View tvSend;
    int type;

    String communityId;
    CommunityInfoModel model;

    CommentDetailAdapter adapter;

    private NineGridViewAdapter nineGridViewAdapter;

    public static void startActForResult(Activity context, CommunityInfoModel model, int type, int requestCode) {
        Intent intent = new Intent(context, MineDynamicDetailUI.class);
        intent.putExtra("model", model);
        intent.putExtra("type", type);
        context.startActivityForResult(intent, requestCode);
    }

    public static void startActById(Context context, String communityId) {
        Intent intent = new Intent(context, MineDynamicDetailUI.class);
        intent.putExtra("communityId", communityId);
        context.startActivity(intent);
    }

    @SuppressLint({"SetTextI18n", "NotifyDataSetChanged"})
    private void initView() {

        PicassoUtils.showImage(ivAvatar, model.getAvatar());
        LogUtil.e("响应结果", "==========" + model.toString());

        tvUserName.setText(model.getUserName());

        vRealName.setVisibility(TextUtils.equals("1", model.getIsReal()) ? View.VISIBLE : View.GONE);

        boolean isWoMen = TextUtils.equals("2", model.getSex());

        vPhone.setVisibility(!TextUtils.equals("2", model.getSex()) ? View.VISIBLE : View.GONE);

        int sexRes = isWoMen ? R.drawable.baseline_female_24 : R.drawable.baseline_male_24;
        ivSex.setImageDrawable(ContextCompat.getDrawable(this, sexRes));
        tvAge.setText(model.getAge());

        int dgRes = isWoMen ? R.drawable.bg_border_primary_color_40 : R.drawable.bg_border_5595ff_40;
        vSex.setBackground(ContextCompat.getDrawable(this, dgRes));

        tvInfo.setText(model.getTextContent());
        tvInfo.setVisibility(!TextUtils.isEmpty(model.getTextContent()) ? View.VISIBLE : View.GONE);


        boolean isAccost = TextUtils.equals("1", model.getIsAccost());
        tvChat.setVisibility(isAccost ? View.VISIBLE : View.GONE);
        tvAccost.setVisibility(isAccost ? View.VISIBLE : View.GONE);

        tvTimeInfo.setText(model.getCreateDate());

        int color = ContextCompat.getColor(this, TextUtils.equals("1", model.getIsLike()) ? R.color.colorPrimary : R.color.color_9d9d9d);
        tvCollectNum.setTextColor(color);

        tvCollectNum.setText(model.getLikeNum() > 0 ? "" + model.getLikeNum() : "点赞");
        tvChatNum.setText("" + model.getMentsNum());


        if (model.uploadImgInfos == null) {
            model.uploadImgInfos = new ArrayList<>();
        }
        if (model.uploadImgInfos.isEmpty()) {
            if (TextUtils.isEmpty(model.getVideoImg())) {
                List<MediaFileModel> list = JSONArray.parseArray(model.getFileUrl(), MediaFileModel.class);
                if (list == null) {
                    list = new ArrayList<>();
                }
                model.uploadImgInfos = list;
            } else {
                MediaFileModel info = new MediaFileModel();
                info.setImgUrl(model.getFileUrl());
                info.setTumhImgUrl(model.getVideoImg());
                model.uploadImgInfos.add(info);
            }
        }


        if (model.uploadImgInfos.isEmpty()) {
            recycler.removeAllViews();
            recycler.setAdapter(null);
        } else {

            final List<MediaFileModel> li = type == 2 ? model.uploadImgInfos.subList(0, 1) : model.uploadImgInfos;

            GridLayoutManager manager = new GridLayoutManager(this, li.size() > 2 ? 3 : li.size());
            recycler.setLayoutManager(manager);
            recycler.setNestedScrollingEnabled(false);

            NineGridViewAdapter nineGridViewAdapter = new NineGridViewAdapter(this);
            nineGridViewAdapter.setOnItemClickListener((baseQuickAdapter, view, i) -> {
                MediaFileModel model = li.get(i);
                nineGridViewAdapter.onClickListener(this, model, li);
            });
            nineGridViewAdapter.setList(li);

            recycler.setAdapter(nineGridViewAdapter);
        }

        if (!LibCollections.isEmpty(model.getCommentList()))
            tvLabel.setText("评论（" + model.getCommentList().size() + "）");

        adapter.setItems(model.getCommentList());
        adapter.notifyDataSetChanged();

        toggleEditVisibility(View.GONE, 0, 0, null);

    }

    @SuppressLint("NonConstantResourceId")
    @OnClick({R.id.iv_avatar, R.id.v_chat, R.id.tv_accost, R.id.v_collect})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.iv_avatar:
                UserCenterUI.start(this, model.getUserId());
                break;
            case R.id.tv_accost:
                String status = model.getIsAccost();
                if (TextUtils.equals("1", status)) {
                    ChatActivity.start(this, model.getUserId());
                } else {
                    presenter.accostToUser(model.getUserId(), model, 0);
                }
                break;
            case R.id.v_collect:
                presenter.likeToUser(model, 0);
                break;
            case R.id.v_chat:
                toggleEditVisibility(View.VISIBLE, 0, 0, null);
                break;
            case R.id.btn_send:
                String content = etInput.getText().toString();
                if (content.isEmpty()) {
                    Toaster.show("请输入评论内容");
                    return;
                }
                presenter.reply(content, model, 0, 0, null);
                break;

        }
    }

    private void toggleEditVisibility(int status, int index, int type, CommunityInfoModel.CommentInfoModel data) {
        if (status == View.GONE) {
            etInput.setText("");
            etInput.setHint("说点什么吧～");
            KeyboardUtil.hideSoftInput(this);
        } else if (status == View.VISIBLE) {
            etInput.requestFocus();
            KeyboardUtil.showSoftInput(this);
        }

        tvSend.setOnClickListener(v -> {
            String content = etInput.getText().toString();
            if (content.isEmpty()) {
                Toaster.show("请输入评论内容");
                return;
            }
            presenter.reply(content, model, index, type, data);
        });
    }

    @Override
    protected void initDatas() {
        if (getIntent() != null) {
            model = getIntent().getParcelableExtra("model");
            type = getIntent().getIntExtra("type", 0);
            communityId = getIntent().getStringExtra("communityId");
        }

        presenter.update(model, communityId, 0);
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_dynamic_detail_layout;
    }

    @Override
    protected void initViews() {
        customBar.setTitleText("动态详情");
        customBar.setAllColor(ContextCompat.getColor(this, R.color.black));

        customBar.setOnActionBarClickListerner(function -> {
            if (function == CustomActionBar.FUNCTION_BUTTON_LEFT) {
                MineDynamicDetailUI.this.finish();
                return true;
            }
            return false;
        });

        nineGridViewAdapter = new NineGridViewAdapter(this);
        recycler.setNestedScrollingEnabled(false);
        recycler.setAdapter(nineGridViewAdapter);


        adapter = new CommentDetailAdapter();
        adapter.setStateView(getLayoutInflater().inflate(R.layout.community_empty_layout, null));
        adapter.setStateViewEnable(true);

        adapter.setOnItemClickListener((baseQuickAdapter, view, i) -> {
            CommunityInfoModel.CommentInfoModel item = baseQuickAdapter.getItem(i);
            showInputDialogWithComment(i, 1, item);
        });

        rvRecords.setNestedScrollingEnabled(false);
        rvRecords.setAdapter(adapter);

    }

    private void showInputDialogWithComment(int index, int type, CommunityInfoModel.CommentInfoModel data) {
        String hint = "";
        if (type == 1) {
            hint = "回复 " + data.getUserName();
        }
        etInput.setHint(hint);
        toggleEditVisibility(View.VISIBLE, index, type, data);
        tvSend.setOnClickListener(v -> {
            String content = etInput.getText().toString();
            if (content.isEmpty()) {
                Toaster.show("请输入评论内容");
                return;
            }
            presenter.reply(content, model, index, type, data);
        });
    }

    @Override
    protected CommunityPresenter setPresenter() {
        return new CommunityPresenter(null, this);
    }

    @Override
    public FragmentActivity context() {
        return this;
    }

    @Override
    public void accostSucceed(CommunityInfoModel data, int index) {
        data.setIsAccost("1");
        initView();
    }


    @Override
    public void likeSucceed(CommunityInfoModel data, int index) {
        if (TextUtils.equals("1", data.getIsLike())) {
            data.setIsLike("0");
            data.setLikeNum(data.getLikeNum() > 0 ? data.getLikeNum() - 1 : 0);
        } else {
            data.setIsLike("1");
            data.setLikeNum(data.getLikeNum() + 1);
        }
        initView();
    }

    @Override
    public void updateSucceed(CommunityInfoModel model, int index) {
        this.model = model;
        initView();
    }

    @Override
    public void replySucceed(CommunityInfoModel model, int index, CommunityInfoModel.CommentInfoModel data) {
        presenter.update(model, communityId, index);
        toggleEditVisibility(View.GONE, 0, 0, null);
    }

    @Override
    public void failed() {
    }

    @Override
    public void deleteCallback(BaseBean callbackBean) {

    }
}
