package com.chat.laty.activity;

import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.view.View;
import android.widget.CompoundButton;
import android.widget.LinearLayout;

import androidx.fragment.app.FragmentActivity;

import com.allen.library.SuperTextView;
import com.hjq.permissions.OnPermissionCallback;
import com.hjq.permissions.Permission;
import com.hjq.permissions.XXPermissions;
import com.chat.laty.R;
import com.chat.laty.base.XYApplication;
import com.chat.laty.controllers.GeneralSettingController;
import com.chat.laty.entity.BaseBean;
import com.chat.laty.entity.GeneralSettingInfo;
import com.chat.laty.entity.RyUserInfo;
import com.chat.laty.entity.SimiInfo;
import com.chat.laty.entity.WebBeanInfo;
import com.chat.laty.mvp.BasePresenterActivity;
import com.chat.laty.presenters.GeneralSettingPresenter;
import com.chat.laty.utils.PermissionInterceptor;
import com.chat.laty.view.CustomActionBar;

import java.util.HashMap;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * <AUTHOR>
 * @date 2023/12/24 20:46
 * @description:
 */
public class AgreementUI extends BasePresenterActivity<GeneralSettingPresenter> implements GeneralSettingController.View {

    @BindView(R.id.custom_bar)
    CustomActionBar mActionBar;

    @BindView(R.id.location_stv)
    SuperTextView currentLocationStv;
    @BindView(R.id.city_stv)
    SuperTextView userCityStv;
    @BindView(R.id.line_stv)
    SuperTextView onlineStatuStv;

    @BindView(R.id.yinsi_layout)
    LinearLayout yinsiLl;

    @BindView(R.id.notice_stv)
    SuperTextView noticeStv;
    @BindView(R.id.camre_stv)
    SuperTextView camreStv;

    @BindView(R.id.audio_stv)
    SuperTextView audioStv;

    @BindView(R.id.location_prm_stv)
    SuperTextView locationStv;

    @BindView(R.id.shengri_stv)
    SuperTextView shengri_stv;

    @BindView(R.id.shengao_stv)
    SuperTextView shengao_stv;
    @BindView(R.id.tizhong_stv)
    SuperTextView tizhong_stv;
    @BindView(R.id.xueli_stv)
    SuperTextView xueli_stv;
    @BindView(R.id.zhiye_stv)
    SuperTextView zhiye_stv;
    @BindView(R.id.nianshouru_stv)
    SuperTextView nianshouru_stv;
    @BindView(R.id.dizhi_stv)
    SuperTextView dizhi_stv;
    @BindView(R.id.ganqing_stv)
    SuperTextView ganqing_stv;
    @BindView(R.id.juzhu_stv)
    SuperTextView juzhu_stv;

    public static void start(Context context) {
        Intent intent = new Intent(context, AgreementUI.class);
        context.startActivity(intent);
    }

    @Override
    protected void initDatas() {

    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_agreement_settings_activity;
    }

    @Override
    protected void initViews() {
        mActionBar.setTitleText("隐私设置");
        mActionBar.setTitleTextColor(getResources().getColor(R.color.color_333333));
        RyUserInfo userInfo = XYApplication.getCurrentUserInfo();
        yinsiLl.setVisibility(TextUtils.equals("1", userInfo.getSex()) ? View.GONE : View.VISIBLE);

        presenter.getSettings();

        presenter.getSimiInfo();
    }

    @Override
    protected void onResume() {
        super.onResume();
        presenter.getSettings();
        noticeStv.setSwitchIsChecked(XXPermissions.isGranted(AgreementUI.this, Permission.POST_NOTIFICATIONS));
        camreStv.setSwitchIsChecked(XXPermissions.isGranted(AgreementUI.this, Permission.CAMERA));
        audioStv.setSwitchIsChecked(XXPermissions.isGranted(AgreementUI.this, Permission.RECORD_AUDIO));

//        locationStv.setSwitchIsChecked(XXPermissions.isGranted(XYApplication.getAppApplicationContext(),Permission.ACCESS_FINE_LOCATION,Permission.ACCESS_COARSE_LOCATION));
        camreStv.setSwitchCheckedChangeListener(new SuperTextView.OnSwitchCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {

                if (isChecked) {
                    XXPermissions.with(AgreementUI.this).permission(Permission.CAMERA).interceptor(new PermissionInterceptor()).request(new OnPermissionCallback() {
                        @Override
                        public void onGranted(List<String> permissions, boolean all) {
                            if (!all) {
                                camreStv.setSwitchIsChecked(false);
                                return;
                            }
                            camreStv.setSwitchIsChecked(true);
                        }
                    });
                } else {
                    XXPermissions.startPermissionActivity(AgreementUI.this, Permission.CAMERA);
                }
            }
        });

        audioStv.setSwitchCheckedChangeListener(new SuperTextView.OnSwitchCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {

                if (isChecked) {
                    XXPermissions.with(XYApplication.getAppApplicationContext()).permission(Permission.RECORD_AUDIO).interceptor(new PermissionInterceptor()).request(new OnPermissionCallback() {
                        @Override
                        public void onGranted(List<String> permissions, boolean all) {
                            if (!all) {
                                audioStv.setSwitchIsChecked(false);
                                return;
                            }
                            audioStv.setSwitchIsChecked(true);
                        }
                    });
                } else {
                    XXPermissions.startPermissionActivity(AgreementUI.this, Permission.RECORD_AUDIO);
                }
            }
        });

        locationStv.setSwitchCheckedChangeListener(new SuperTextView.OnSwitchCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                HashMap<String, Object> params = new HashMap<>();
                params.put("hideLocation", isChecked ? 1 : 0);
                presenter.updateSettings(params);
            }
        });

        noticeStv.setSwitchCheckedChangeListener(new SuperTextView.OnSwitchCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {

                if (isChecked) {
                    XXPermissions.with(AgreementUI.this).permission(Permission.POST_NOTIFICATIONS).interceptor(new PermissionInterceptor()).request(new OnPermissionCallback() {
                        @Override
                        public void onGranted(List<String> permissions, boolean all) {
                            if (!all) {
                                noticeStv.setSwitchIsChecked(false);
                                return;
                            }
                            noticeStv.setSwitchIsChecked(true);
                        }
                    });
                } else {
                    XXPermissions.startPermissionActivity(AgreementUI.this, Permission.POST_NOTIFICATIONS);
                }
            }
        });
    }

    @OnClick({R.id.privacy_policy_stv, R.id.service_agreement_stv, R.id.black_list_stv,})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.privacy_policy_stv://通用设置
                presenter.getAccordByNum(12);
                break;

            case R.id.service_agreement_stv://通用设置
                presenter.getAccordByNum(10);
                break;

            case R.id.black_list_stv://通用设置
                BlacklistActivity.start(AgreementUI.this);
                break;
        }
    }

    SuperTextView.OnSwitchCheckedChangeListener switchChecked = new SuperTextView.OnSwitchCheckedChangeListener() {
        @Override
        public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
        }
    };

    @Override
    protected GeneralSettingPresenter setPresenter() {
        return new GeneralSettingPresenter(this);
    }

    @Override
    public FragmentActivity context() {
        return this;
    }

    @Override
    public void showGeneralSetting(GeneralSettingInfo info) {
        userCityStv.setSwitchIsChecked(info.getHideCity() == 1);
        currentLocationStv.setSwitchIsChecked(info.getHideLocation() == 1);
        locationStv.setSwitchIsChecked(info.getHideLocation() == 1);
        onlineStatuStv.setSwitchIsChecked(info.getHideOnline() == 1);
        showStatus();
    }

    @Override
    public void showWebInfo(WebBeanInfo info) {
        WebViewActivity.startActivity(AgreementUI.this, "", info.getUrl());
    }

    @Override
    public void showDeleteCallback(BaseBean baseBean) {

    }

    @Override
    public void showLogoutCallback(BaseBean uploadBean) {

    }

    private void showStatus() {
        userCityStv.setSwitchCheckedChangeListener(new SuperTextView.OnSwitchCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                HashMap<String, Object> params = new HashMap<>();
                params.put("hideCity", isChecked ? 1 : 0);
                presenter.updateSettings(params);
            }
        });

        currentLocationStv.setSwitchCheckedChangeListener(new SuperTextView.OnSwitchCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                HashMap<String, Object> params = new HashMap<>();
                params.put("hideLocation", isChecked ? 1 : 0);
                presenter.updateSettings(params);
            }
        });

        onlineStatuStv.setSwitchCheckedChangeListener(new SuperTextView.OnSwitchCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                HashMap<String, Object> params = new HashMap<>();
                params.put("hideOnline", isChecked ? 1 : 0);
                presenter.updateSettings(params);
            }
        });
    }

    @Override
    public void getSimiInfo(SimiInfo simiInfo) {
        shengri_stv.setSwitchIsChecked(simiInfo.getShengri() != null && simiInfo.getShengri().equals("1"));
        shengao_stv.setSwitchIsChecked(simiInfo.getShengao() != null && simiInfo.getShengao().equals("1"));
        tizhong_stv.setSwitchIsChecked(simiInfo.getTizhong() != null && simiInfo.getTizhong().equals("1"));
        xueli_stv.setSwitchIsChecked(simiInfo.getXueli() != null && simiInfo.getXueli().equals("1"));
        zhiye_stv.setSwitchIsChecked(simiInfo.getZhiye() != null && simiInfo.getZhiye().equals("1"));
        nianshouru_stv.setSwitchIsChecked(simiInfo.getNianshouru() != null && simiInfo.getNianshouru().equals("1"));
        dizhi_stv.setSwitchIsChecked(simiInfo.getDizhi() != null && simiInfo.getDizhi().equals("1"));
        ganqing_stv.setSwitchIsChecked(simiInfo.getGanqing() != null && simiInfo.getGanqing().equals("1"));
        juzhu_stv.setSwitchIsChecked(simiInfo.getJuzhu() != null && simiInfo.getJuzhu().equals("1"));
        showSimiStatus();
    }

   private void showSimiStatus(){
        shengri_stv.setSwitchCheckedChangeListener(new SuperTextView.OnSwitchCheckedChangeListener(){
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                HashMap<String, Object> params = new HashMap<>();
                params.put("shengri", isChecked ? "1" : "0");
                presenter.updateSimiInfo(params);
            }
        });

       shengao_stv.setSwitchCheckedChangeListener(new SuperTextView.OnSwitchCheckedChangeListener(){
           @Override
           public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
               HashMap<String, Object> params = new HashMap<>();
               params.put("shengao", isChecked ? "1" : "0");
               presenter.updateSimiInfo(params);
           }
       });

       tizhong_stv.setSwitchCheckedChangeListener(new SuperTextView.OnSwitchCheckedChangeListener(){
           @Override
           public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
               HashMap<String, Object> params = new HashMap<>();
               params.put("tizhong", isChecked ? "1" : "0");
               presenter.updateSimiInfo(params);
           }
       });

       xueli_stv.setSwitchCheckedChangeListener(new SuperTextView.OnSwitchCheckedChangeListener(){
           @Override
           public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
               HashMap<String, Object> params = new HashMap<>();
               params.put("xueli", isChecked ? "1" : "0");
               presenter.updateSimiInfo(params);
           }
       });

       zhiye_stv.setSwitchCheckedChangeListener(new SuperTextView.OnSwitchCheckedChangeListener(){
           @Override
           public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
               HashMap<String, Object> params = new HashMap<>();
               params.put("zhiye", isChecked ? "1" : "0");
               presenter.updateSimiInfo(params);
           }
       });

       nianshouru_stv.setSwitchCheckedChangeListener(new SuperTextView.OnSwitchCheckedChangeListener(){
           @Override
           public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
               HashMap<String, Object> params = new HashMap<>();
               params.put("nianshouru", isChecked ? "1" : "0");
               presenter.updateSimiInfo(params);
           }
       });

       dizhi_stv.setSwitchCheckedChangeListener(new SuperTextView.OnSwitchCheckedChangeListener(){
           @Override
           public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
               HashMap<String, Object> params = new HashMap<>();
               params.put("dizhi", isChecked ? "1" : "0");
               presenter.updateSimiInfo(params);
           }
       });

       ganqing_stv.setSwitchCheckedChangeListener(new SuperTextView.OnSwitchCheckedChangeListener(){
           @Override
           public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
               HashMap<String, Object> params = new HashMap<>();
               params.put("ganqing", isChecked ? "1" : "0");
               presenter.updateSimiInfo(params);
           }
       });

       juzhu_stv.setSwitchCheckedChangeListener(new SuperTextView.OnSwitchCheckedChangeListener(){
           @Override
           public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
               HashMap<String, Object> params = new HashMap<>();
               params.put("juzhu", isChecked ? "1" : "0");
               presenter.updateSimiInfo(params);
           }
       });


   }
}
