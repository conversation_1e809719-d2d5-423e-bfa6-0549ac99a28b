package com.chat.laty.activity;

import android.content.Context;
import android.content.Intent;
import android.widget.CompoundButton;

import androidx.fragment.app.FragmentActivity;

import com.allen.library.SuperTextView;
import com.chat.laty.R;
import com.chat.laty.controllers.GeneralSettingController;
import com.chat.laty.entity.BaseBean;
import com.chat.laty.entity.GeneralSettingInfo;
import com.chat.laty.entity.SimiInfo;
import com.chat.laty.entity.WebBeanInfo;
import com.chat.laty.mvp.BasePresenterActivity;
import com.chat.laty.presenters.GeneralSettingPresenter;
import com.chat.laty.view.CustomActionBar;

import java.util.HashMap;

import butterknife.BindView;

/**
 * <AUTHOR>
 * @date 2023/12/24 20:46
 * @description:
 */
public class DisturbSettingsUI extends BasePresenterActivity<GeneralSettingPresenter> implements GeneralSettingController.View {

    @BindView(R.id.custom_bar)
    CustomActionBar mActionBar;
    @BindView(R.id.video_call_disturb_stv)
    SuperTextView videoCallDisturbStv;
    @BindView(R.id.voice_call_disturb_stv)
    SuperTextView voiceCallDisturbStv;

    public static void start(Context context) {
        Intent intent = new Intent(context, DisturbSettingsUI.class);
        context.startActivity(intent);
    }

    @Override
    protected void initDatas() {

    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_disturb_settings_activity;
    }

    @Override
    protected void initViews() {
        mActionBar.setTitleText("免打扰设置");
        mActionBar.setTitleTextColor(getResources().getColor(R.color.color_333333));
        presenter.getSettings();
    }


    @Override
    protected GeneralSettingPresenter setPresenter() {
        return new GeneralSettingPresenter(this);
    }

    @Override
    public FragmentActivity context() {
        return this;
    }

    @Override
    public void showGeneralSetting(GeneralSettingInfo info) {
        voiceCallDisturbStv.setSwitchIsChecked(info.getVoiceDisturb() == 1);
        videoCallDisturbStv.setSwitchIsChecked(info.getVideoDisturb() == 1);
        showStatus();
    }

    @Override
    public void showWebInfo(WebBeanInfo info) {

    }

    @Override
    public void showDeleteCallback(BaseBean baseBean) {

    }

    @Override
    public void showLogoutCallback(BaseBean uploadBean) {

    }

    @Override
    public void getSimiInfo(SimiInfo simiInfo) {

    }

    private void showStatus() {


        voiceCallDisturbStv.setSwitchCheckedChangeListener(new SuperTextView.OnSwitchCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                HashMap<String, Object> params = new HashMap<>();
                params.put("voiceDisturb", isChecked ? 1 : 0);
                presenter.updateSettings(params);
            }
        });

        videoCallDisturbStv.setSwitchCheckedChangeListener(new SuperTextView.OnSwitchCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                HashMap<String, Object> params = new HashMap<>();
                params.put("videoDisturb", isChecked ? 1 : 0);
                presenter.updateSettings(params);
            }
        });


    }
}
