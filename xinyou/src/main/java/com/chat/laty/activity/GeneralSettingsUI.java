package com.chat.laty.activity;

import android.content.Context;
import android.content.Intent;
import android.widget.CompoundButton;

import androidx.fragment.app.FragmentActivity;

import com.allen.library.SuperTextView;
import com.chat.laty.R;
import com.chat.laty.controllers.GeneralSettingController;
import com.chat.laty.entity.BaseBean;
import com.chat.laty.entity.GeneralSettingInfo;
import com.chat.laty.entity.SimiInfo;
import com.chat.laty.entity.WebBeanInfo;
import com.chat.laty.mvp.BasePresenterActivity;
import com.chat.laty.presenters.GeneralSettingPresenter;
import com.chat.laty.view.CustomActionBar;

import java.util.HashMap;

import butterknife.BindView;
import io.rong.imkit.config.RongConfigCenter;

/**
 * <AUTHOR>
 * @date 2023/12/24 20:46
 * @description:
 */
public class GeneralSettingsUI extends BasePresenterActivity<GeneralSettingPresenter> implements GeneralSettingController.View {

    @BindView(R.id.custom_bar)
    CustomActionBar mActionBar;
    @BindView(R.id.newmsg_sound_stv)
    SuperTextView newmsgSoundStv;
    @BindView(R.id.newmsg_vibrate_stv)
    SuperTextView newmsgVibrateStv;
    @BindView(R.id.video_call_disturb_stv)
    SuperTextView videoCallDisturbStv;
    @BindView(R.id.voice_call_disturb_stv)
    SuperTextView voiceCallDisturbStv;
    @BindView(R.id.current_location_stv)
    SuperTextView currentLocationStv;
    @BindView(R.id.user_city_stv)
    SuperTextView userCityStv;
    @BindView(R.id.online_statu_stv)
    SuperTextView onlineStatuStv;
    @BindView(R.id.privacy_policy_stv)
    SuperTextView privacyPolicyStv;
    @BindView(R.id.service_agreement_stv)
    SuperTextView serviceAgreementStv;

    public static void start(Context context) {
        Intent intent = new Intent(context, GeneralSettingsUI.class);
        context.startActivity(intent);
    }

    @Override
    protected void initDatas() {

    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_general_settings_activity;
    }

    @Override
    protected void initViews() {
        mActionBar.setTitleText("基本设置");
        mActionBar.setTitleTextColor(getResources().getColor(R.color.color_333333));
        presenter.getSettings();

        newmsgSoundStv.setSwitchIsChecked(RongConfigCenter.featureConfig().isSoundInForeground());
        newmsgVibrateStv.setSwitchIsChecked(RongConfigCenter.featureConfig().isVibrateInForeground());
    }


    @Override
    protected GeneralSettingPresenter setPresenter() {
        return new GeneralSettingPresenter(this);
    }

    @Override
    public FragmentActivity context() {
        return this;
    }

    @Override
    public void showGeneralSetting(GeneralSettingInfo info) {
        userCityStv.setSwitchIsChecked(info.getHideCity() == 1);
        currentLocationStv.setSwitchIsChecked(info.getHideLocation() == 1);
        onlineStatuStv.setSwitchIsChecked(info.getHideOnline() == 1);
        voiceCallDisturbStv.setSwitchIsChecked(info.getVoiceDisturb() == 1);
        videoCallDisturbStv.setSwitchIsChecked(info.getVideoDisturb() == 1);
        showStatus();
    }

    @Override
    public void showWebInfo(WebBeanInfo info) {

    }

    @Override
    public void showDeleteCallback(BaseBean baseBean) {

    }

    @Override
    public void showLogoutCallback(BaseBean uploadBean) {

    }

    @Override
    public void getSimiInfo(SimiInfo simiInfo) {

    }

    private void showStatus() {
        userCityStv.setSwitchCheckedChangeListener(new SuperTextView.OnSwitchCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                HashMap<String, Object> params = new HashMap<>();
                params.put("hideCity", isChecked ? 1 : 0);
                presenter.updateSettings(params);
            }
        });

        voiceCallDisturbStv.setSwitchCheckedChangeListener(new SuperTextView.OnSwitchCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                HashMap<String, Object> params = new HashMap<>();
                params.put("voiceDisturb", isChecked ? 1 : 0);
                presenter.updateSettings(params);
            }
        });

        videoCallDisturbStv.setSwitchCheckedChangeListener(new SuperTextView.OnSwitchCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                HashMap<String, Object> params = new HashMap<>();
                params.put("videoDisturb", isChecked ? 1 : 0);
                presenter.updateSettings(params);
            }
        });

        currentLocationStv.setSwitchCheckedChangeListener(new SuperTextView.OnSwitchCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                HashMap<String, Object> params = new HashMap<>();
                params.put("hideLocation", isChecked ? 1 : 0);
                presenter.updateSettings(params);
            }
        });

        onlineStatuStv.setSwitchCheckedChangeListener(new SuperTextView.OnSwitchCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                HashMap<String, Object> params = new HashMap<>();
                params.put("hideOnline", isChecked ? 1 : 0);
                presenter.updateSettings(params);
            }
        });

        newmsgSoundStv.setSwitchCheckedChangeListener(new SuperTextView.OnSwitchCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                RongConfigCenter.featureConfig().setSoundInForeground(isChecked);
            }
        });

        newmsgVibrateStv.setSwitchCheckedChangeListener(new SuperTextView.OnSwitchCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                RongConfigCenter.featureConfig().setVibrateInForeground(isChecked);
            }
        });

    }
}
