package com.chat.laty.activity;

import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;

import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chat.laty.R;
import com.chat.laty.adapter.ManAccostLanguageAdapter;
import com.chat.laty.base.XYApplication;
import com.chat.laty.controllers.AccostLanguageController;
import com.chat.laty.entity.AccostLanguageInfo;
import com.chat.laty.entity.AccostManLanguageInfo;
import com.chat.laty.entity.BaseBean;
import com.chat.laty.entity.RyUserInfo;
import com.chat.laty.entity.UploadImgInfo;
import com.chat.laty.greenDao.RyUserInfoDao;
import com.chat.laty.mvp.BasePresenterActivity;
import com.chat.laty.presenters.AccostLanguagePresenter;
import com.chat.laty.utils.XYSPUtils;
import com.chat.laty.utils.net.Common;
import com.chat.laty.view.CustomActionBar;

import java.util.HashMap;
import java.util.List;

import butterknife.BindView;

/**
 * <AUTHOR>
 * @date 2023/12/25 19:32
 * @description:搭讪语男
 */
public class AccostLanguageListUI extends BasePresenterActivity<AccostLanguagePresenter> implements AccostLanguageController.View {
    @BindView(R.id.custom_bar)
    CustomActionBar mActionBar;

    @BindView(R.id.base_rv)
    RecyclerView mRecyclerView;

    ManAccostLanguageAdapter mAdapter;
    RyUserInfo mUserInfo;

    private String mManAccostId;


    public static void start(Context context) {
        Intent intent = new Intent(context, AccostLanguageListUI.class);
        context.startActivity(intent);
    }

    @Override
    protected void initDatas() {

    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_base_rc_layout;
    }

    @Override
    protected void initViews() {
        mActionBar.setTitleText("搭讪设置");
        mActionBar.setTitleTextColor(getResources().getColor(R.color.color_333333));
        String userId = XYSPUtils.getString(Common.KEY_APP_USER_RY_ID);
        mUserInfo = XYApplication.getDaoInstant().getRyUserInfoDao().queryBuilder().where(RyUserInfoDao.Properties.Id.eq(userId)).unique();

        mAdapter = new ManAccostLanguageAdapter(mUserInfo.getManAccostId());
        mAdapter.setUserChoiceId(mUserInfo.getManAccostId());
        GridLayoutManager manager = new GridLayoutManager(AccostLanguageListUI.this, 2);
        mRecyclerView.setLayoutManager(manager);

        mRecyclerView.setAdapter(mAdapter);

        mAdapter.setStateViewEnable(true);
        mAdapter.setStateView(createEmptyListView("暂无数据", R.mipmap.icon_nodata));
        mAdapter.addOnItemChildClickListener(R.id.accost_riv, (baseQuickAdapter, view, position) -> {
            if (XYApplication.appLogin(true)) {
                AccostLanguageInfo info = (AccostLanguageInfo) baseQuickAdapter.getItem(position);
                mManAccostId = info.getId();
                info.setSelect(true);
                for (int i = 0; i < mAdapter.getItems().size(); i++) {
                    if (position != i) {
                        mAdapter.getItems().get(i).setSelect(false);
                    }
                }

                mAdapter.notifyDataSetChanged();
                HashMap<String, Object> params = new HashMap<>();
                params.put("manAccostId", info.getId());
                presenter.updateSettings(params);
//                mUserInfo.setId(info.getId());
//                mAdapter.setUserChoiceId(info.getId());

//                XYApplication.getDaoInstant().getRyUserInfoDao().insertOrReplace(mUserInfo);
            }
        });
    }

    @Override
    protected AccostLanguagePresenter setPresenter() {
        return new AccostLanguagePresenter(this);
    }

    @Override
    public FragmentActivity context() {
        return this;
    }

    @Override
    protected void onResume() {
        super.onResume();
        presenter.getAccostManList();
    }


    @Override
    public void showUploadImgResult(List<UploadImgInfo> data) {

    }

    @Override
    public void showAccostWomanList(List<AccostLanguageInfo> accostInfo) {
    }

    @Override
    public void showAccostManList(AccostManLanguageInfo accostInfo) {

        for (int i = 0; i < accostInfo.getAccostList().size(); i++) {
            if (TextUtils.isEmpty(mUserInfo.getManAccostId())) {
                accostInfo.getAccostList().get(0).setSelect(true);
            } else {
                if (TextUtils.equals(mUserInfo.getManAccostId(), accostInfo.getAccostList().get(i).getId())) {
                    accostInfo.getAccostList().get(i).setSelect(true);
                }
            }


        }
        mAdapter.setStateView(createEmptyListView("暂无数据", R.mipmap.icon_nodata));
        mAdapter.setItems(accostInfo.getAccostList());
    }

    @Override
    public void showupdateCallback(int code) {

    }

    @Override
    public void showDeleteCallback(Integer code) {
    }

    @Override
    public void showDefaultCallback(Integer code) {
    }

    @Override
    public void showCountDownFinish() {

    }

    @Override
    public void showUploadAudioResult(List<String> result) {

    }

    @Override
    public void showCountDownTravel(int l) {

    }

    @Override
    public void showUpdateCallback(BaseBean base) {
        if (200 == base.getCode())
        {
            mUserInfo.setManAccostId(mManAccostId);
            XYApplication.getDaoInstant().getRyUserInfoDao().insertOrReplace(mUserInfo);
        }
    }

}
