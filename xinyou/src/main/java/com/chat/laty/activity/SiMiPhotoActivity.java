package com.chat.laty.activity;

import android.content.Context;
import android.content.Intent;
import android.view.View;
import android.widget.Button;

import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.luck.picture.lib.basic.PictureSelector;
import com.luck.picture.lib.config.PictureConfig;
import com.luck.picture.lib.entity.LocalMedia;
import com.chat.laty.R;
import com.chat.laty.adapter.AddSiMiAdapter;
import com.chat.laty.controllers.SiMiPhotoController;
import com.chat.laty.dialog.BottomChoiceDialog;
import com.chat.laty.entity.SiMiUploadBean;
import com.chat.laty.entity.SiMiUploadBean1;
import com.chat.laty.mvp.BasePresenterActivity;
import com.chat.laty.presenters.SiMiPhotoPresenter;
import com.chat.laty.view.CustomActionBar;
import com.yalantis.ucrop.UCrop;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;

public class SiMiPhotoActivity extends BasePresenterActivity<SiMiPhotoPresenter> implements SiMiPhotoController.View, CustomActionBar.OnActionBarClickListerner {

    @BindView(R.id.custom_bar)
    CustomActionBar mActionBar;
    @BindView(R.id.recycler_view)
    RecyclerView rvImages;
    @BindView(R.id.submit_btn)
    Button submit_btn;

    List<SiMiUploadBean1> mSelectList = new ArrayList<>();
    BottomChoiceDialog mBottomChoiceDialog;
    AddSiMiAdapter mAdapter;

    public static void startAct(Context context) {
        Intent intent = new Intent(context, SiMiPhotoActivity.class);
        context.startActivity(intent);
    }


    @Override
    protected void initDatas() {
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_simi_photo;
    }


    @Override
    protected void initViews() {
        mActionBar.setTitleText("私密相册");
        mActionBar.setTitleTextColor(getColor(R.color.color_333333));

        mAdapter = new AddSiMiAdapter();

//        mSelectList.add(new SiMiUploadBean1());
        mAdapter.setItems(mSelectList);

        rvImages.setAdapter(mAdapter);
        rvImages.setLayoutManager(new GridLayoutManager(this, 2));

        mAdapter.addOnItemChildClickListener(R.id.add_layout, (baseQuickAdapter, view, position) -> {
//            choicePic(10 - mSelectList.size());
            choicePic();
        });
        mAdapter.addOnItemChildClickListener(R.id.iv_thum, (baseQuickAdapter, view, position) -> {
            setPicCoverDialog(position);
        });
        mAdapter.addOnItemChildClickListener(R.id.delete_layout, (baseQuickAdapter, view, position) -> {

            if (mSelectList.get(position).getId()==null||mSelectList.get(position).getId().isEmpty()) {
                for (SiMiUploadBean1 bean : mSelectList) {
                    if (bean.getImageUrl() == null) {
                        mSelectList.remove(bean);
                    }
                }

                mSelectList.remove(position);

                if (mSelectList.size() < 9) {
                    mSelectList.add(new SiMiUploadBean1());
                }
                mAdapter.notifyDataSetChanged();
                if (mSelectList.size() == 1) {
                    submit_btn.setVisibility(View.GONE);
                }
            } else {
                presenter.delSimiPhoto(mSelectList.get(position).getId(), position);
            }
        });

        presenter.getSimiPhoto();
    }


    @OnClick({R.id.submit_btn})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.submit_btn:

                List<SiMiUploadBean> uploadList = new ArrayList<>();
                for (int i = 0; i < mSelectList.size(); i++) {
                    if (mSelectList.get(i).getImageUrl()!=null) {
                        SiMiUploadBean bean = new SiMiUploadBean();
                        bean.setImageSort(String.valueOf(i));
                        bean.setImageUrl(mSelectList.get(i).getImageUrl());
                        uploadList.add(bean);
                    }
                }
                presenter.UploadInfo(uploadList);

                break;
        }
    }


    @Override
    public void loadSiMiData(List<SiMiUploadBean1> result) {
        mSelectList.addAll(result);

        if (mSelectList.size() < 9) {
            mSelectList.add(new SiMiUploadBean1());
        }
//        mAdapter.setItems(mSelectList);
        runOnUiThread(() -> mAdapter.notifyDataSetChanged());
    }

    @Override
    public void delSiMiResult(int position) {
        for (SiMiUploadBean1 bean : mSelectList) {
            if (bean.getImageUrl() == null) {
                mSelectList.remove(bean);
            }
        }

        mSelectList.remove(position);

        if (mSelectList.size() < 9) {
            mSelectList.add(new SiMiUploadBean1());
        }
        if (mSelectList.size() == 1) {
            submit_btn.setVisibility(View.GONE);
        }
        runOnUiThread(() -> mAdapter.notifyDataSetChanged());
    }

    @Override
    public void submitSiMiResult() {
        // 确保在 UI 线程中执行
        runOnUiThread(() -> submit_btn.setVisibility(View.GONE));
    }

    @Override
    public void showUploadResults(List<String> result) {
        dismissProgressDialog();

        for (SiMiUploadBean1 bean : mSelectList) {
            if (bean.getImageUrl() == null) {
                mSelectList.remove(bean);
            }
        }

        for (String info : result) {
            SiMiUploadBean1 bean = new SiMiUploadBean1();
            bean.setImageSort(String.valueOf(mSelectList.size()));
            bean.setImageUrl(info);
            mSelectList.add(bean);
        }

        if (mSelectList.size() < 9) {
            mSelectList.add(new SiMiUploadBean1());
        }
        mAdapter.setItems(mSelectList);
        runOnUiThread(() -> mAdapter.notifyDataSetChanged());

        submit_btn.setVisibility(View.VISIBLE);
    }

    @Override
    protected SiMiPhotoPresenter setPresenter() {
        return new SiMiPhotoPresenter(this);
    }

    @Override
    public FragmentActivity context() {
        return this;
    }


    private void setPicCoverDialog(int position) {
        if (null == mBottomChoiceDialog) {
            mBottomChoiceDialog = new BottomChoiceDialog(this);
        }

        mBottomChoiceDialog.setOnDialogCallbackListener(new BottomChoiceDialog.OnDialogCallbackListener() {
            @Override
            public void onDialogSelectItem(int type) {
                /*if (1 == type) {
                    coverUrl = mSelectList.get(position);
                } else if (2 == type) {
                    mSelectList.remove(position);

                    mSelectList = LibCollections.removeEmptyStrings(mSelectList);
                    if (mSelectList.size() < 9) {
                        mSelectList.add("");
                    }
                    mAdapter.setItems(mSelectList);
                    mAdapter.notifyDataSetChanged();
                } else if (3 == type) {
                    ArrayList<DiaplayPicInfo> mDiaplayPicInfos = new ArrayList<>();
                    List<String> temp = LibCollections.removeEmptyStrings(mSelectList);
                    for (String picurl : temp) {
                        mDiaplayPicInfos.add(new DiaplayPicInfo(picurl));
                    }

                    DischargedPicBrowserActivity.start(ImproveInformationUI.this, mDiaplayPicInfos, position);

                }*/
            }
        });
        mBottomChoiceDialog.show();
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, @Nullable @org.jetbrains.annotations.Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK) {
            switch (requestCode) {
                case UCrop.REQUEST_CROP:

//                    showProgressDialog(R.string.app_uploadding);
//                    final Uri resultUri = UCrop.getOutput(data);
//                    LogUtil.i("TAG", "裁剪回调的地址===》 " + resultUri);
//                    presenter.uploadHeadImgs(resultUri.getPath());
                    break;
                case PictureConfig.CHOOSE_REQUEST:
                    showProgressDialog(R.string.app_uploadding);
                    List<LocalMedia> selectList = PictureSelector.obtainSelectorList(data);
                    presenter.uploadFiles(selectList);
                    break;
            }
        }
    }

    @Override
    public boolean onActionBarClickListener(int function) {
        switch (function) {
            case CustomActionBar.FUNCTION_TEXT_RIGHT:

                break;
            case CustomActionBar.FUNCTION_BUTTON_LEFT:
                finish();
                break;
        }
        return false;
    }
}