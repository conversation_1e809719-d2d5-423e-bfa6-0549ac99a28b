package com.chat.laty.activity;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.view.View;

import androidx.appcompat.widget.AppCompatTextView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.RecyclerView;

import com.chat.laty.utils.ItemDecoration;
import com.hjq.toast.Toaster;
import com.chat.laty.R;
import com.chat.laty.adapter.MyRewardsAdapter;
import com.chat.laty.controllers.RewardsController;
import com.chat.laty.dialog.BottomSelectBankCardDialog;
import com.chat.laty.dialog.CenterEditDialog;
import com.chat.laty.entity.BankCardInfo;
import com.chat.laty.entity.BaseBean;
import com.chat.laty.entity.RewardsEntity;
import com.chat.laty.entity.WithdrawalInfo;
import com.chat.laty.mvp.BasePresenterActivity;
import com.chat.laty.presenters.RewardsPresenter;
import com.chat.laty.view.CustomActionBar;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;
import cn.xjc_soft.lib_utils.LibCollections;

public class MyRewardsActivity extends BasePresenterActivity<RewardsPresenter> implements RewardsController.View {

    @BindView(R.id.custom_bar)
    CustomActionBar customBar;

    @BindView(R.id.recycler_view)
    RecyclerView recyclerView;

    @BindView(R.id.balance_tv)
    AppCompatTextView balanceTv;
    @BindView(R.id.todayRewards_tv)
    AppCompatTextView todayRewardsTv;

    @BindView(R.id.ali_acount_tv)
    AppCompatTextView aliAcountTv;

    @BindView(R.id.weichat_acount_tv)
    AppCompatTextView weiChatAcountTv;

    @BindView(R.id.bankcard_acount_tv)
    AppCompatTextView bankCardAcountTv;

    @BindView(R.id.ali_layout)
    ConstraintLayout aliLayout;
    @BindView(R.id.weichat_layout)
    ConstraintLayout weichatLayout;
    @BindView(R.id.bankcard_layout)
    ConstraintLayout bankCardLayout;
    @BindView(R.id.rewards_desc_tv)
    AppCompatTextView rewardsDescTv;

    MyRewardsAdapter mAdapter;

    RewardsEntity mRewardsEntity;

    WithdrawalInfo mWithdrawalInfo;

    BankCardInfo mBankCardInfo;


    private int mWithdrawalType;

    @Override
    protected void initDatas() {

    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_my_rewards_layout;
    }

    List<WithdrawalInfo> list = new ArrayList<>();
    int index = -1;

    public static void start(Context context) {
        Intent intent = new Intent(context, MyRewardsActivity.class);
        context.startActivity(intent);
    }

    @Override
    protected void initViews() {
        customBar.setTitleText("我的奖励");
        customBar.setTitleTextColor(getResources().getColor(R.color.white));
        customBar.setRightText("钻石详情");
        customBar.addFunction(CustomActionBar.FUNCTION_TEXT_RIGHT);

        customBar.setOnActionBarClickListerner(function -> {
            if (function == CustomActionBar.FUNCTION_BUTTON_LEFT) {
                MyRewardsActivity.this.finish();
                return true;
            } else if (function == CustomActionBar.FUNCTION_TEXT_RIGHT) {
                MasonryDetailActivity.start(MyRewardsActivity.this);
                return true;
            }
            return false;
        });

        mAdapter = new MyRewardsAdapter();
        recyclerView.setAdapter(mAdapter);
        if (recyclerView.getItemDecorationCount() == 0) {
            recyclerView.addItemDecoration(new ItemDecoration(this,0,7f,7f));
        }
        mAdapter.setOnItemClickListener((baseQuickAdapter, view, i) -> {
            if (index == i) return;
            if (index != -1) {
                list.get(index).selected = false;
                baseQuickAdapter.notifyItemChanged(index);
            }
            index = i;
            list.get(index).selected = true;
            baseQuickAdapter.notifyItemChanged(index);
            mWithdrawalInfo = list.get(index);
        });
        presenter.getRewards();
    }

    @SuppressLint("NonConstantResourceId")
    @OnClick({R.id.btn_confirm, R.id.band_ali_tv, R.id.band_weichat, R.id.band_bank_card, R.id.ali_layout, R.id.weichat_layout, R.id.bankcard_layout})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.ali_layout:
                if (TextUtils.isEmpty(mRewardsEntity.getAliPayName())) {
                    Toaster.show("请先绑定支付宝账号");
                    return;
                }
                mWithdrawalType = 1;
                aliLayout.setSelected(true);
                weichatLayout.setSelected(false);
                bankCardLayout.setSelected(false);
                break;

            case R.id.weichat_layout:
                if (TextUtils.isEmpty(mRewardsEntity.getWxPayAccount())) {
                    Toaster.show("请先绑定微信账号");
                    return;
                }
                mWithdrawalType = 2;
                aliLayout.setSelected(false);
                weichatLayout.setSelected(true);
                bankCardLayout.setSelected(false);
                break;

            case R.id.bankcard_layout:
                if (LibCollections.isEmpty(mRewardsEntity.getBankCardList())) {
                    Toaster.show("请先绑定银行账号");
                    return;
                }
                BottomSelectBankCardDialog dialog = new BottomSelectBankCardDialog(MyRewardsActivity.this, mRewardsEntity.getBankCardList(), mBankCardInfo);
                dialog.setOnCallbackListener(this::getBankCardCallback);
                dialog.create();
                dialog.show();
                break;

            case R.id.band_ali_tv:
                showCenterEditDialog(1, TextUtils.isEmpty(mRewardsEntity.getAliPayName())?"":mRewardsEntity.getAliPayName(), TextUtils.isEmpty(mRewardsEntity.getAliPayAccount())?"":mRewardsEntity.getAliPayAccount());
                break;
            case R.id.band_weichat:
                showCenterEditDialog(2, TextUtils.isEmpty(mRewardsEntity.getWxPayName())?"":mRewardsEntity.getWxPayName(), TextUtils.isEmpty(mRewardsEntity.getWxPayAccount())?"":mRewardsEntity.getWxPayAccount());
                break;
            case R.id.band_bank_card:
                showCenterEditDialog(3, "", "");
                break;

            case R.id.btn_confirm:
                if (null == mWithdrawalInfo) {
                    Toaster.show("请选择提现套餐");
                    return;
                }
                if (0 == mWithdrawalType) {
                    Toaster.show("请选择提现类型");
                    return;
                }
                presenter.saveWithdrawal(mWithdrawalInfo.getId(), mWithdrawalType, mBankCardInfo);
                break;
        }
    }

    private void showCenterEditDialog(int type, String userName, String acount) {
        CenterEditDialog dialog = new CenterEditDialog(MyRewardsActivity.this, userName, acount);

        dialog.setOnDialogCallbackListener(new CenterEditDialog.OnDialogCallbackListener() {
            @Override
            public void onDialogCallBack(String userName, String num, String bankName) {
                HashMap<String, Object> params = new HashMap<>();
                if (1 == type) {
                    params.put("aliPayName", userName);
                    params.put("aliPayAccount", num);
                } else if (2 == type) {
                    params.put("wxPayName", userName);
                    params.put("wxPayAccount", num);
                } else if (3 == type) {
                    params.put("userName", userName);
                    params.put("bankNumber", num);
                    params.put("bankName", bankName);
                }
                presenter.updateAliWxPayAccount(type, params);
            }
        });
        dialog.show();
        dialog.setType(type);
    }

    private void getBankCardCallback(BankCardInfo bankCardInfo) {
        if (bankCardInfo == null) {
            this.mBankCardInfo = null;
            bankCardAcountTv.setText("");
            bankCardAcountTv.setSelected(false);
            bankCardLayout.setSelected(false);
        } else {
            mWithdrawalType = 3;
            this.mBankCardInfo = bankCardInfo;
            bankCardAcountTv.setText(bankCardInfo.getUserName() + "-" + bankCardInfo.getBankNumber());
            bankCardAcountTv.setSelected(true);
            bankCardLayout.setSelected(true);

            aliLayout.setSelected(false);
            weichatLayout.setSelected(false);
        }
    }

    @Override
    protected RewardsPresenter setPresenter() {
        return new RewardsPresenter(this);
    }


    @Override
    public FragmentActivity context() {
        return this;
    }

    @Override
    public void onPointerCaptureChanged(boolean hasCapture) {
        super.onPointerCaptureChanged(hasCapture);
    }

    @Override
    public void showRewards(RewardsEntity data) {
        mRewardsEntity = data;
        list = mRewardsEntity.getWithdrawalSettings();
        mAdapter.setItems(list);
        mAdapter.notifyDataSetChanged();

        balanceTv.setText(data.getDiamondBalance() + "");
        todayRewardsTv.setText(data.getTodayRewards() + "");

        if (!TextUtils.isEmpty(data.getAliPayName()))
            aliAcountTv.setText(data.getAliPayName() + "-" + data.getAliPayAccount());

        if (!TextUtils.isEmpty(data.getWxPayAccount()))
            weiChatAcountTv.setText(data.getWxPayName() + "-" + data.getWxPayAccount());

        if (!LibCollections.isEmpty(data.getBankCardList())) {
            mBankCardInfo = data.getBankCardList().get(0);
            bankCardAcountTv.setText(mBankCardInfo.getUserName() + "-" + mBankCardInfo.getBankNumber());
        }

        if (!TextUtils.isEmpty(data.getWithdrawalPrompt())) {
            rewardsDescTv.setText(data.getWithdrawalPrompt());
        }
    }

    @Override
    public void showUpdateCallback(BaseBean baseBean) {
        if (200 == baseBean.getCode()) {
            presenter.getRewards();
        }
    }

    @Override
    public void showWithdrawalResult(BaseBean baseBean) {
        if (null != baseBean) {
            Toaster.show(baseBean.getMessage());
        }
    }
}