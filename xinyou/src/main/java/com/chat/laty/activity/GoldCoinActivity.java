package com.chat.laty.activity;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Handler;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextPaint;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.view.View;
import android.widget.CheckBox;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.RecyclerView;

import com.chat.laty.utils.ItemDecoration;
import com.hjq.toast.Toaster;
import com.chat.laty.R;
import com.chat.laty.adapter.GoldCoinAdapter;
import com.chat.laty.adapter.PayTypeAdapter;
import com.chat.laty.base.AppHelper;
import com.chat.laty.controllers.GoldCoinController;
import com.chat.laty.entity.GoldCoinInfoModel;
import com.chat.laty.entity.PayResultEntity;
import com.chat.laty.entity.PayTypeModel;
import com.chat.laty.entity.WeiChatPayInfo;
import com.chat.laty.mvp.BasePresenterActivity;
import com.chat.laty.presenters.GoldCoinPresenter;
import com.chat.laty.presenters.PayManager;
import com.chat.laty.utils.AgreementController;
import com.chat.laty.view.CustomActionBar;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import butterknife.BindView;
import butterknife.OnClick;

public class GoldCoinActivity extends BasePresenterActivity<GoldCoinPresenter> implements GoldCoinController.IInfoView {


    public static void startAct(Context context) {
        Intent intent = new Intent(context, GoldCoinActivity.class);
        context.startActivity(intent);
    }

    @BindView(R.id.custom_bar)
    CustomActionBar customBar;

    @BindView(R.id.tv_gold_coin)
    TextView tvGoldCoin;
    @BindView(R.id.tv_recharge_coin)
    TextView tvRechargeCoin;

    @BindView(R.id.recycler_view)
    RecyclerView recyclerView;

    @BindView(R.id.rv_pay_grid)
    RecyclerView rvPayGrid;

    @BindView(R.id.checkbox)
    CheckBox cbAgree;

    @BindView(R.id.tv_agree_extra)
    TextView tvAgree;


    @Override
    protected void initDatas() {

    }

    GoldCoinAdapter adapter;
    PayTypeAdapter payTypeAdapter;


    @Override
    protected int getContentViewId() {
        return R.layout.activity_gold_coin_layout;
    }

    GoldCoinInfoModel model;

    List<GoldCoinInfoModel.GoldCoinTypeModel> list = new ArrayList<>();
    int index = -1;

    int payTypeIndex = -1;

    GoldCoinInfoModel.GoldCoinTypeModel typeModel;

    private AgreementController controller = new AgreementController();

    @Override
    protected void initViews() {

        customBar.setTitleText("金币充值");
        customBar.setRightText("金币明细");
//        customBar.setAllColor(ContextCompat.getColor(this, R.color.white));

        customBar.addFunction(CustomActionBar.FUNCTION_TEXT_RIGHT);
        customBar.setOnActionBarClickListerner(function -> {
            if (function == CustomActionBar.FUNCTION_BUTTON_LEFT) {
                GoldCoinActivity.this.finish();
                return true;
            } else if (function == CustomActionBar.FUNCTION_TEXT_RIGHT) {
                GoldCoinDetailActivity.startAct(GoldCoinActivity.this);
                return true;
            }
            return false;
        });

        SpannableString spannableString = new SpannableString("请勿提示相信非官方渠道的充值信息，禁止未成年人充值！请理性消费谨防诈骗行为，参考《平台公约》及《防诈骗指南》");
        spannableString.setSpan(new ClickableSpan() {

            @Override
            public void updateDrawState(TextPaint ds) {
                super.updateDrawState(ds);
                ds.setUnderlineText(false);
                ds.setColor(ContextCompat.getColor(GoldCoinActivity.this, R.color.colorPrimary));
            }

            @Override
            public void onClick(@NonNull View view) {
                controller.getAccordByNum(GoldCoinActivity.this, 6);
            }


        }, 40, 46, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new ClickableSpan() {

            @Override
            public void updateDrawState(TextPaint ds) {
                super.updateDrawState(ds);
                ds.setUnderlineText(false);
                ds.setColor(ContextCompat.getColor(GoldCoinActivity.this, R.color.colorPrimary));
            }

            @Override
            public void onClick(@NonNull View view) {
                controller.getAccordByNum(GoldCoinActivity.this, 5);
            }


        }, 47, 54, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);

        tvAgree.setText(spannableString);
        tvAgree.setMovementMethod(LinkMovementMethod.getInstance());

        adapter = new GoldCoinAdapter();
        recyclerView.setAdapter(adapter);
        if (recyclerView.getItemDecorationCount() == 0) {
            recyclerView.addItemDecoration(new ItemDecoration(this,0,7f,7f));
        }
        adapter.setOnItemClickListener((baseQuickAdapter, view, i) -> {
            if (index == i) return;
            if (index != -1) {
                list.get(index).selected = false;
                baseQuickAdapter.notifyItemChanged(index);
            }
            index = i;
            list.get(index).selected = true;
            baseQuickAdapter.notifyItemChanged(index);
            typeModel = model.getGoldList().get(index);
        });
        adapter.setItems(list);

        payTypeAdapter = new PayTypeAdapter();
        rvPayGrid.setAdapter(payTypeAdapter);

//        showProgressDialog(R.string.app_loadding);
        presenter.getGoldCoinInfo();
    }


    @OnClick({R.id.tv_agree, R.id.btn_recharge})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.tv_agree:
                controller.getAccordByNum(this, 7);
                break;
            case R.id.btn_recharge:
                if (getStatusFromCache()){
                    Toaster.show("您处于青少年模式无法使用此功能");
                    return;
                }

                if (index == -1) {
                    Toaster.show("请选择充值套餐");
                    return;
                }
                if (payTypeIndex == -1) {
                    Toaster.show("请选择支付方式");
                    return;
                }
                if (!cbAgree.isChecked()) {
                    Toaster.show("请先阅读并同意《充值协议》");
                    return;
                }

                showProgressDialog(R.string.app_loadding);
                PayTypeModel data = model.getPayTypeList().get(payTypeIndex);
                presenter.buyGoldCoin(data.getCode(), typeModel.getId());
                break;
        }
    }

    @Override
    protected GoldCoinPresenter setPresenter() {
        return new GoldCoinPresenter(this, null);
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (payed) {
            presenter.getGoldCoinInfo();
            payed = false;
        }
    }

    boolean payed = false;

    @Override
    protected boolean isUseEventBus() {
        return true;
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void payResult(PayResultEntity event) {
        if (event.code == 0) {
            payed = true;
            Toaster.show("充值成功");
            new Handler().postDelayed(() -> presenter.getGoldCoinInfo(), 3000);
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    @Override
    public void getInfoSucceed(GoldCoinInfoModel data) {

        this.model = data;
        tvGoldCoin.setText(data.getGoldBalance());
        tvRechargeCoin.setText(data.getTodayTotalGold());

        list = data.getGoldList();
        adapter.setItems(list);
        adapter.notifyDataSetChanged();

        for (PayTypeModel m : model.getPayTypeList()) {
            if (m.getCode().contains("wechat") || m.getCode().contains("wxPayAll")) {
                m.setIcon(R.mipmap.pay_weixin);
            } else if (m.getCode().contains("ali") || m.getCode().contains("aliPayAll")) {
                m.setIcon(R.mipmap.pay_zhifubao);
            } else {
                m.setIcon(R.mipmap.pay_yinlian);
            }
        }
        payTypeAdapter.setItems(model.getPayTypeList());
        payTypeAdapter.setOnItemClickListener((baseQuickAdapter, view, i) -> {

            if (payTypeIndex == i) return;

            if (payTypeIndex != -1) {
                Objects.requireNonNull(baseQuickAdapter.getItem(payTypeIndex)).selected = false;
                baseQuickAdapter.notifyItemChanged(payTypeIndex);
            }
            payTypeIndex = i;
            Objects.requireNonNull(baseQuickAdapter.getItem(payTypeIndex)).selected = true;
            baseQuickAdapter.notifyItemChanged(payTypeIndex);

        });
        payTypeAdapter.notifyDataSetChanged();
    }

    @Override
    public void getInfoFailed() {
    }

    @Override
    public void buySucceed(WeiChatPayInfo info) {
        dismissProgressDialog();
        PayTypeModel data = model.getPayTypeList().get(payTypeIndex);
        if (data.getCode().contains("wxPayAll") || data.getCode().contains("aliPayAll")) {
            AppHelper.openUrl(info.getPayUrl());
        } else if (data.getCode().contains("wechat")) {
            PayManager.weiChatPay(this, info);
        } else if (data.getCode().contains("ali")) {
            PayManager.alipayPay(this, info.getPayUrl());
        } else {
            Toaster.show("银联支付");
        }
    }

    @Override
    public void buyFailed() {
        dismissProgressDialog();
    }


    @Override
    public FragmentActivity context() {
        return this;
    }

    /**
     * 从缓存中获取状态
     *
     * @return
     */
    private boolean getStatusFromCache() {
        SharedPreferences sharedPreferences = getSharedPreferences("app_prefs", Context.MODE_PRIVATE);
        return sharedPreferences.getBoolean("qsnms_status", false); // 默认值为 false
    }

}