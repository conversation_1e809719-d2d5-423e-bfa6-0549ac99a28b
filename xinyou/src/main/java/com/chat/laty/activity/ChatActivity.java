package com.chat.laty.activity;

import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.net.Uri;
import android.text.TextUtils;
import android.view.View;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.appcompat.widget.AppCompatTextView;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import com.hjq.permissions.OnPermissionCallback;
import com.hjq.permissions.Permission;
import com.hjq.permissions.XXPermissions;
import com.hjq.toast.Toaster;
import com.luck.picture.lib.basic.PictureSelector;
import com.luck.picture.lib.config.SelectMimeType;
import com.luck.picture.lib.entity.LocalMedia;
import com.luck.picture.lib.interfaces.OnResultCallbackListener;
import com.lxj.xpopup.XPopup;
import com.lxj.xpopup.interfaces.OnConfirmListener;
import com.makeramen.roundedimageview.RoundedImageView;
import com.opensource.svgaplayer.SVGACallback;
import com.opensource.svgaplayer.SVGAImageView;
import com.opensource.svgaplayer.SVGAParser;
import com.opensource.svgaplayer.SVGAVideoEntity;
import com.chat.laty.R;
import com.chat.laty.base.XYApplication;
import com.chat.laty.controllers.ChatController;
import com.chat.laty.dialog.BottomCallPhoneDialog;
import com.chat.laty.dialog.BottomCommonPhrasesDialog;
import com.chat.laty.dialog.BottomGiftDialog;
import com.chat.laty.dialog.BottomUserOptDialog;
import com.chat.laty.dialog.UpdateUserNicknameDialog;
import com.chat.laty.entity.BalanceInfo;
import com.chat.laty.entity.BaseBean;
import com.chat.laty.entity.CommonPhrasesInfo;
import com.chat.laty.entity.GiftInfo;
import com.chat.laty.entity.RyUserInfo;
import com.chat.laty.entity.UserFreeInfo;
import com.chat.laty.entity.XYUserInfo;
import com.chat.laty.fragment.ChatConversationFragment;
import com.chat.laty.im.message.XYCommentContent;
import com.chat.laty.im.message.XYGiftContent;
import com.chat.laty.im.message.XYGoldCoinsTextContent;
import com.chat.laty.mvp.BasePresenterActivity;
import com.chat.laty.presenters.ChatPresenter;
import com.chat.laty.utils.GlideEngine;
import com.chat.laty.utils.ImageFileCompressEngine;
import com.chat.laty.utils.LogUtil;
import com.chat.laty.utils.PermissionInterceptor;
import com.chat.laty.utils.PicassoUtils;
import com.chat.laty.utils.XYSPUtils;
import com.chat.laty.utils.net.Common;
import com.chat.laty.view.CustomActionBar;
import com.chat.laty.view.RotateAnimator;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.jetbrains.annotations.NotNull;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;
import cn.rongcloud.callplus.api.RCCallPlusClient;
import cn.rongcloud.callplus.api.RCCallPlusSession;
import cn.xjc_soft.lib_utils.LibCollections;
import io.rong.imkit.IMCenter;
import io.rong.imkit.MessageInterceptor;
import io.rong.imkit.RongIM;
import io.rong.imkit.config.ConversationClickListener;
import io.rong.imkit.config.RongConfigCenter;
import io.rong.imkit.userinfo.RongUserInfoManager;
import io.rong.imkit.utils.PermissionCheckUtil;
import io.rong.imkit.utils.RouteUtils;
import io.rong.imlib.IRongCallback;
import io.rong.imlib.IRongCoreCallback;
import io.rong.imlib.IRongCoreEnum;
import io.rong.imlib.RongCoreClient;
import io.rong.imlib.RongIMClient;
import io.rong.imlib.listener.OnReceiveMessageWrapperListener;
import io.rong.imlib.model.Conversation;
import io.rong.imlib.model.ConversationIdentifier;
import io.rong.imlib.model.Message;
import io.rong.imlib.model.MessageContent;
import io.rong.imlib.model.ReceivedProfile;
import io.rong.imlib.model.UserInfo;
import io.rong.message.ImageMessage;
import io.rong.message.SightMessage;
import io.rong.message.TextMessage;

/**
 * <AUTHOR>
 * @date 2023/12/1 13:48
 * @description:聊天界面
 */
public class ChatActivity extends BasePresenterActivity<ChatPresenter> implements ChatController.View, CustomActionBar.OnActionBarClickListerner, ChatConversationFragment.OnExtensionBoardListener {

    @BindView(R.id.custom_bar)
    CustomActionBar mActionBar;
    @BindView(R.id.left_user_riv)
    RoundedImageView mLeftRiv;
    @BindView(R.id.xindong_tv)
    AppCompatTextView mXindongTv;

    @BindView(R.id.user_name_tv)
    TextView userNameTv;
    @BindView(R.id.tv_online)
    TextView tvOnline;
    @BindView(R.id.tv_city)
    TextView tvCity;
    @BindView(R.id.tv_job)
    TextView tvJob;

    @BindView(R.id.right_user_riv)
    RoundedImageView mRightRiv;

    @BindView(R.id.chat_bg_image)
    RoundedImageView mChatBgRiv;

    @BindView(R.id.svgaImage)
    SVGAImageView mGiftIV;

    @BindView(R.id.next_chat_tv)
    AppCompatTextView mNextMsgTv;
    @BindView(R.id.ll_bottom)
    LinearLayout mBottomLayout;

    @BindView(R.id.v_line_1)
    View line1;

    @BindView(R.id.v_line_2)
    View line2;

    RyUserInfo mUserInfo;
    XYUserInfo mXyUserInfo;

    ChatConversationFragment conversationFragment;
    BottomGiftDialog mBottomRvDialog;
    BottomCallPhoneDialog mBottomChoiceDialog;

    BottomCommonPhrasesDialog mCommonPhrasesDialog;
    BottomUserOptDialog mChoiceDialog;

    List<CommonPhrasesInfo> mCommonPhrasesInfos;

    String mUserId, mNextMsgId;

    private List<String> mUnMsgUserIds;
    private List<String> mUnGifts = new ArrayList<>();

    private int mUserBlackStatus;

    UserFreeInfo mUserFreeInfo;

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void getGiftInfo(GiftInfo giftInfo) {
        mUnGifts.add(giftInfo.getTrendsImg());
        if (mGiftIV.getVisibility() != View.VISIBLE) {
            playGifts();
        }
    }

    private long currentTime;

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void getGiftInfo(XYGiftContent giftInfo) {
        LogUtil.i("礼物监听", "getGiftInfo");
        mUnGifts.add(giftInfo.getGiftTrendsImg());
        if (mGiftIV.getVisibility() != View.VISIBLE) {
            playGifts();
        }
    }

    public static void start(Context context, String targetId) {
        Intent intent = new Intent(context, ChatActivity.class);
        intent.putExtra("targetId", targetId);
        context.startActivity(intent);
    }

    @OnClick({R.id.shequ, R.id.gift_tv, R.id.call_phone, R.id.pic_tv, R.id.next_chat_tv, R.id.svgaImage, R.id.right_user_riv})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.shequ:
                mCommonPhrasesDialog = new BottomCommonPhrasesDialog(ChatActivity.this);
                mCommonPhrasesDialog.create();
                mCommonPhrasesDialog.show();

                mCommonPhrasesDialog.setOnDialogCallbackListener(new BottomCommonPhrasesDialog.OnDialogCallbackListener() {
                    @Override
                    public void onDialogDeleteItem(String id) {
                        presenter.deleteCommonPhrasesList(id);
                    }

                    @Override
                    public void onDialogAddCommonPhrases(String content) {
                        presenter.addCommonPhrases(content);
                    }

                    @Override
                    public void onDialogSendCommonPhrases(String content) {
                        sendMsg(mUserId, content);
                    }
                });
                mCommonPhrasesDialog.setNewDatas(mCommonPhrasesInfos);
                break;

            case R.id.gift_tv:
                showTechnologicalDialog();
                break;

            case R.id.call_phone:
                presenter.getUserBalance();
                showCallPhoneDialog(mUserId);
                break;

            case R.id.pic_tv:
                XXPermissions.with(ChatActivity.this).permission(Permission.READ_MEDIA_IMAGES).interceptor(new PermissionInterceptor()).request(new OnPermissionCallback() {
                    @Override
                    public void onGranted(List<String> permissions, boolean all) {
                        if (!all) {
                            return;
                        }
                        choicePic(mUserId);
                    }
                });
                break;

            case R.id.svgaImage:
                mUnGifts.clear();
                mGiftIV.stopAnimation();
                mGiftIV.setVisibility(View.GONE);
                break;

            case R.id.next_chat_tv:
                if (!TextUtils.isEmpty(mNextMsgId)) {
//                    ChatActivity.start(ChatActivity.this, mNextMsgId);
                    ConversationIdentifier conversationIdentifier = new ConversationIdentifier(Conversation.ConversationType.PRIVATE, mNextMsgId);
                    RouteUtils.routeToConversationActivity(ChatActivity.this, conversationIdentifier, false, null);
                    mNextMsgTv.setVisibility(View.GONE);
                }
                break;

            case R.id.right_user_riv:
                UserCenterUI.start(ChatActivity.this, XYSPUtils.getString(Common.KEY_APP_USER_RY_ID));
                break;
        }
    }

    private void showTechnologicalDialog() {
        mBottomRvDialog = new BottomGiftDialog(ChatActivity.this);

        mBottomRvDialog.setTitleTv("礼物选择");
        mBottomRvDialog.setOnDialogCallbackListener(new BottomGiftDialog.OnDialogCallbackListener() {
            @Override
            public void onDialogRefresh() {
                presenter.getGift();
                presenter.getUserBalance();
            }

            @Override
            public void onChoiceGift(GiftInfo gift, int type, int num) {
                sendGiftMsg(mUserId, gift, type, num);
                mBottomRvDialog.dismiss();
            }
        });
        mBottomRvDialog.show();
    }

    private void showCallPhoneDialog(String userId) {

        if (null == mBottomChoiceDialog) {
            mBottomChoiceDialog = new BottomCallPhoneDialog(ChatActivity.this, userId);
        }

        mBottomChoiceDialog.setOnDialogCallbackListener(new BottomCallPhoneDialog.OnDialogCallbackListener() {
            @Override
            public void onDialogSelectItem(int type) {
                if (XYApplication.getCurrentUserStatus()) {
                    if (!TextUtils.isEmpty(mXyUserInfo.getSysMindNum()) && Integer.valueOf(mXyUserInfo.getSysMindNum()) <= Integer.valueOf(mXyUserInfo.getMindNum())) {
                        if (mXyUserInfo.getSex().equals(mUserInfo.getSex())) {
                            Toaster.show("同性别之间无法进行通讯！");
                            return;
                        }

                        if (XYApplication.getCurrentUserStatus()) {

                            if (type == 0 && TextUtils.equals("1", mXyUserInfo.getVoiceDisturb())) {
                                Toaster.show("该用户已打开语音免勿扰模式");
                                return;
                            }
                            if (type == 1 && TextUtils.equals("1", mXyUserInfo.getVideoDisturb())) {
                                Toaster.show("该用户已打开视频免勿扰模式");
                                return;
                            }

                            if (XYApplication.getCurrentIsMan()) { //如果我是男用户,判断我的金币/免费时长够不够

                                if (Common.VIP_CALL_PERMISSION) {
                                    if (mUserInfo.getVipLevel() == 0 || XYApplication.vipTypeModelList.get(mUserInfo.getVipLevel() - 1).getIsCallPermission() == 0) {
                                        Toaster.show("未开通VIP通话权限");
                                        return;
                                    }
                                }

                                BalanceInfo balanceInfo = XYApplication.getCurrentUserBalance();
                                if (type == 0 && (balanceInfo.getFreeVoiceNum() < 1 && balanceInfo.getGoldNum() < Double.valueOf(mUserFreeInfo.getVoiceFee()))) {
                                    Toaster.show("金币不足,无法语音");
                                    return;
                                }

                                if (type == 1 && (balanceInfo.getFreeVideoNum() < 1 && balanceInfo.getGoldNum() < Double.valueOf(mUserFreeInfo.getVideoFee()))) {
                                    Toaster.show("金币不足,无法视频");
                                    return;
                                }
                            }


                            RCCallPlusSession currentCallSession = RCCallPlusClient.getInstance().getCurrentCallSession();
                            if (currentCallSession != null) {
                                Toaster.show("当前正在通话，不允许拨打");
                                return;
                            }

                            presenter.getUserVideoStatus(mXyUserInfo.getUserId(), mUserId, type, ChatActivity.this);
                        }
                    } else {
                        Toaster.show("亲密度不足，未解锁该功能");
                    }
                }
            }
        });

        mBottomChoiceDialog.show();
        if (mUserFreeInfo != null)
            mBottomChoiceDialog.setUserFreeInfo(mUserFreeInfo);
    }

    @Override
    protected void initDatas() {

    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_chat_activity;
    }

    @Override
    protected void initViews() {
        getWindow().setFlags(WindowManager.LayoutParams.FLAG_SECURE, WindowManager.LayoutParams.FLAG_SECURE);
        conversationFragment = new ChatConversationFragment();
        FragmentManager manager = getSupportFragmentManager();
        FragmentTransaction transaction = manager.beginTransaction();
        transaction.replace(R.id.container, conversationFragment);
        transaction.commit();
        mUserId = getIntent().getStringExtra("targetId");
        getUnReadMsgByUserId();
        mUserInfo = XYApplication.getCurrentUserInfo();

        mActionBar.setOnActionBarClickListerner(this);

        if (null != mUserInfo) {
            PicassoUtils.showImage(mRightRiv, mUserInfo.getAvatar());
//            line2.setVisibility(View.VISIBLE);
        }
//        PicassoUtils.showTransImage(this, 8, "https://photo.tuchong.com/9402022/f/369999484.jpg", mChatBgRiv);
        RongIM.getInstance().setMessageInterceptor(messageInterceptor);

//        presenter.getUserInfo(mUserId);
        presenter.getUserBalance();


        RongCoreClient.addOnReceiveMessageListener(receiveMsg);
        RongIMClient.getInstance().setMessageExpansionListener(msgExpansionChage);

        RongConfigCenter.conversationConfig().setConversationClickListener(new ConversationClickListener() {
            @Override
            public boolean onUserPortraitClick(Context context, Conversation.ConversationType conversationType, UserInfo user, String targetId) {
                UserCenterUI.start(ChatActivity.this, user.getUserId());
                return false;
            }

            @Override
            public boolean onUserPortraitLongClick(Context context, Conversation.ConversationType conversationType, UserInfo user, String targetId) {
                return false;
            }

            @Override
            public boolean onMessageClick(Context context, View view, Message message) {
                LogUtil.e("message", "message:" + message.toString());
                if ("app:giftcontent".equals(message.getObjectName())) {
                    XYGiftContent giftInfo = (XYGiftContent) (message.getContent());
                    loadAnimation(giftInfo.getGiftTrendsImg());
                }
                if ("app:commentmsg".equals(message.getObjectName())) {
                    XYCommentContent commentInfo = (XYCommentContent) (message.getContent());
                    MineDynamicDetailUI.startActById(ChatActivity.this, commentInfo.getCommentId());
//                    loadAnimation(giftInfo.getGiftTrendsImg());
                }
                return false;
            }

            @Override
            public boolean onMessageLongClick(Context context, View view, Message message) {
                if (TextUtils.equals("RC:TxtMsg", message.getObjectName()) || TextUtils.equals("RC:SightMsg", message.getObjectName()) || TextUtils.equals("RC:FileMsg", message.getObjectName()) || TextUtils.equals("RC:ImgMsg", message.getObjectName())) {
                    if (TextUtils.equals(message.getSenderUserId(), XYSPUtils.getString(Common.KEY_APP_USER_RY_ID))) {
                        {
                            if ((System.currentTimeMillis() - message.getSentTime()) > 2 * 60 * 1000) {
                                Toaster.show("2分钟之前发送的消息不可撤回!");
                            } else {
                                new XPopup.Builder(ChatActivity.this)
                                        .isDestroyOnDismiss(true) //对于只使用一次的弹窗，推荐设置这个
                                        .customAnimator(new RotateAnimator())
                                        .asConfirm("撤回提示", "是否撤回该信息?", new OnConfirmListener() {
                                            @Override
                                            public void onConfirm() {
                                                IMCenter.getInstance().recallMessage(message, "", new RongIMClient.ResultCallback() {
                                                    @Override
                                                    public void onSuccess(Object o) {
                                                        Toaster.show("撤回成功");
                                                    }

                                                    @Override
                                                    public void onError(RongIMClient.ErrorCode e) {
                                                        Toaster.show("撤回失败:" + e.getMessage());
                                                    }
                                                });
                                            }
                                        })
                                        .show();
                            }
                        }

                    }
                }
                return true;
            }

            @Override
            public boolean onMessageLinkClick(Context context, String link, Message message) {
                return false;
            }

            @Override
            public boolean onReadReceiptStateClick(Context context, Message message) {
                return false;
            }
        });

        presenter.getCommonPhrasesList();

        RongIMClient.getInstance().getBlacklistStatus(mUserId, new RongIMClient.ResultCallback<RongIMClient.BlacklistStatus>() {
            @Override
            public void onSuccess(RongIMClient.BlacklistStatus blacklistStatus) {
                mUserBlackStatus = blacklistStatus.getValue();
            }

            @Override
            public void onError(RongIMClient.ErrorCode e) {

            }
        });

        mLeftRiv.setOnClickListener(v -> UserCenterUI.start(ChatActivity.this, mUserId));
//        mRightRiv.setOnClickListener(v -> UserCenterUI.start(ChatActivity.this, mUserInfo.getUserId()));

        PermissionCheckUtil.setRequestPermissionListListener(
                new PermissionCheckUtil.IRequestPermissionListListener() {
                    @Override
                    public void onRequestPermissionList(
                            Context activity,
                            List<String> permissionsNotGranted,
                            PermissionCheckUtil.IPermissionEventCallback callback) {
                        LogUtil.i("onRequestPermissionList", "permissionsNotGranted:" + permissionsNotGranted);

                        XXPermissions.with(activity).permission(permissionsNotGranted).interceptor(new PermissionInterceptor()).interceptor(new PermissionInterceptor()).request(new OnPermissionCallback() {
                            @Override
                            public void onGranted(List<String> permissions, boolean all) {
                                if (!all) {
                                    callback.cancelled();
                                    return;
                                }
                                callback.confirmed();
                            }
                        });
                    }
                });
    }

    private void getUnReadMsgByUserId() {
        Conversation.ConversationType conversationType = Conversation.ConversationType.PRIVATE;
        int count = 10;
        boolean desc = true;

        RongCoreClient.getInstance().getUnreadMentionedMessages(conversationType, mUserId, count, desc, new IRongCoreCallback.ResultCallback<List<Message>>() {
            @Override
            public void onSuccess(List<Message> messages) {
            }

            @Override
            public void onCallback(List<Message> messages) {
                for (Message msg : messages) {
                    LogUtil.e("未读消息", "onCallback-->已读状态--->" + msg.getSentStatus());
                }
            }

            @Override
            public void onError(IRongCoreEnum.CoreErrorCode e) {
//                LogUtil.e("未读消息", "CoreErrorCode状态--->" + e.message);
            }
        });

//        HistoryMessageOption historyMessageOption = new HistoryMessageOption();
//        historyMessageOption.setDataTime(0);//2022-09-07 17:25:12:112
//        historyMessageOption.setOrder(HistoryMessageOption.PullOrder.DESCEND);
//        historyMessageOption.setCount(8);
//        RongCoreClient.getInstance().getMessages(conversationType, mUserId, historyMessageOption, new IRongCoreCallback.IGetMessageCallback() {
//            @Override
//            public void onComplete(List<Message> list, IRongCoreEnum.CoreErrorCode coreErrorCode) {
//                mUnGifts.clear();
//                for (Message msg : list) {
////                    LogUtil.e("未读消息", "已读状态--->" + msg.getReadReceiptInfo().isReadReceiptMessage()+" 消息内容："+msg.getContent());
//                    LogUtil.e("未读消息", "发送人id："+msg.getTargetId()+"  已读状态--->" + msg.getReceivedStatus().isRead());
//                    if (TextUtils.equals(msg.getTargetId(), mUserId) && !msg.getReceivedStatus().isRead() && TextUtils.equals(msg.getObjectName(), "app:giftcontent")) {
//                        XYGiftContent gift = (XYGiftContent) msg.getContent();
//                        mUnGifts.add(gift);
//
//                    }
//                }
//                if (!LibCollections.isEmpty(mUnGifts))
//                    EventBus.getDefault().post(mUnGifts);
//            }
//        });
        IMCenter.getInstance().clearMessagesUnreadStatus(conversationType, mUserId, new RongIMClient.ResultCallback<Boolean>() {
            @Override
            public void onSuccess(Boolean aBoolean) {
            }

            @Override
            public void onError(RongIMClient.ErrorCode e) {

            }
        });
        RongIMClient.getInstance().syncConversationReadStatus(conversationType, mUserId, 0, new RongIMClient.OperationCallback() {
            @Override
            public void onSuccess() {

            }

            @Override
            public void onError(RongIMClient.ErrorCode errorCode) {

            }
        });
    }

    OnReceiveMessageWrapperListener receiveMsg = new OnReceiveMessageWrapperListener() {
        @Override
        public void onReceivedMessage(Message message, ReceivedProfile profile) {
            LogUtil.e("Message", "message-->" + message.toString());
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    presenter.getUserInfo(mUserId);
                    if (TextUtils.equals(mUserId, message.getSenderUserId()) && TextUtils.equals(message.getObjectName(), "app:giftcontent")) {
                        XYGiftContent gift = (XYGiftContent) message.getContent();
                        EventBus.getDefault().post(gift);
                    }

                    if (!mUserId.equals(message.getTargetId())) {
                        mNextMsgTv.setVisibility(View.VISIBLE);
                        mNextMsgId = message.getTargetId();
                        return;
                    }
                    new Thread(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                Thread.sleep(3000);
                                presenter.getUserInfo(mUserId);
                            } catch (InterruptedException e) {
                                e.printStackTrace();
                            }
                        }
                    }).start();

                }
            });

            //  针对接收离线消息时，服务端会将 200 条消息打成一个包发到客户端，客户端对这包数据进行解析。该参数表示每个数据包数据逐条上抛后，还剩余的条数
            int left = profile.getLeft();
            // 消息是否离线消息
            boolean isOffline = profile.isOffline();
            // 是否在服务端还存在未下发的消息包
            boolean hasPackage = profile.hasPackage();
        }
    };

    RongIMClient.MessageExpansionListener msgExpansionChage = new RongIMClient.MessageExpansionListener() {
        @Override
        public void onMessageExpansionUpdate(Map<String, String> expansion, Message message) {
            LogUtil.e("ChatActivity", "onMessageExpansionUpdate-->expansion-->" + expansion.toString());
            LogUtil.e("ChatActivity", "onMessageExpansionUpdate-->message-->" + message.toString());
        }

        @Override
        public void onMessageExpansionRemove(List<String> keyArray, Message message) {
            LogUtil.e("ChatActivity", "onMessageExpansionRemove-->expansion-->" + keyArray.toString());
            LogUtil.e("ChatActivity", "onMessageExpansionRemove-->message-->" + message.toString());
        }
    };

    MessageInterceptor messageInterceptor = new MessageInterceptor() {
        @Override
        public boolean interceptReceivedMessage(Message message, int left, boolean hasPackage, boolean offline) {
            LogUtil.e("messageInterceptor", "ChatActivity-->interceptReceivedMessage-->ChatActivity-->" + message.toString());
            return false;
        }

        /**
         * 从缓存中获取状态
         *
         * @return
         */
        private boolean getStatusFromCache() {
            SharedPreferences sharedPreferences = getSharedPreferences("app_prefs", Context.MODE_PRIVATE);
            return sharedPreferences.getBoolean("qsnms_status", false); // 默认值为 false
        }

        @Override
        public boolean interceptOnSendMessage(Message message) {
            if (getStatusFromCache()) {
                Toaster.show("您处于青少年模式无法使用私信");
                return true;
            }

            message.setCanIncludeExpansion(true);
            presenter.getUserBalance();
            String userSex = XYSPUtils.getString(Common.KEY_APP_USER_GENDER);
            if (TextUtils.equals("1", userSex)) {
                BalanceInfo balanceInfo = XYApplication.getCurrentUserBalance();
                if (balanceInfo.getGoldNum() <= 0 || Double.parseDouble(mUserFreeInfo.getTextFee()) > balanceInfo.getGoldNum()) {
                    if (balanceInfo.getFreeTextNum() <= 0) {
                        Toaster.show("剩余金币不足，请及时充值！");
                        GoldCoinActivity.startAct(ChatActivity.this);
                        return true;
                    } else {
                        sendEmptyGoldMsg(message.getTargetId(), balanceInfo);
                    }
                }
            } else {
                RyUserInfo currentUserInfo = XYApplication.getCurrentUserInfo();
                if (null != currentUserInfo && TextUtils.isEmpty(currentUserInfo.getIsName()) && !TextUtils.equals("1", currentUserInfo.getIsName())) {
                    Toaster.show("还未实名");
                    return true;
                }
                if (null != currentUserInfo && TextUtils.isEmpty(currentUserInfo.getIsReal()) && !TextUtils.equals("1", currentUserInfo.getIsReal())) {
                    Toaster.show("还未认证");
                    return true;
                }
            }
            new Thread(new Runnable() {
                @Override
                public void run() {
                    try {
                        Thread.sleep(3000);
                        presenter.getUserInfo(mUserId);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
            }).start();
            return false;
        }

        @Override
        public boolean interceptOnSentMessage(Message message) {
            LogUtil.e("messageInterceptor", "ChatActivity-->interceptOnSentMessage-->" + message.toString());
            return false;
        }

        @Override
        public boolean interceptOnInsertOutgoingMessage(Conversation.ConversationType type, String targetId, Message.SentStatus sentStatus, MessageContent content, long sentTime) {
            LogUtil.e("messageInterceptor", "ChatActivity-->interceptOnInsertOutgoingMessage-->" + content);
            return false;
        }

        @Override
        public boolean interceptOnInsertIncomingMessage(Conversation.ConversationType type, String targetId, String senderId, Message.ReceivedStatus receivedStatus, MessageContent content, long sentTime) {
            LogUtil.e("messageInterceptor", "ChatActivity-->interceptOnInsertIncomingMessage-->" + content);
            return false;
        }
    };

    @Override
    protected void onResume() {
        super.onResume();
        getUnReadMsg();

        presenter.getUserFee(mUserId);
        presenter.getUserInfo(mUserId);
    }

    @Override
    protected ChatPresenter setPresenter() {
        return new ChatPresenter(this);
    }

    @Override
    public FragmentActivity context() {
        return this;
    }

    @Override
    public void showUserInfo(RyUserInfo userInfo) {

    }

    @Override
    public void showUserDetails(XYUserInfo userInfo) {
        if (null != userInfo) {
            RongUserInfoManager.getInstance().refreshUserInfoCache(new UserInfo(userInfo.getUserId(), (TextUtils.equals("null", userInfo.getNotes()) || TextUtils.isEmpty(userInfo.getNotes())) ? userInfo.getNickname() : userInfo.getNotes(), Uri.parse(TextUtils.isEmpty(userInfo.getAvatar()) ? "https://xinyou-dev.oss-cn-shenzhen.aliyuncs.com/man_default.png" : userInfo.getAvatar())));
            mXyUserInfo = userInfo;
            try {
                PicassoUtils.showImage(mLeftRiv, userInfo.getAvatar());
                userNameTv.setText(userInfo.getNickname());
                tvCity.setText(userInfo.getLiveAddress());
//                tvJob.setText(userInfo.getLiveAddress());

                line1.setVisibility((!TextUtils.isEmpty(userInfo.getOnlineStatus()) && "1".equals(userInfo.getOnlineStatus())) ? View.VISIBLE : View.GONE);
                tvOnline.setVisibility((!TextUtils.isEmpty(userInfo.getOnlineStatus()) && "1".equals(userInfo.getOnlineStatus())) ? View.VISIBLE : View.GONE);
            } catch (Exception e) {

            }
            conversationFragment.setUser(userInfo);
            mXindongTv.setText(userInfo.getMindNum() + "");
        } else {
            finish();
        }
    }

    @Override
    public void showUserBalance(BalanceInfo balanceInfo) {
        if (null != mBottomRvDialog)
            mBottomRvDialog.setUserBalance(balanceInfo);
    }

    @Override
    public void showGiftResult(List<GiftInfo> data) {
        mBottomRvDialog.setNewDatas(data);
    }

    @Override
    public void showCommonPhrases(List<CommonPhrasesInfo> data) {
        mCommonPhrasesInfos = data;
        conversationFragment.setCommonPhrases(mCommonPhrasesInfos);
    }

    @Override
    public void showDeleteCallback(BaseBean baseBean) {
        presenter.getCommonPhrasesList();
    }

    @Override
    public void showAddCallback(BaseBean baseBean) {
        if (200 != baseBean.getCode()) {
            Toaster.show(baseBean.getMessage());
        }
        presenter.getCommonPhrasesList();
    }

    @Override
    public void showSaveResult(BaseBean callbackBean) {
        Toaster.show(callbackBean.getMessage());
        presenter.getUserInfo(mUserId);
    }

    @Override
    public void showUserFreeInfo(UserFreeInfo userFreeInfo) {
        mUserFreeInfo = userFreeInfo;
    }

    private void sendEmptyGoldMsg(String userId, BalanceInfo balanceInfo) {
        Conversation.ConversationType conversationType = Conversation.ConversationType.PRIVATE;
        XYGoldCoinsTextContent messages = XYGoldCoinsTextContent.obtain("温馨提示:交友对象正在使用免费条数，文字剩余" + balanceInfo.getFreeTextNum() + "条，语音 剩余" + balanceInfo.getFreeVoiceNum() + "分钟，视频剩余+" + balanceInfo.getFreeVideoNum() + "分钟，请进行金币充值后使用。");
        Message message = Message.obtain(userId, conversationType, messages);
        message.setSentStatus(Message.SentStatus.SENT);
        long sentTime = System.currentTimeMillis();

        IMCenter.getInstance().insertOutgoingMessage(conversationType, userId, Message.SentStatus.SENT, messages, new RongIMClient.ResultCallback<Message>() {
            @Override
            public void onSuccess(Message message) {

            }

            @Override
            public void onError(RongIMClient.ErrorCode e) {

            }
        });
    }

    public void sendGiftMsg(String userId, GiftInfo giftInfo, int type, int num) {
        EventBus.getDefault().post(giftInfo);
        Conversation.ConversationType conversationType = Conversation.ConversationType.PRIVATE;
        XYGiftContent messages = XYGiftContent.obtain(giftInfo, type + "", num + "");
        Message message = Message.obtain(userId, conversationType, messages);
        message.setCanIncludeExpansion(true);


        IMCenter.getInstance().sendMessage(message, null, null, new IRongCallback.ISendMediaMessageCallback() {
            @Override
            public void onProgress(Message message, int i) {

            }

            @Override
            public void onCanceled(Message message) {

            }

            @Override
            public void onAttached(Message message) {

            }

            @Override
            public void onSuccess(Message message) {

            }

            @Override
            public void onError(final Message message, final RongIMClient.ErrorCode errorCode) {

            }
        });
    }

    private void playGifts() {
        if (!LibCollections.isEmpty(mUnGifts))
            loadAnimation(mUnGifts.get(0));
    }

    private void loadAnimation(String giftUrl) {
//        if (TextUtils.equals(mCurrentGiftUrl, giftUrl)) {
//            return;
//        }
//        mCurrentGiftUrl = giftUrl;


        LogUtil.e("message", "loadAnimation(url) loadAnimation getGiftInfo:播放礼物地址-->" + giftUrl);
        mGiftIV.setVisibility(View.VISIBLE);
        try { // new URL needs try catch.
            SVGAParser svgaParser = SVGAParser.Companion.shareParser();
//            svgaParser.setFrameSize(100, 100);
            svgaParser.decodeFromURL(new URL(giftUrl), new SVGAParser.ParseCompletion() {
                @Override
                public void onComplete(@NotNull SVGAVideoEntity videoItem) {
                    mGiftIV.setVideoItem(videoItem);
                    mGiftIV.startAnimation();
                }

                @Override
                public void onError() {

                }

            }, null);
        } catch (MalformedURLException e) {
            e.printStackTrace();
        }

        mGiftIV.setCallback(new SVGACallback() {
            @Override
            public void onPause() {

            }

            @Override
            public void onFinished() {
                if (!LibCollections.isEmpty(mUnGifts)) {
                    mUnGifts.remove(giftUrl);
                }

                if (LibCollections.isEmpty(mUnGifts)) {
                    mGiftIV.stopAnimation();
                    mGiftIV.setVisibility(View.GONE);
                } else {
                    playGifts();
                }
//                mGiftIV.setVisibility(View.GONE);
            }

            @Override
            public void onRepeat() {

            }

            @Override
            public void onStep(int i, double v) {

            }
        });
    }

    public void choicePic(String userId) {
        PictureSelector.create(ChatActivity.this)
                .openGallery(SelectMimeType.ofAll())
                .setImageEngine(GlideEngine.createGlideEngine())
                .setMaxSelectNum(20)
                .setCompressEngine(new ImageFileCompressEngine())
                .forResult(new OnResultCallbackListener<LocalMedia>() {
                    @Override
                    public void onResult(ArrayList<LocalMedia> result) {
                        for (LocalMedia media : result) {
                            if (media.getMimeType().contains("image/")) {
                                sendImgMsg(userId, media);
                            } else if (media.getMimeType().contains("video/mp4")) {
                                sendVideoMsg(userId, media);
                            }
                        }
                    }

                    @Override
                    public void onCancel() {
//                        Toaster.show("");
                    }
                });
    }

    private void sendMsg(String userId, String content) {
        Conversation.ConversationType conversationType = Conversation.ConversationType.PRIVATE;
        TextMessage messages = TextMessage.obtain(content);
        Message message = Message.obtain(userId, conversationType, messages);
        message.setCanIncludeExpansion(true);
        IMCenter.getInstance().sendMessage(message, null, null, new IRongCallback.ISendMediaMessageCallback() {
            @Override
            public void onProgress(Message message, int i) {

            }

            @Override
            public void onCanceled(Message message) {

            }

            @Override
            public void onAttached(Message message) {

            }

            @Override
            public void onSuccess(Message message) {
                Toaster.show("常用语发送成功!");
            }

            @Override
            public void onError(final Message message, final RongIMClient.ErrorCode errorCode) {

            }
        });
    }

    private void sendVideoMsg(String userId, LocalMedia localMedia) {
        Conversation.ConversationType conversationType = Conversation.ConversationType.PRIVATE;
        SightMessage messages = SightMessage.obtain(ChatActivity.this, Uri.parse(localMedia.getPath()), (int) (localMedia.getDuration() / 1000));
        Message message = Message.obtain(userId, conversationType, messages);
        message.setCanIncludeExpansion(true);

        IMCenter.getInstance().sendMediaMessage(message, null, null, new IRongCallback.ISendMediaMessageCallback() {
            @Override
            public void onProgress(Message message, int i) {
            }

            @Override
            public void onCanceled(Message message) {

            }

            @Override
            public void onAttached(Message message) {

            }

            @Override
            public void onSuccess(Message message) {
            }

            @Override
            public void onError(final Message message, final RongIMClient.ErrorCode errorCode) {
                LogUtil.e("响应结果", "==========onError--》" + message + "    errorCode::" + errorCode.msg);
            }
        });
    }

    private void sendImgMsg(String userId, LocalMedia media) {
        LogUtil.e("响应结果", "sendImg==========文件路劲--》" + media.getPath());
        Conversation.ConversationType conversationType = Conversation.ConversationType.PRIVATE;
        Uri localUri = Uri.parse(media.getPath());
        boolean mIsFull = true; //是否发送原图
        ImageMessage mediaMessageContent = ImageMessage.obtain(localUri, mIsFull);
        Message message = Message.obtain(userId, conversationType, mediaMessageContent);
        message.setCanIncludeExpansion(true);

        IMCenter.getInstance().sendMediaMessage(message, null, null, new IRongCallback.ISendMediaMessageCallback() {
            @Override
            public void onProgress(Message message, int i) {
                LogUtil.e("响应结果", "sendImg==========onProgress--》" + i);
            }

            @Override
            public void onCanceled(Message message) {
                LogUtil.e("响应结果", "sendImg==========onCanceled--》" + message.getSentStatus().getValue());
            }

            @Override
            public void onAttached(Message message) {
                LogUtil.e("响应结果", "sendImg==========onAttached--》" + message);
            }

            @Override
            public void onSuccess(Message message) {
                LogUtil.e("响应结果", "sendImg==========onSuccess--》" + message);
            }

            @Override
            public void onError(final Message message, final RongIMClient.ErrorCode errorCode) {

            }
        });
    }

    private void getUnReadMsg() {
        mUnMsgUserIds = new ArrayList<>();
        RongIMClient.getInstance().getConversationList(new RongIMClient.ResultCallback<List<Conversation>>() {
            @Override
            public void onSuccess(List<Conversation> conversations) {
                for (Conversation chat : conversations) {
                    if (0 < chat.getUnreadMessageCount() && !TextUtils.equals(chat.getTargetId(), mUserId)) {
                        mUnMsgUserIds.add(chat.getTargetId());
                    }
                }
                if (!LibCollections.isEmpty(mUnMsgUserIds)) {
                    mNextMsgId = mUnMsgUserIds.get(0);
                    mNextMsgTv.setVisibility(View.VISIBLE);
                } else {
                    mNextMsgTv.setVisibility(View.GONE);
                }
            }

            @Override
            public void onError(RongIMClient.ErrorCode e) {

            }
        });
    }

    private void showMsgOptDialog() {

        if (null == mChoiceDialog)
            mChoiceDialog = new BottomUserOptDialog(ChatActivity.this);
        mChoiceDialog.setOnDialogCallbackListener(new BottomUserOptDialog.OnDialogCallbackListener() {
            @Override
            public void onDialogSelectItem(int type) {
                optUser(type);
            }
        });
        mChoiceDialog.show();
        mChoiceDialog.setUserBlack(mUserBlackStatus);
    }

    private void optUser(int type) {
        switch (type) {
            case 1:
                showUpdateUserNickNameDialog();
                break;

            case 2:
                if (mUserBlackStatus == IRongCoreEnum.BlacklistStatus.IN_BLACK_LIST.getValue())
                    RongIMClient.getInstance().removeFromBlacklist(mUserId, blackCallback);
                else
                    RongIMClient.getInstance().addToBlacklist(mUserId, blackCallback);
                break;

            case 3:
                ReportUserActivity.start(ChatActivity.this, mUserId);
                break;
        }
    }

    private void showUpdateUserNickNameDialog() {
        UpdateUserNicknameDialog updateUserNicknameDialog = new UpdateUserNicknameDialog(ChatActivity.this);
        updateUserNicknameDialog.setOnDialogCallbackListener(new UpdateUserNicknameDialog.OnDialogCallbackListener() {
            @Override
            public void onDialogSelectItem(String userName) {
                LogUtil.e("响应结果", "==========conversation--》修改userId:" + mXyUserInfo.getUserId());
                RongUserInfoManager.getInstance().refreshUserInfoCache(new UserInfo(mXyUserInfo.getUserId(), userName, Uri.parse(mXyUserInfo.getAvatar())));

                presenter.updateUserNickName(userName, mUserId);

            }
        });
        updateUserNicknameDialog.show();
    }

    RongIMClient.OperationCallback blackCallback = new RongIMClient.OperationCallback() {
        @Override
        public void onSuccess() {
            if (mUserBlackStatus == IRongCoreEnum.BlacklistStatus.IN_BLACK_LIST.getValue()) {
                Toaster.show("已移除黑名单");
                mUserBlackStatus = IRongCoreEnum.BlacklistStatus.NOT_IN_BLACK_LIST.getValue();
            } else {
                Toaster.show("已加入黑名单");
                mUserBlackStatus = IRongCoreEnum.BlacklistStatus.IN_BLACK_LIST.getValue();
            }
        }

        @Override
        public void onError(RongIMClient.ErrorCode errorCode) {

        }
    };

    @Override
    protected boolean isUseEventBus() {
        return true;
    }

    @Override
    public boolean onActionBarClickListener(int function) {
        switch (function) {
            case CustomActionBar.FUNCTION_BUTTON_LEFT:
                finish();
                break;

            case CustomActionBar.FUNCTION_TEXT_RIGHT:
                showMsgOptDialog();
                break;
        }
        return false;
    }

    @Override
    public void onChange(boolean isShow) {
        if (isShow) {
            mBottomLayout.setVisibility(View.GONE);
        } else {
            mBottomLayout.setVisibility(View.VISIBLE);
        }
    }

}
