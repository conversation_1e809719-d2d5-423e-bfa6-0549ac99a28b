package com.chat.laty.activity;

import android.content.Context;
import android.content.Intent;

import androidx.annotation.NonNull;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.RecyclerView;

import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshLoadMoreListener;
import com.chat.laty.R;
import com.chat.laty.adapter.VisitorAdapter;
import com.chat.laty.base.XYApplication;
import com.chat.laty.controllers.VisitorController;
import com.chat.laty.entity.VisitorUserInfo;
import com.chat.laty.mvp.BasePresenterActivity;
import com.chat.laty.presenters.VisitorPresenter;
import com.chat.laty.view.CustomActionBar;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import cn.xjc_soft.lib_utils.LibCollections;

/**
 * <AUTHOR>
 * @date 2024/1/19 19:32
 * @description:看过我
 */
public class VisitorMeUI extends BasePresenterActivity<VisitorPresenter> implements VisitorController.View {
    @BindView(R.id.custom_bar)
    CustomActionBar mActionBar;

    @BindView(R.id.base_rv)
    RecyclerView mRecyclerView;

    @BindView(R.id.activity_srl)
    SmartRefreshLayout mSmartRefreshLayout;

    VisitorAdapter mAdapter;

    List<VisitorUserInfo> mDatas = new ArrayList<>();

    public static void start(Context context) {
        Intent intent = new Intent(context, VisitorMeUI.class);
        context.startActivity(intent);
    }

    @Override
    protected void initDatas() {

    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_base_smartrefresh_recyclerview_layout;
    }

    @Override
    protected void initViews() {
        mActionBar.setTitleText("访客中心");
        mActionBar.setTitleTextColor(getResources().getColor(R.color.color_333333));

        mAdapter = new VisitorAdapter();
        mRecyclerView.setAdapter(mAdapter);

        mAdapter.setOnItemClickListener((adapter, view, position) -> {
            if (XYApplication.appLogin(true)) {
                VisitorUserInfo info = (VisitorUserInfo) adapter.getItem(position);
                UserCenterUI.start(VisitorMeUI.this, info.getToUserId());
            }
        });

        mAdapter.setStateViewEnable(true);
        mAdapter.setStateView(createEmptyListView("暂无数据", R.mipmap.icon_nodata));
        mSmartRefreshLayout.setOnRefreshLoadMoreListener(new OnRefreshLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                PAGE_INDEX++;
                presenter.getUserVisitorList(PAGE_INDEX);
            }

            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                PAGE_INDEX = 1;
                presenter.getUserVisitorList(PAGE_INDEX);
            }
        });

        mSmartRefreshLayout.autoRefresh();
    }

    @Override
    protected VisitorPresenter setPresenter() {
        return new VisitorPresenter(this);
    }

    @Override
    public FragmentActivity context() {
        return this;
    }

    @Override
    public void showVisitorList(List<VisitorUserInfo> list) {
        if (LibCollections.isEmpty(list) || list.size() < PAGE_SIZE) {
            mSmartRefreshLayout.setEnableLoadMore(false);
        }

        if (PAGE_INDEX == MIN_PAGE_INDEX_1) {
            mDatas = list;
            mSmartRefreshLayout.finishRefresh();
        } else {
            mDatas.addAll(list);
            mSmartRefreshLayout.finishLoadMore();
        }

        mAdapter.setStateView(createEmptyListView("暂无数据", R.mipmap.icon_nodata));
        if (!LibCollections.isEmpty(mDatas))
            mAdapter.setItems(mDatas);
        mAdapter.notifyDataSetChanged();
    }
}
