package com.chat.laty.activity;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.EditText;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.SwitchCompat;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.RecyclerView;

import com.alibaba.fastjson.JSON;
import com.hjq.toast.Toaster;
import com.luck.picture.lib.basic.PictureSelector;
import com.luck.picture.lib.config.PictureConfig;
import com.luck.picture.lib.entity.LocalMedia;
import com.otaliastudios.transcoder.Transcoder;
import com.otaliastudios.transcoder.TranscoderListener;
import com.chat.laty.R;
import com.chat.laty.adapter.MediaPickerAdapter;
import com.chat.laty.controllers.DynamicController;
import com.chat.laty.entity.MediaItemModel;
import com.chat.laty.entity.MediaItemStatus;
import com.chat.laty.entity.UploadImgInfo;
import com.chat.laty.mvp.BasePresenterActivity;
import com.chat.laty.presenters.DynamicPresenter;
import com.chat.laty.utils.XYSPUtils;
import com.chat.laty.utils.net.Common;
import com.chat.laty.view.CustomActionBar;

import java.io.File;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import butterknife.BindView;
import butterknife.OnClick;

public class MineDynamicReleaseUI extends BasePresenterActivity<DynamicPresenter> implements DynamicController.ReleaseView, DynamicController.UploadView {

    @BindView(R.id.custom_bar)
    CustomActionBar customBar;
    @BindView(R.id.et_input)
    EditText editText;
    @BindView(R.id.recycler_view)
    RecyclerView recyclerView;

    public String isMents = "0";

    public static void startAct(Context context) {
        Intent intent = new Intent(context, MineDynamicReleaseUI.class);
        context.startActivity(intent);
    }

    MediaPickerAdapter adapter;

    @Override
    protected void initDatas() {
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_release_dynamic_layout;
    }

    @Override
    protected void initViews() {
        customBar.setTitleText("发布动态");
        customBar.setAllColor(ContextCompat.getColor(this, R.color.black));

        customBar.setOnActionBarClickListerner(function -> {
            if (function == CustomActionBar.FUNCTION_BUTTON_LEFT) {
                MineDynamicReleaseUI.this.finish();
                return true;
            }
            return false;
        });

        adapter = new MediaPickerAdapter(this);
        recyclerView.setAdapter(adapter);

        SwitchCompat switchComments = findViewById(R.id.switch_comments);
        switchComments.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (isChecked) {
                isMents = "0";
            } else {
                isMents = "1";
            }
        });
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, @Nullable @org.jetbrains.annotations.Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK) {
            switch (requestCode) {
                case PictureConfig.CHOOSE_REQUEST:
                    List<LocalMedia> list = PictureSelector.obtainSelectorList(data);
                    List<MediaItemModel> selected = new ArrayList<>();
                    for (LocalMedia item : list) {
                        MediaItemModel model = new MediaItemModel(item.getRealPath(), item.getMimeType());
                        model.status = MediaItemStatus.uploading;
                        selected.add(model);
                        if (item.getMimeType().contains("image")) {
                            presenter.uploadFiles(item);
                        } else {
                            compress(item, model);
                        }
                    }
                    if (selected.isEmpty()) return;
                    adapter.setList(selected);
                    break;
            }
        }
    }

    public static long maxLength = (long) (30 * 1024 * 1024 / 0.8);

    private final Set<String> urls = new HashSet<>();

    @SuppressLint("NotifyDataSetChanged")
    private void compress(LocalMedia item, MediaItemModel model) {

        File origin = new File(item.getRealPath());
        if (origin.exists() && origin.isFile()) {
            Log.e(TAG, "Origin compress: ==>" + origin.length());
            if (origin.length() > maxLength) {
                model.status = MediaItemStatus.fail;
                adapter.notifyDataSetChanged();
                Toaster.show("文件过大，请重新选择～");
                return;
            }
            if (origin.length() <= 5 * 1024 * 1024) {
                model.status = MediaItemStatus.uploading;
                adapter.notifyDataSetChanged();
                presenter.uploadFiles(item);
                return;
            }
        }

        // https://github.com/yellowcath/VideoProcessor
        String dir = getCacheDir().getAbsolutePath() + "/video/";
        File filePath = new File(dir);
        if (!filePath.exists()) {
            filePath.mkdir();
        }
        String fileName = System.currentTimeMillis() + ".mp4";
        File target = new File(filePath, fileName);

        model.status = MediaItemStatus.compress;
        adapter.notifyDataSetChanged();

        Toaster.show("正在压缩资源～");
        Transcoder.into(target.getAbsolutePath())
                .addDataSource(item.getRealPath()) // or
                // ...
                .setListener(new TranscoderListener() {
                    public void onTranscodeProgress(double progress) {
                        Log.e(TAG, "onTranscodeProgress: " + progress);
                    }

                    public void onTranscodeCompleted(int successCode) {
                        Log.e(TAG, "onTranscodeCompleted: " + target.length());
                        model.status = MediaItemStatus.uploading;
                        adapter.notifyDataSetChanged();
                        item.setCompressPath(target.getAbsolutePath());
                        presenter.uploadFiles(item);
                        urls.add(item.getCompressPath());
                    }

                    public void onTranscodeCanceled() {

                    }

                    public void onTranscodeFailed(@NonNull Throwable exception) {
                        Log.e(TAG, "onTranscodeFailed: " + exception);
                        model.status = MediaItemStatus.fail;
                        adapter.notifyDataSetChanged();
                    }
                }).transcode();
    }

    @Override
    protected DynamicPresenter setPresenter() {
        return new DynamicPresenter(this, this);
    }

    @Override
    public FragmentActivity context() {
        return this;
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        for (String url : urls) {
            File file = new File(url);
            if (file.exists()) {
                Log.e(TAG, "onDestroy: " + file.length());
                file.delete();
            }
        }
    }

    @OnClick({R.id.btn_release})
    public void onClick(View view) {
        List<MediaItemModel> list = adapter.getList();

        if (list.isEmpty()) {
            Toaster.show("请上传图片/视频");
            return;
        }
        boolean data = false;

        for (MediaItemModel item : list) {
            if (item.status == MediaItemStatus.compress) {
                Toaster.show("图片/视频正在压缩，请稍等～");
                return;
            }
            if (item.status == MediaItemStatus.uploading) {
                Toaster.show("图片/视频正在上传，请稍等～");
                return;
            }
            if (item.status == MediaItemStatus.succeed) {
                data = true;
            }
        }
        if (!data) {
            Toaster.show("图片/视频上传失败，请重新上传～");
            return;
        }

        showProgressDialog(R.string.app_release);

        int type = 1;
        String urls = "";
        if (list.get(0).getMimeType().contains("video")) {
            urls = list.get(0).url;
            type = 2;
        } else {

            List<UploadImgInfo> li = new ArrayList<>();

            for (MediaItemModel item : list) {
                UploadImgInfo info = new UploadImgInfo();
                info.setImgUrl(item.url);
                info.setTumhImgUrl(item.thumbUrl);
                li.add(info);
            }
            urls = JSON.toJSONString(li);
        }

        presenter.release(editText.getText().toString(), urls, type, isMents, this);
    }

    @Override
    public void uploadSucceed(LocalMedia item, List<UploadImgInfo> infos) {

        for (MediaItemModel model : adapter.getList()) {
            if (item.getRealPath().equals(model.getPath())) {
                model.status = MediaItemStatus.succeed;
                model.url = infos.get(0).getImgUrl();
                model.thumbUrl = infos.get(0).getTumhImgUrl();
            }
        }
        adapter.notifyDataSetChanged();
    }

    @Override
    public void uploadFailed(LocalMedia item) {
        Toaster.show("上传失败～");
        for (MediaItemModel model : adapter.getList()) {
            if (item.getRealPath().equals(model.getPath())) {
                model.status = MediaItemStatus.fail;
            }
        }

        adapter.notifyDataSetChanged();
    }

    @Override
    public void releaseSucceed() {
//        editText.setText("");
//        adapter.clear();
        dismissProgressDialog();
        Toaster.show("发布成功～");
        String userId = XYSPUtils.getString(Common.KEY_APP_USER_RY_ID);
        UserDynamicsActivity.start(this, userId);
        MineDynamicReleaseUI.this.finish();
    }

    @Override
    public void releaseFailed(String msg) {
        if (!TextUtils.isEmpty(msg)) {
            Toaster.show(msg);
        }
        dismissProgressDialog();
    }
}
