package com.chat.laty.activity;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.view.KeyEvent;
import android.view.View;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentActivity;

import com.hjq.toast.Toaster;
import com.chat.laty.MainActivity;
import com.chat.laty.R;
import com.chat.laty.base.XYApplication;
import com.chat.laty.contants.Constant;
import com.chat.laty.controllers.LoginController;
import com.chat.laty.entity.LoginEntity;
import com.chat.laty.entity.LoinEntity;
import com.chat.laty.entity.RYTokenInfo;
import com.chat.laty.entity.WXLoginInfo;
import com.chat.laty.entity.WebBeanInfo;
import com.chat.laty.entity.event.WeiXinCodeEvent;
import com.chat.laty.mvp.BasePresenterActivity;
import com.chat.laty.presenters.LoginPresenter;
import com.chat.laty.presenters.PayManager;
import com.chat.laty.utils.XYSPUtils;
import com.chat.laty.utils.net.Common;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import butterknife.BindView;
import butterknife.OnClick;
import io.rong.imkit.RongIM;

/**
 * <AUTHOR>
 * @date 2023/9/11 15:36
 * @description:微信登录
 */
public class LoginByWeiChatUI extends BasePresenterActivity<LoginPresenter> implements LoginController.View {
    @BindView(R.id.agree_checkbox)
    CheckBox mAgreeCb;

//    @BindView(R.id.user_read_tv)
//    TextView mAgreeTv;

    @BindView(R.id.qitadenglufangshi)
    TextView mQitaDengluFangshi;

    @BindView(R.id.weichat_login_riv)
    ImageView weichatLoginRiv;

    private boolean mIsAgree = false;

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void weiXinLogin(WeiXinCodeEvent event) {
        showProgressDialog(R.string.app_uploadding);
        presenter.weiXinLogin(event.getCode());
    }


    public static void startNew(Context context) {
        Intent intent = new Intent(context, LoginByWeiChatUI.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);
        context.startActivity(intent);
    }

    @Override
    protected void initDatas() {

    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_login_by_weichat;
    }

    @Override
    protected void initViews() {
        mAgreeCb.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                mIsAgree = isChecked;
            }
        });
        RongIM.getInstance().logout();

        String testText = "我已认真阅读并同意《联通统一认证服务条款》,《服务协议》和《隐私政策》。";

        SpannableString spannableString = new SpannableString(testText);
        ClickableSpan clickableSpan = new ClickableSpan() {
            @Override
            public void onClick(@NonNull View widget) {
                presenter.getAccordByNum(10);
            }

            @Override
            public void updateDrawState(@NonNull TextPaint ds) {
                ds.setColor(ContextCompat.getColor(LoginByWeiChatUI.this, R.color.color_FF6D7A));
            }
        };
        ClickableSpan clickableSpan2 = new ClickableSpan() {
            @Override
            public void onClick(@NonNull View widget) {
                presenter.getAccordByNum(14);
            }

            @Override
            public void updateDrawState(@NonNull TextPaint ds) {
                ds.setColor(ContextCompat.getColor(LoginByWeiChatUI.this, R.color.color_FF6D7A));
            }
        };
        ClickableSpan clickableSpan3 = new ClickableSpan() {
            @Override
            public void onClick(@NonNull View widget) {
                presenter.getAccordByNum(12);
            }

            @Override
            public void updateDrawState(@NonNull TextPaint ds) {
                ds.setColor(ContextCompat.getColor(LoginByWeiChatUI.this, R.color.color_FF6D7A));
            }
        };
        spannableString.setSpan(clickableSpan2, 9, 21, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(clickableSpan, 22, 28, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(clickableSpan3, 29, 35, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);

//        mAgreeTv.setText(spannableString);
//        mAgreeTv.setMovementMethod(LinkMovementMethod.getInstance());
//        mAgreeTv.setHighlightColor(Color.TRANSPARENT);

        if (!Common.WEICHAT_LOGIN) {
            mQitaDengluFangshi.setVisibility(View.GONE);
            weichatLoginRiv.setVisibility(View.GONE);
        }
    }

    @OnClick({R.id.login_button, R.id.weichat_login_riv})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.login_button:
                XYSPUtils.delete(Common.KEY_APP_USER_OPENID);
                LoginByPhoneUI.start(LoginByWeiChatUI.this);
                finish();
                break;

            case R.id.weichat_login_riv:
                if (!mIsAgree) {
                    Toaster.show("请阅读并同意用户协议");
                    return;
                }
                XYSPUtils.delete(Common.KEY_APP_USER_PHONE);
                PayManager.wxLogin(LoginByWeiChatUI.this);
//                Toaster.show("演示版已限制微信登录新用户，请用手机号登录 男用户（验证码都是000000）18888888888、18888888887、18888888886 女用户（验证码都是000000）17777777778、17777777777、17777777776");
                break;
        }
    }

    @Override
    protected LoginPresenter setPresenter() {
        return new LoginPresenter(this);
    }


    @Override
    public FragmentActivity context() {
        return this;
    }

    @Override
    public void showCountDown(long millis) {
    }

    @Override
    public void showCountDownFinish() {
    }

    @Override
    public void showLogin(LoinEntity data) {
        dismissProgressDialog();
        if (null != data) {
            presenter.getRongCloudToken();
            Toaster.show(data.getUserInfo().getNickname());
        }
    }

    @Override
    public void showRyToken(RYTokenInfo data) {
        if (null != data && !TextUtils.isEmpty(data.getToken())) {
            dismissProgressDialog();
            XYSPUtils.put(Constant.KEY_USER_RONGYUN_TOKEN, data.getToken());
        }
    }

    @Override
    public void showLoginCallBack(LoginEntity loginInfo) {

    }

    @Override
    public void showWeiXinLogin(WXLoginInfo wxInfo) {
        if (null != wxInfo) {
            if (0 == wxInfo.getIsNewUser()) {
                presenter.getRongCloudToken();
                XYSPUtils.put(Common.KEY_APP_TOKEN, wxInfo.getToken());
                XYSPUtils.put(Common.KEY_APP_USER_RY_ID, wxInfo.getLoginUser().getId());
                XYSPUtils.put(Common.KEY_APP_USER_GENDER, wxInfo.getLoginUser().getSex());
                XYApplication.getDaoInstant().getRyUserInfoDao().insertOrReplaceInTx(wxInfo.getLoginUser());
            } else {
                XYSPUtils.put(Common.KEY_APP_USER_OPENID, wxInfo.getOpenId());
                ComplementUserInfoUI.start(LoginByWeiChatUI.this, "");
                finish();
            }
        }
    }

    @Override
    public void showLoginImCallback() {
        MainActivity.start(LoginByWeiChatUI.this);
        finish();
    }

    @Override
    public void showWebInfo(WebBeanInfo info) {
        WebViewActivity.startActivity(LoginByWeiChatUI.this, "", info.getUrl());
    }

    @Override
    public void loadVipData() {
        MainActivity.start(LoginByWeiChatUI.this);
        finish();
    }

    @Override
    protected boolean isUseEventBus() {
        return true;
    }

    @SuppressLint("RestrictedApi")
    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        if (event.getAction() == KeyEvent.ACTION_DOWN && event.getKeyCode() == KeyEvent.KEYCODE_BACK) {
            finishAffinity();
            return true;
        }
        return super.dispatchKeyEvent(event);
    }

}
