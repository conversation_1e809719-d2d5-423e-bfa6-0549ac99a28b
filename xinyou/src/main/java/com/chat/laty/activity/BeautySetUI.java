package com.chat.laty.activity;

import android.content.Context;
import android.content.Intent;
import android.hardware.Sensor;
import android.hardware.SensorEvent;
import android.hardware.SensorEventListener;
import android.hardware.SensorManager;
import android.view.Gravity;
import android.view.View;
import android.widget.CompoundButton;
import android.widget.FrameLayout;

import androidx.fragment.app.FragmentActivity;

import com.allen.library.SuperTextView;
import com.faceunity.core.enumeration.FUInputTextureEnum;
import com.faceunity.nama.FURenderer;
import com.faceunity.nama.data.FaceUnityDataFactory;
import com.chat.laty.entity.SimiInfo;
import com.chat.laty.face.db.FaceSettingHelper;
import com.chat.laty.face.listener.FaceSettingListener;
import com.chat.laty.face.ui.FaceUnityView;
import com.hjq.permissions.OnPermissionCallback;
import com.hjq.permissions.Permission;
import com.hjq.permissions.XXPermissions;
import com.hjq.toast.Toaster;
import com.chat.laty.R;
import com.chat.laty.controllers.GeneralSettingController;
import com.chat.laty.entity.BaseBean;
import com.chat.laty.entity.GeneralSettingInfo;
import com.chat.laty.entity.WebBeanInfo;
import com.chat.laty.mvp.BasePresenterActivity;
import com.chat.laty.presenters.GeneralSettingPresenter;
import com.chat.laty.utils.PermissionInterceptor;
import com.chat.laty.utils.XYSPUtils;
import com.chat.laty.utils.net.Common;
import com.chat.laty.view.CustomActionBar;

import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;
import cn.rongcloud.callplus.api.RCCallPlusClient;
import cn.rongcloud.callplus.api.RCCallPlusLocalVideoView;
import cn.rongcloud.fubeautifier.FUBeautifierResultCallback;
import cn.rongcloud.fubeautifier.RCRTCFUBeautifierEngine;

/**
 * <AUTHOR>
 * @date 2023/12/24 20:46
 * @description:
 */
public class BeautySetUI extends BasePresenterActivity<GeneralSettingPresenter> implements GeneralSettingController.View {

    @BindView(R.id.custom_bar)
    CustomActionBar mActionBar;

    @BindView(R.id.local)
    FrameLayout mLocalVideoViewFrameLayout;

    @BindView(R.id.faceunity_control)
    FaceUnityView mFaceUnityView;

    private FURenderer mFURenderer;

    private FaceUnityDataFactory mFaceUnityDataFactory;
    private SensorManager mSensorManager;

    private FaceSettingListener mFaceSettingListener;
    private final FaceSettingHelper mFaceSettingHelper = new FaceSettingHelper();

    public static void start(Context context) {
        Intent intent = new Intent(context, BeautySetUI.class);
        context.startActivity(intent);
    }

    @Override
    protected void initDatas() {
        String userId = XYSPUtils.getString(Common.KEY_APP_USER_RY_ID);
        mFaceSettingListener = new FaceSettingListener(mFaceSettingHelper.getFaceSetting(userId, true));
    }

    @Override
    protected void onStop() {
        super.onStop();
        mFaceSettingHelper.saveFaceSetting(mFaceSettingListener.getFaceSetting());
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        RCCallPlusClient.getInstance().stopCamera();
        mLocalVideoViewFrameLayout.removeAllViews();
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_beaut_settings_activity;
    }

    @Override
    protected void initViews() {
        mActionBar.setTitleText("美颜设置");
        mActionBar.setTitleTextColor(getResources().getColor(R.color.color_333333));

        XXPermissions.with(BeautySetUI.this).permission(Permission.RECORD_AUDIO, Permission.CAMERA).interceptor(new PermissionInterceptor()).request(new OnPermissionCallback() {
            @Override
            public void onGranted(List<String> permissions, boolean all) {
                if (!all) {
                    return;
                }
                setLocalVideoView();
            }
        });

        RCRTCFUBeautifierEngine.getInstance().setBeautyEnable(true, new FUBeautifierResultCallback() {
            @Override
            public void onSuccess() {
//                Toaster.show("美颜打开成功");
                mFaceUnityView.setSettingListener(mFaceSettingListener);
            }

            @Override
            public void onFailed(int code) {
                Toaster.show("美颜打开失败--->code==" + code);
            }
        });

        mFURenderer = FURenderer.getInstance();
        mFURenderer.setInputTextureType(FUInputTextureEnum.FU_ADM_FLAG_COMMON_TEXTURE);
        mFURenderer.setMarkFPSEnable(true);

        mFaceUnityDataFactory = FaceUnityDataFactory.getInstance();
        mFaceUnityView.bindDataFactory(mFaceUnityDataFactory);
        mFaceSettingHelper.initFaceSettingData(mFaceUnityDataFactory, mFaceSettingListener.getFaceSetting());
        mFaceUnityView.postDelayed(() -> {
            mFaceUnityView.setFaceBeautyControlChecked(mFaceSettingListener.getFaceSetting().getEnableFaceBeauty());
            mFaceUnityView.showFaceBeautyControlView();
        },200);

        mFaceUnityView.findViewById(R.id.control_beauty).setVisibility(View.GONE);
        mSensorManager = (SensorManager) getSystemService(SENSOR_SERVICE);
        Sensor sensor = mSensorManager.getDefaultSensor(Sensor.TYPE_ACCELEROMETER);
        mSensorManager.registerListener(new SensorEventListener() {
            @Override
            public void onSensorChanged(SensorEvent event) {

                float x = event.values[0];
                float y = event.values[1];
                if (Math.abs(x) > 3 || Math.abs(y) > 3) {
                    if (Math.abs(x) > Math.abs(y)) {
                        mFURenderer.setDeviceOrientation(x > 0 ? 0 : 180);
                    } else {
                        mFURenderer.setDeviceOrientation(y > 0 ? 90 : 270);
                    }
                }
            }

            @Override
            public void onAccuracyChanged(Sensor sensor, int accuracy) {

            }
        }, sensor, SensorManager.SENSOR_DELAY_UI);
    }


    private void setLocalVideoView() {
        RCCallPlusClient.getInstance().startCamera();
        //创建本地视图对象
        RCCallPlusLocalVideoView localVideoView = new RCCallPlusLocalVideoView(this.getApplicationContext());
        //FIT: 视频帧通过保持宽高比(可能显示黑色边框)来缩放以适应视图的大小
//        localVideoView.setRenderMode(RCCallPlusRenderMode.FIT);
        //设置本地视图给SDK
        RCCallPlusClient.getInstance().setVideoView(localVideoView);

        FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(FrameLayout.LayoutParams.MATCH_PARENT, FrameLayout.LayoutParams.MATCH_PARENT);
        params.gravity = Gravity.CENTER_HORIZONTAL;//在父布局中横向居中显示
        //将本地视图添加到XML中显示
        //示例代码中 mLocalVideoViewFrameLayout 为 android.widget.FrameLayout 对象
        mLocalVideoViewFrameLayout.removeAllViews();
        mLocalVideoViewFrameLayout.addView(localVideoView);
    }

    SuperTextView.OnSwitchCheckedChangeListener switchChecked = new SuperTextView.OnSwitchCheckedChangeListener() {
        @Override
        public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
        }
    };

    @OnClick({R.id.local})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.local:
                RCRTCFUBeautifierEngine.getInstance().setBeautyEnable(true, new FUBeautifierResultCallback() {
                    @Override
                    public void onSuccess() {
//                Toaster.show("美颜打开成功");
                    }

                    @Override
                    public void onFailed(int code) {
                        Toaster.show("美颜打开失败--->code==" + code);
                    }
                });
                RCCallPlusClient.getInstance().stopCamera();
                RCCallPlusClient.getInstance().startCamera();
                break;
        }
    }

    @Override
    protected GeneralSettingPresenter setPresenter() {
        return new GeneralSettingPresenter(this);
    }

    @Override
    public FragmentActivity context() {
        return this;
    }

    @Override
    public void showGeneralSetting(GeneralSettingInfo info) {
    }

    @Override
    public void showWebInfo(WebBeanInfo info) {
        WebViewActivity.startActivity(BeautySetUI.this, "隐私政策", info.getUrl());
    }

    @Override
    public void showDeleteCallback(BaseBean baseBean) {

    }

    @Override
    public void showLogoutCallback(BaseBean uploadBean) {

    }

    @Override
    public void getSimiInfo(SimiInfo simiInfo) {

    }

}
