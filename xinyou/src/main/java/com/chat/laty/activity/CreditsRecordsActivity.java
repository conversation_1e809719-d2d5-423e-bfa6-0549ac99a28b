package com.chat.laty.activity;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.chat.laty.R;
import com.chat.laty.controllers.CreditsController;
import com.chat.laty.entity.CreditsRecordsModel;
import com.chat.laty.mvp.BasePresenterActivity;
import com.chat.laty.presenters.CreditsStorePresenter;
import com.chat.laty.view.CustomActionBar;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;

public class CreditsRecordsActivity extends BasePresenterActivity<CreditsStorePresenter> implements CreditsController.ICreditsRecordsView {

    public static void startAct(Context context) {
        Intent intent = new Intent(context, CreditsRecordsActivity.class);
        context.startActivity(intent);
    }

    @BindView(R.id.custom_bar)
    CustomActionBar customBar;
    @BindView(R.id.recycler_view)
    RecyclerView recyclerView;

    List<CreditsRecordsModel> list = new ArrayList<>();

    CreditsRecordAdapter adapter;

    @Override
    protected void initDatas() {

    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_vip_recharge_records;
    }

    @Override
    protected void initViews() {
        customBar.setTitleText("兑换记录");
        customBar.setAllColor(ContextCompat.getColor(this,R.color.black));

        customBar.setOnActionBarClickListerner(function -> {
            if (function == CustomActionBar.FUNCTION_BUTTON_LEFT) {
                CreditsRecordsActivity.this.finish();
                return true;
            }
            return false;
        });

        adapter = new CreditsRecordAdapter();
        adapter.setItems(list);
        recyclerView.setAdapter(adapter);

        presenter.getRecordsList();
    }

    @Override
    protected CreditsStorePresenter setPresenter() {
        return new CreditsStorePresenter(null, this, null);
    }


    @Override
    public FragmentActivity context() {
        return this;
    }

    @SuppressLint("NotifyDataSetChanged")
    @Override
    public void getRecordsSucceed(List<CreditsRecordsModel> list) {
        this.list.clear();
        this.list.addAll(list);

        adapter.setStateView(getLayoutInflater().inflate(R.layout.community_empty_layout, null));
        adapter.setStateViewEnable(true);

        adapter.notifyDataSetChanged();
    }

    @Override
    public void getRecordsFailed() {
    }

    private static class CreditsRecordAdapter extends BaseQuickAdapter<CreditsRecordsModel, QuickViewHolder> {

        @Override
        protected void onBindViewHolder(@NonNull QuickViewHolder helper, int i, @Nullable CreditsRecordsModel item) {
            helper.setText(R.id.tv_detail, item.getExchangeDetail());
            helper.setText(R.id.tv_time, "购买时间：" + item.getCreateTime());
            helper.setText(R.id.tv_value, "-" + item.getChangeIntegral());
        }

        @NonNull
        @Override
        protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int i) {
            return new QuickViewHolder(R.layout.adapter_credits_records_layout, viewGroup);
        }
    }
}