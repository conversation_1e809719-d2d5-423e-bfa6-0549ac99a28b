package com.chat.laty.activity;

import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.view.View;

import androidx.appcompat.widget.AppCompatTextView;
import androidx.fragment.app.FragmentActivity;

import com.allen.library.SuperTextView;
import com.hjq.toast.Toaster;
import com.chat.laty.R;
import com.chat.laty.controllers.CostSetController;
import com.chat.laty.dialog.BottomSignWheelViewDialog;
import com.chat.laty.entity.BaseBean;
import com.chat.laty.entity.CostBean;
import com.chat.laty.entity.PickerBean;
import com.chat.laty.entity.WebBeanInfo;
import com.chat.laty.mvp.BasePresenterActivity;
import com.chat.laty.presenters.CostSetPresenter;
import com.chat.laty.view.CustomActionBar;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * <AUTHOR>
 * @date 2023/12/29 9:29
 * @description:
 */
public class CostSettingUI extends BasePresenterActivity<CostSetPresenter> implements CostSetController.View, CustomActionBar.OnActionBarClickListerner {

    @BindView(R.id.custom_bar)
    CustomActionBar customBar;
    @BindView(R.id.text_chat_cost_stv)
    SuperTextView textChatCostStv;
    @BindView(R.id.text_chat_time_stv)
    AppCompatTextView textChatTimeStv;
    @BindView(R.id.yuyin_chat_cost_stv)
    SuperTextView yuyinChatCostStv;
    @BindView(R.id.yuyin_chat_time_stv)
    AppCompatTextView yuyinChatTimeStv;
    @BindView(R.id.video_chat_cost_stv)
    SuperTextView videoChatCostStv;
    @BindView(R.id.video_chat_time_stv)
    AppCompatTextView videoChatTimeStv;

    BottomSignWheelViewDialog mBottomWheelDialog;

    CostBean mCostInfo;

    List<PickerBean> mSignWheelDatas = new ArrayList<>();

    public static void start(Context context) {
        Intent intent = new Intent(context, CostSettingUI.class);
        context.startActivity(intent);
    }

    @Override
    protected void initDatas() {

    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_cost_set_layout;
    }

    @Override
    protected void initViews() {
        customBar.setTitleText("收费设置");
        customBar.addFunction(CustomActionBar.FUNCTION_TEXT_RIGHT);
        customBar.setRightText("升级标准");
        customBar.setTitleTextColor(getResources().getColor(R.color.color_333333));
        customBar.setRightTextColor(getResources().getColor(R.color.color_333333));
        customBar.setOnActionBarClickListerner(this);
        presenter.getUserFeeSettings();
    }

    @Override
    protected CostSetPresenter setPresenter() {
        return new CostSetPresenter(this);
    }

    @Override
    public FragmentActivity context() {
        return this;
    }

    @OnClick({R.id.text_chat_cost_stv, R.id.yuyin_chat_cost_stv, R.id.video_chat_cost_stv})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.text_chat_cost_stv:
                showBottomSignWheel(1, textChatCostStv);
                break;

            case R.id.yuyin_chat_cost_stv:
                showBottomSignWheel(2, yuyinChatCostStv);
                break;

            case R.id.video_chat_cost_stv:
                showBottomSignWheel(3, videoChatCostStv);
                break;
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        customBar.setRightTextColor(getResources().getColor(R.color.color_333333));
    }

    @Override
    public void showCostCallback(CostBean costInfo) {
        mCostInfo = costInfo;
        if (!TextUtils.isEmpty(costInfo.getTextFee()))
            textChatCostStv.setRightString(costInfo.getTextFee() + "金币/条");
        if (!TextUtils.isEmpty(costInfo.getVoiceFee()))
            yuyinChatCostStv.setRightString(costInfo.getVoiceFee() + "金币/条");
        if (!TextUtils.isEmpty(costInfo.getVideoFee()))
            videoChatCostStv.setRightString(costInfo.getVideoFee() + "金币/条");
        if (!TextUtils.isEmpty(costInfo.getTotalOnlineTime()))
            textChatTimeStv.setText("在线总时长:" + costInfo.getTotalOnlineTime());
        if (!TextUtils.isEmpty(costInfo.getTotalVoiceTime()))
            yuyinChatTimeStv.setText("语音通话总时长:" + costInfo.getTotalVoiceTime());
        if (!TextUtils.isEmpty(costInfo.getTotalVideoTime()))
            videoChatTimeStv.setText("视频通话总时长:" + costInfo.getTotalVideoTime());
    }

    @Override
    public void showUpdateCallback(BaseBean baseBean) {
        Toaster.show(baseBean.getMessage());
        dismissProgressDialog();
    }

    @Override
    public void showWebInfo(WebBeanInfo info) {
        WebViewActivity.startActivity(CostSettingUI.this, "", info.getUrl());
    }

    private void showBottomSignWheel(int type, SuperTextView textView) {
        mSignWheelDatas.clear();

        if (1 == type) {
            for (String value : mCostInfo.getTextFeeList()) {
                mSignWheelDatas.add(new PickerBean(value, value + "金币/条"));
            }
        } else if (2 == type) {
            for (String value : mCostInfo.getVoiceFeeList()) {
                mSignWheelDatas.add(new PickerBean(value, value + "金币/条"));
            }
        } else if (3 == type) {
            for (String value : mCostInfo.getVideoFeeList()) {
                mSignWheelDatas.add(new PickerBean(value, value + "金币/条"));
            }
        }

        if (null == mBottomWheelDialog) {
            mBottomWheelDialog = new BottomSignWheelViewDialog(CostSettingUI.this);
        }

        mBottomWheelDialog.setOnDialogCallbackListener(new BottomSignWheelViewDialog.OnDialogCallbackListener() {
            @Override
            public void onDialogSelectItem(PickerBean info) {
                showProgressDialog(R.string.app_uploadding);
                textView.setRightString(info.getName());
                if (1 == type) {
                    HashMap<String, Object> params = new HashMap<>();
                    params.put("textFee", info.getKey());
                    presenter.updateSettings(params);
                } else if (2 == type) {
                    HashMap<String, Object> params = new HashMap<>();
                    params.put("voiceFee", info.getKey());
                    presenter.updateSettings(params);
                } else if (3 == type) {
                    HashMap<String, Object> params = new HashMap<>();
                    params.put("videoFee", info.getKey());
                    presenter.updateSettings(params);
                }

            }
        });

        mBottomWheelDialog.show();
        mBottomWheelDialog.setNewDatas(mSignWheelDatas);
    }

    @Override
    public boolean onActionBarClickListener(int function) {
        switch (function)
        {
            case CustomActionBar.FUNCTION_TEXT_RIGHT:
                presenter.getAccordByNum(9);
                break;
            case CustomActionBar.FUNCTION_BUTTON_LEFT:
                finish();
                break;
        }
        return false;
    }
}
