package com.chat.laty.activity;

import android.content.Context;
import android.content.Intent;
import android.view.View;

import androidx.viewpager.widget.ViewPager;

import com.chat.laty.R;
import com.chat.laty.adapter.PicBrowseNewAdapter;
import com.chat.laty.base.BaseActivity;
import com.chat.laty.entity.DiaplayPicInfo;
import com.chat.laty.view.CustomActionBar;
import com.chat.laty.view.HackyViewPager;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import cn.xjc_soft.lib_utils.LibCollections;

/**
 * <AUTHOR>
 * @date 2023/2/13.
 * description： 图片集预览
 */
public class DischargedPicBrowserActivity extends BaseActivity {
    @BindView(R.id.activity_pic_browse_viewpager)
    HackyViewPager activityPicBrowseViewpager;

    @BindView(R.id.custom_bar)
    CustomActionBar activityPicBrowseTitleBar;

    private static final String KEY_TO_PIC_LIST = "_KEY_TO_PIC_LIST_";
    private static final String KEY_TO_PIC_POSITION = "_KEY_TO_PIC_POSITION_";
    private PicBrowseNewAdapter mPicBrowseAdapter;
    private List<DiaplayPicInfo> mPicInfos;
    private int mIndex;
    private List<String> mServerUrls;

    public static void start(Context context, ArrayList<DiaplayPicInfo> picAdds, int position) {
        Intent intent = new Intent(context, DischargedPicBrowserActivity.class);
        intent.putExtra(KEY_TO_PIC_LIST, position);
        intent.putExtra(KEY_TO_PIC_POSITION, picAdds);
        context.startActivity(intent);
    }

    @Override
    protected void initDatas() {
        mPicInfos = getIntent().getParcelableArrayListExtra(KEY_TO_PIC_POSITION);
        mIndex = getIntent().getIntExtra(KEY_TO_PIC_LIST, 0);
        mServerUrls = new ArrayList<>();
        mServerUrls.clear();
        for (int i = 0; i < LibCollections.size(mPicInfos); i++) {
            mServerUrls.add(mPicInfos.get(i).getPicUrl());
        }
    }

    @Override
    protected int getContentViewId() {
        return R.layout.act_display_pic_browser;
    }

    @Override
    protected void initViews() {
        mPicBrowseAdapter = new PicBrowseNewAdapter(this, mServerUrls);
        mPicBrowseAdapter.setOnPhotoTapListener((view, x, y) -> toggleFullScreen());
        activityPicBrowseViewpager.setAdapter(mPicBrowseAdapter);
        activityPicBrowseViewpager.setCurrentItem(mIndex);
        activityPicBrowseViewpager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int i, float v, int i1) {
                mIndex = i;
            }

            @Override
            public void onPageSelected(int position) {
                mIndex = position;
            }

            @Override
            public void onPageScrollStateChanged(int i) {
            }
        });
    }

    private void toggleFullScreen() {
        if (activityPicBrowseTitleBar.getVisibility() == View.GONE) {
            activityPicBrowseTitleBar.setVisibility(View.VISIBLE);
        } else {
            activityPicBrowseTitleBar.setVisibility(View.GONE);
        }
    }
}
