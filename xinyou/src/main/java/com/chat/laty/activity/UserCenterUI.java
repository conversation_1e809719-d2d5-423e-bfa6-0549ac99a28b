package com.chat.laty.activity;

import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.appcompat.widget.AppCompatTextView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.hjq.permissions.OnPermissionCallback;
import com.hjq.permissions.Permission;
import com.hjq.permissions.XXPermissions;
import com.hjq.toast.Toaster;
import com.lxj.xpopup.XPopup;
import com.chat.laty.R;
import com.chat.laty.adapter.NineGridViewAdapter;
import com.chat.laty.adapter.UCSiMiAdapter;
import com.chat.laty.base.XYApplication;
import com.chat.laty.controllers.UserInfoController;
import com.chat.laty.dialog.BottomUserOptDialog;
import com.chat.laty.entity.AddressInfo;
import com.chat.laty.entity.BalanceInfo;
import com.chat.laty.entity.BannerInfo;
import com.chat.laty.entity.BaseBean;
import com.chat.laty.entity.DiaplayPicInfo;
import com.chat.laty.entity.LoinEntity;
import com.chat.laty.entity.MediaFileModel;
import com.chat.laty.entity.RYTokenInfo;
import com.chat.laty.entity.RyUserInfo;
import com.chat.laty.entity.SimiInfo;
import com.chat.laty.entity.UserFreeInfo;
import com.chat.laty.entity.XYUserInfo;
import com.chat.laty.mvp.BasePresenterActivity;
import com.chat.laty.presenters.UserInfoPresenter;
import com.chat.laty.utils.BgmPlayer;
import com.chat.laty.utils.ClipboardHelper;
import com.chat.laty.utils.FromJsonUtils;
import com.chat.laty.utils.LogUtil;
import com.chat.laty.utils.PermissionInterceptor;
import com.chat.laty.utils.PicassoUtils;
import com.chat.laty.utils.ResultData;
import com.chat.laty.utils.XYSPUtils;
import com.chat.laty.utils.net.Common;
import com.chat.laty.utils.net.HttpUtils;
import com.chat.laty.utils.net.TextCallBack;
import com.chat.laty.view.CustomActionBar;
import com.chat.laty.view.CustomAttachPopup;
import com.chat.laty.view.LineBreak;
import com.youth.banner.Banner;
import com.youth.banner.listener.OnBannerListener;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;
import cn.rongcloud.callplus.api.RCCallPlusClient;
import cn.rongcloud.callplus.api.RCCallPlusSession;
import io.rong.imkit.config.RongConfigCenter;
import io.rong.imkit.utils.RouteUtils;
import io.rong.imlib.IRongCoreEnum;
import io.rong.imlib.RongIMClient;
import io.rong.imlib.model.Conversation;

/**
 * <AUTHOR>
 * @date 2023/12/20 17:26
 * @description:
 */
public class UserCenterUI extends BasePresenterActivity<UserInfoPresenter> implements UserInfoController.View, CustomActionBar.OnActionBarClickListerner, OnBannerListener {
    public static final String KEY_USER_ID = "KEY_USER_ID";

    @BindView(R.id.banner)
    Banner mBannerView;

    @BindView(R.id.custom_bar)
    CustomActionBar mActionBar;

    List<BannerInfo> mBanners = new ArrayList<>();
    List<String> mSimiList = new ArrayList<>();
    List<String> mSimiList1 = new ArrayList<>();

    private String mUserId;
    @BindView(R.id.nine_grid)
    RecyclerView userIconRv;
    @BindView(R.id.user_name_tv)
    AppCompatTextView userNameTv;
    @BindView(R.id.user_id_tv)
    AppCompatTextView userIdTv;
    @BindView(R.id.user_age_tv)
    AppCompatTextView userAgeTv;
    @BindView(R.id.user_isreal_tv)
    TextView userIsrealTv;
    @BindView(R.id.user_isname_tv)
    View userIsnameTv;
    @BindView(R.id.user_isphone_tv)
    TextView userIsphoneTv;
    @BindView(R.id.user_caifu_tv)
    AppCompatTextView userCaifuTv;
    @BindView(R.id.makefriends_detail_tv)
    AppCompatTextView makefriendsDetailTv;
    @BindView(R.id.user_age_tv2)
    AppCompatTextView userAgeTv2;
    @BindView(R.id.user_weight_tv)
    AppCompatTextView userWeightTv;
    @BindView(R.id.user_education_tv)
    AppCompatTextView userEducationTv;
    @BindView(R.id.user_income_tv)
    AppCompatTextView userIncomeTv;
    @BindView(R.id.user_height_tv)
    AppCompatTextView userHeightTv;
    @BindView(R.id.user_city_tv)
    AppCompatTextView userCityTv;
    @BindView(R.id.user_career_tv)
    AppCompatTextView userCareerTv;

    @BindView(R.id.friendly_voice_tv)
    AppCompatTextView friendlyVoiceTv;
    @BindView(R.id.impression_lb)
    LineBreak impressionLB;
    @BindView(R.id.love_friend_lb)
    LineBreak loveFriendLB;
    @BindView(R.id.online_view)
    LinearLayout olineView;

    @BindView(R.id.bottom_layout)
    LinearLayout mBottomChatLl;

    @BindView(R.id.user_caifu_layout)
    LinearLayout oCaiFuView;

    @BindView(R.id.follow_tv)
    AppCompatTextView followTv;

    @BindView(R.id.vip_tv)
    ImageView mVipTv;

    @BindView(R.id.user_info_stv)
    TextView mEditUserInfoStv;

    @BindView(R.id.age_cardview)
    LinearLayout mAgeCardView;

    @BindView(R.id.weight_cardview)
    LinearLayout mWeightCardView;

    @BindView(R.id.education_cardview)
    LinearLayout mEducationCardView;

    @BindView(R.id.income_cardview)
    LinearLayout mIncomeCardView;

    @BindView(R.id.height_cardview)
    LinearLayout mHeightCardView;

    @BindView(R.id.city_cardview)
    LinearLayout mCityCardView;

    @BindView(R.id.career_cardview)
    LinearLayout mCareerCardView;

    @BindView(R.id.ll_ismi)
    View ll_ismi;

    @BindView(R.id.ll_ismi_video)
    View ll_ismi_video;
    @BindView(R.id.iv_ind1)
    View iv_ind1;

    @BindView(R.id.iv_ind2)
    View iv_ind2;

    @BindView(R.id.user_photo_rv)
    RecyclerView user_photo_rv;

    @BindView(R.id.user_photo_rv_video)
    RecyclerView user_photo_rv_video;

    @BindView(R.id.cl_simi)
    ConstraintLayout clSimi;
    @BindView(R.id.cl_pv_tab)
    ConstraintLayout cl_pv_tab;

    @BindView(R.id.cl_simi_video)
    ConstraintLayout clSimiVideo;

    @BindView(R.id.chat_tv)
    View chat_tv;

    @BindView(R.id.call_yuyin)
    View call_yuyin;

    @BindView(R.id.call_video)
    View call_video;

    @BindView(R.id.textView4_photo)
    TextView textView4Photo;

    @BindView(R.id.textView4_video)
    TextView textView4Video;
    @BindView(R.id.tv_sex)
    TextView tvSex;

    @BindView(R.id.user_riv)
    ImageView userRiv;

    XYUserInfo mUserInfo;
//    RyUserInfo mMyInfo;

    BottomUserOptDialog mChoiceDialog;
    private BgmPlayer mBgmPlayer;
    private int mUserBlackStatus;
    RyUserInfo mCurrentUser;
    private int mCurrentCallPhoneType = -1;

    UserFreeInfo mUserFreeInfo;

    UCSiMiAdapter mSimiAdapter;
    UCSiMiAdapter mSimiAdapter1;

    public static void start(Context context, String userId) {
        Intent intent = new Intent(context, UserCenterUI.class);
        intent.putExtra(KEY_USER_ID, userId);
        context.startActivity(intent);
    }

    @Override
    protected void initDatas() {

    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_user_center_activity;
    }

    @Override
    protected void initViews() {
        mCurrentUser = XYApplication.getCurrentUserInfo();
        mUserId = getIntent().getStringExtra(KEY_USER_ID);
        presenter.getUserFee(mUserId);
        presenter.addVisit(mUserId);

        String userId = XYSPUtils.getString(Common.KEY_APP_USER_RY_ID);
        mBottomChatLl.setVisibility(TextUtils.equals(userId, mUserId) ? View.GONE : View.VISIBLE);

        mActionBar.addFunction(CustomActionBar.FUNCTION_TEXT_RIGHT);
        mActionBar.setRightTextColor(getResources().getColor(R.color.color_FF6D7A));
        if (TextUtils.equals(userId, mUserId)) {
            mActionBar.setRightText("编辑");
        } else {
//            mEditUserInfoStv.setRightString("");
//            mEditUserInfoStv.setRightIcon(0);
            mActionBar.setRightText("更多");
            RongIMClient.getInstance().getBlacklistStatus(mUserId, new RongIMClient.ResultCallback<RongIMClient.BlacklistStatus>() {
                @Override
                public void onSuccess(RongIMClient.BlacklistStatus blacklistStatus) {
                    mUserBlackStatus = blacklistStatus.getValue();
                }

                @Override
                public void onError(RongIMClient.ErrorCode e) {

                }
            });

        }

        mActionBar.setOnActionBarClickListerner(this);

        //青少年模式关闭私信功能
        if (getStatusFromCache()) {
            chat_tv.setVisibility(View.GONE);
            call_video.setVisibility(View.GONE);
            call_yuyin.setVisibility(View.GONE);
        }
    }

    @OnClick({R.id.chat_tv, R.id.call_yuyin, R.id.call_video, R.id.follow_tv, R.id.friendly_voice_tv,
            R.id.user_city_tv, R.id.user_info_stv, R.id.user_dynamics_stv, R.id.copy_user_id, R.id.usericon_rv,
            R.id.ll_ismi, R.id.ll_ismi_video, R.id.textView4_video, R.id.textView4_photo, R.id.iv_vip})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.textView4_video:
                iv_ind1.setVisibility(View.GONE);
                iv_ind2.setVisibility(View.VISIBLE);
                clSimi.setVisibility(View.GONE);
                clSimiVideo.setVisibility(View.VISIBLE);
                break;
            case R.id.textView4_photo:
                iv_ind1.setVisibility(View.VISIBLE);
                iv_ind2.setVisibility(View.GONE);
                clSimi.setVisibility(View.VISIBLE);
                clSimiVideo.setVisibility(View.GONE);
                break;
            case R.id.ll_ismi_video:
                //开通vip
                VipCenter1Activity.startAct(this);
                break;
            case R.id.ll_ismi:
                //开通vip
                VipCenter1Activity.startAct(this);
                break;
            case R.id.iv_vip:
                //开通vip
                VipCenter1Activity.startAct(this);
                break;
            case R.id.follow_tv:
                presenter.followUser(mUserId,  mUserInfo.getIsFollow().equals("1"));
                break;
            case R.id.user_info_stv:
                String userId = XYSPUtils.getString(Common.KEY_APP_USER_RY_ID);
                if (TextUtils.equals(userId, mUserId)) {
                    ImproveInformationUI.start(UserCenterUI.this);
                }
                break;

            case R.id.friendly_voice_tv:
                if (null == mBgmPlayer) {
                    mBgmPlayer = BgmPlayer.getInstance(UserCenterUI.this);
                }

                if (mBgmPlayer.isPlay()) {
                    mBgmPlayer.stop();
                }
                mBgmPlayer.playNet(mUserInfo.getAudioUrl(), friendlyVoiceTv);
                break;

            case R.id.chat_tv:
                // 默认拉取历史消息数量
                RongConfigCenter.conversationConfig().setConversationHistoryMessageCount(10);
                if (XYApplication.getCurrentUserStatus()) {
                    RouteUtils.routeToConversationActivity(UserCenterUI.this, Conversation.ConversationType.PRIVATE, mUserId, null);
                }
                break;

            case R.id.user_dynamics_stv:
            case R.id.usericon_rv:
                UserDynamicsActivity.start(UserCenterUI.this, mUserId);
                break;

            case R.id.copy_user_id:
                if (XYApplication.appLogin(true)) {
                    new ClipboardHelper(UserCenterUI.this).copyText(mUserId);
                    Toaster.show("已成功复制到粘贴板");
                }
                break;

            case R.id.user_city_tv:
                new XPopup.Builder(UserCenterUI.this)
                        .isDestroyOnDismiss(true) //对于只使用一次的弹窗，推荐设置这个
//                        .offsetX(50) //偏移10
//                        .offsetY(10)  //往下偏移10
//                        .popupPosition(PopupPosition.Right) //手动指定位置，有可能被遮盖
                        .hasShadowBg(false) // 去掉半透明背景
                        .atView(userCityTv)
                        .asCustom(new CustomAttachPopup(UserCenterUI.this, userCityTv.getText().toString()))
                        .show();
                break;

            case R.id.call_yuyin:
                XXPermissions.with(UserCenterUI.this).permission(Permission.RECORD_AUDIO).interceptor(new PermissionInterceptor()).request(new OnPermissionCallback() {
                    @Override
                    public void onGranted(List<String> permissions, boolean all) {
                        if (!all) {
                            return;
                        }
                        mCurrentCallPhoneType = 0;
                        presenter.getUserInfo(mUserId);

                    }
                });

                break;

            case R.id.call_video:

                XXPermissions.with(UserCenterUI.this).permission(Permission.RECORD_AUDIO).permission(Permission.CAMERA).interceptor(new PermissionInterceptor()).request(new OnPermissionCallback() {
                    @Override
                    public void onGranted(List<String> permissions, boolean all) {
                        if (!all) {
                            return;
                        }
                        mCurrentCallPhoneType = 1;
                        presenter.getUserInfo(mUserId);

                    }
                });
                break;
        }
    }

    @Override
    protected UserInfoPresenter setPresenter() {
        return new UserInfoPresenter(this);
    }

    @Override
    public void showUploadResult(List<String> result) {

    }

    @Override
    public void updateCallback(BaseBean callbackBean) {

    }

    @Override
    public void showUserInfo(RyUserInfo data) {

    }

    @Override
    public void complementCallback(LoinEntity data) {

    }

    @Override
    public void showUserDetails(XYUserInfo userInfo) {
        if (null != userInfo) {
            HashMap<String, Object> params = new HashMap<>();
            params.put("userId", userInfo.getUserId());
            HttpUtils.get(Common.GET_SIMI_USER, params, new TextCallBack() {
                @Override
                protected void onSuccess(String text) {
                    LogUtil.e("响应结果", "==========" + text);
                    ResultData<SimiInfo> mData = FromJsonUtils.fromJson(text, SimiInfo.class);
                    if (200 != mData.getCode()) {
                        Toaster.show(mData.getMessage());
                    }
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            SimiInfo simiInfo = mData.getData();
                            userAgeTv.setText(simiInfo.getShengri().equals("0") ? userInfo.getAge() : "保密");
                            userAgeTv2.setText(simiInfo.getShengri().equals("0") ? userInfo.getAge() : "保密");
                            if (!TextUtils.isEmpty(userInfo.getHeight()))
                                userHeightTv.setText(simiInfo.getShengao().equals("0") ? userInfo.getHeight() : "保密");
                            if (!TextUtils.isEmpty(userInfo.getWeight()))
                                userWeightTv.setText(simiInfo.getTizhong().equals("0") ? userInfo.getWeight() : "保密");
                            if (!TextUtils.isEmpty(userInfo.getEducationName()))
                                userCareerTv.setText(simiInfo.getXueli().equals("0") ? userInfo.getEducationName() : "保密");
                            if (!TextUtils.isEmpty(userInfo.getEducationName()))
                                userEducationTv.setText(simiInfo.getZhiye().equals("0") ? userInfo.getCareerName() : "保密");
                            if (!TextUtils.isEmpty(userInfo.getLiveAddress()))
                                userCityTv.setText(simiInfo.getDizhi().equals("0") ? userInfo.getLiveAddress() : "保密");
                            if (!TextUtils.isEmpty(userInfo.getEducationName()))
                                userIncomeTv.setText(simiInfo.getNianshouru().equals("0") ? userInfo.getIncomeName() : "保密");

                        }
                    });
                }

                @Override
                protected void onFailure(ResponseException e) {
                    LogUtil.e("响应结果", "==========" + e.toString());
                }
            });

            mUserInfo = userInfo;
            userNameTv.setText(userInfo.getNickname());
            userIdTv.setText("ID:" + userInfo.getUserId());
            PicassoUtils.showImage(userRiv, userInfo.getAvatar());

            mAgeCardView.setVisibility((!TextUtils.isEmpty(userInfo.getAge()) && !TextUtils.equals("0", userInfo.getAge())) ? View.VISIBLE : View.GONE);
            mWeightCardView.setVisibility((!TextUtils.isEmpty(userInfo.getWeight()) && !TextUtils.equals("0", userInfo.getWeight())) ? View.VISIBLE : View.GONE);
            mHeightCardView.setVisibility((!TextUtils.isEmpty(userInfo.getHeight()) && !TextUtils.equals("0", userInfo.getHeight())) ? View.VISIBLE : View.GONE);
            mEducationCardView.setVisibility(!TextUtils.isEmpty(userInfo.getEducationName()) ? View.VISIBLE : View.GONE);
            mIncomeCardView.setVisibility(!TextUtils.isEmpty(userInfo.getIncomeName()) ? View.VISIBLE : View.GONE);
            mCityCardView.setVisibility(!TextUtils.isEmpty(userInfo.getLiveAddress()) ? View.VISIBLE : View.GONE);
            mCareerCardView.setVisibility(!TextUtils.isEmpty(userInfo.getCareerName()) ? View.VISIBLE : View.GONE);


            if (!TextUtils.isEmpty(userInfo.getGoldNum()))
                userCaifuTv.setText(userInfo.getGoldNum());
            if (TextUtils.isEmpty(userInfo.getAudioUrl())) {
                friendlyVoiceTv.setVisibility(View.GONE);
            } else {
                friendlyVoiceTv.setText(userInfo.getAudioLength() + "s");
                friendlyVoiceTv.setVisibility(View.VISIBLE);
            }
//            UserIconAdapter iconAdapter = new UserIconAdapter();
//            userIconRv.setAdapter(iconAdapter);
//            iconAdapter.setItems(Arrays.asList(userInfo.getTrendsFiles().split(",")));
            if (TextUtils.isEmpty(userInfo.getTrendsFiles())) {
                userIconRv.removeAllViews();
                userIconRv.setAdapter(null);
            } else {
                final List<MediaFileModel> li = new ArrayList<>();

                if (TextUtils.equals("2", userInfo.getTrendsType())) {
                    li.add(new MediaFileModel(userInfo.getTrendsFiles(), userInfo.getTrendsFiles()));
                } else {
                    List<String> dys = Arrays.asList(userInfo.getTrendsFiles().split(","));
                    for (String dyTemp : dys) {
                        li.add(new MediaFileModel(dyTemp, dyTemp));
                    }
                }
                GridLayoutManager manager = new GridLayoutManager(this, li.size() > 2 ? 3 : li.size());
                userIconRv.setLayoutManager(manager);
                userIconRv.setNestedScrollingEnabled(false);

                NineGridViewAdapter nineGridViewAdapter = new NineGridViewAdapter(this);
                nineGridViewAdapter.setOnItemClickListener((baseQuickAdapter, view, i) -> {
//                MediaFileModel model = li.get(i);
                    UserDynamicsActivity.start(UserCenterUI.this, mUserId);
                });
                nineGridViewAdapter.setList(li);
                userIconRv.setAdapter(nineGridViewAdapter);
            }

            if (!TextUtils.isEmpty(userInfo.getImpressionLabels()))
                impressionLB.setLabels(Arrays.asList(userInfo.getImpressionLabels().split(",")));
            if (!TextUtils.isEmpty(userInfo.getLoveFriendLabels()))
                loveFriendLB.setLabels(Arrays.asList(userInfo.getLoveFriendLabels().split(",")));

//            userIsrealTv.setVisibility((!TextUtils.isEmpty(userInfo.getIsReal()) && "1".equals(userInfo.getIsReal())) ? View.VISIBLE : View.GONE);
            userIsnameTv.setVisibility((!TextUtils.isEmpty(userInfo.getIsName()) && "1".equals(userInfo.getIsName())) ? View.VISIBLE : View.GONE);
//            userIsphoneTv.setVisibility((!TextUtils.isEmpty(userInfo.getIsPhone()) && "1".equals(userInfo.getIsPhone())) ? View.VISIBLE : View.GONE);

            olineView.setVisibility((!TextUtils.isEmpty(userInfo.getOnlineStatus()) && "1".equals(userInfo.getOnlineStatus())) ? View.VISIBLE : View.GONE);
            if (userInfo.getVipLevel() == 0) {
                mVipTv.setVisibility(View.GONE);
                userNameTv.setTextColor(Color.parseColor("#333333"));
            } else {
                mVipTv.setVisibility(View.VISIBLE);
                Glide.with(this).load(XYApplication.vipTypeModelList.get(userInfo.getVipLevel() - 1).getExclusiveIdentifier()).into(mVipTv);
                userNameTv.setTextColor(Color.parseColor(XYApplication.vipTypeModelList.get(userInfo.getVipLevel() - 1).getExclusiveNameColor()));
            }

            if (!XYApplication.getCurrentIsMan()) {
                cl_pv_tab.setVisibility(View.GONE);
                clSimi.setVisibility(View.GONE);
                clSimiVideo.setVisibility(View.GONE);
            }

            if (TextUtils.equals("2", userInfo.getSex())) {
                userIsphoneTv.setVisibility(View.GONE);
                oCaiFuView.setVisibility(View.GONE);
                tvSex.setText("男");
            } else {

                tvSex.setText("女");

                userIsrealTv.setText((!TextUtils.isEmpty(userInfo.getIsReal()) && "1".equals(userInfo.getIsReal())) ? "已认证" : "未认证");
//                userIsnameTv.setText((!TextUtils.isEmpty(userInfo.getIsName()) && "1".equals(userInfo.getIsName())) ? "已实名" : "未实名");
//                userIsphoneTv.setVisibility(View.VISIBLE);
//                oCaiFuView.setVisibility(View.VISIBLE);

                if ("1".equals(userInfo.getIsPhone())) {
                    userIsphoneTv.setText("手机已认证");
                } else if ("0".equals(userInfo.getIsPhone())) {
                    userIsphoneTv.setText("微信已认证");
                }
            }

            setSex(userInfo);
            makefriendsDetailTv.setText(userInfo.getMakeFriendsDetail());
            userEducationTv.setText(userInfo.getEducationName());
            if (!TextUtils.isEmpty(userInfo.getImpressionLabels()))
                impressionLB.setLabels(Arrays.asList(userInfo.getImpressionLabels().split(",")));
            if (!TextUtils.isEmpty(userInfo.getLoveFriendLabels()))
                loveFriendLB.setLabels(Arrays.asList(userInfo.getLoveFriendLabels().split(",")));

            setFollow(mUserInfo);

//            if (!TextUtils.isEmpty(userInfo.getPhotoList())) {
//                mBanners.clear();
//                for (String s : userInfo.getPhotoList().split(",")) {
//                    mBanners.add(new BannerInfo(s));
//                }
//            }

//            mBannerView.setAdapter(new UserCenterImageLoader(mBanners));
//            mBannerView.setIndicator(new CircleIndicator(this));
//            mBannerView.setOnBannerListener(this);
//            mBannerView.start();

            mSimiList.clear();
            mSimiList.addAll(Arrays.asList(userInfo.getPrivateAlbums().split(",")));
            mSimiList.removeAll(Collections.singleton(""));
            if (!TextUtils.equals(XYSPUtils.getString(Common.KEY_APP_USER_RY_ID), mUserId)) {
                if (mCurrentUser.getVipLevel() != 0 && XYApplication.vipTypeModelList.get(mCurrentUser.getVipLevel() - 1).getIsPrivateAlbum() == 1) {
                    ll_ismi.setVisibility(View.GONE);
                } else {
                    ll_ismi.setVisibility(View.VISIBLE);
                }
            } else {
                ll_ismi.setVisibility(View.GONE);
            }
            mSimiAdapter = new UCSiMiAdapter();
            mSimiAdapter.setItems(mSimiList);
            mSimiAdapter.setOnItemClickListener((baseQuickAdapter, view, i) -> {
                ArrayList<DiaplayPicInfo> mDiaplayPicInfos = new ArrayList<>();
                for (String s : mSimiList) {
                    mDiaplayPicInfos.add(new DiaplayPicInfo(s));
                }
                DischargedPicBrowserActivity.start(UserCenterUI.this, mDiaplayPicInfos, i);
            });

            user_photo_rv.setVisibility(View.VISIBLE);
            user_photo_rv.setAdapter(mSimiAdapter);
            user_photo_rv.setLayoutManager(new LinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false));
            mSimiList1.clear();
            mSimiList1.addAll(Arrays.asList(userInfo.getPrivateVideos().split(",")));
            //去除空元素
            mSimiList1.removeAll(Collections.singleton(""));
            if (!TextUtils.equals(XYSPUtils.getString(Common.KEY_APP_USER_RY_ID), mUserId)) {
                if (mCurrentUser.getVipLevel() != 0 && XYApplication.vipTypeModelList.get(mCurrentUser.getVipLevel() - 1).getIsPrivateVideo() == 1) {
                    ll_ismi_video.setVisibility(View.GONE);
                } else {
                    ll_ismi_video.setVisibility(View.VISIBLE);
                }
            } else {
                ll_ismi_video.setVisibility(View.GONE);
            }
            mSimiAdapter1 = new UCSiMiAdapter();
            mSimiAdapter1.setItems(mSimiList1);
            mSimiAdapter1.setOnItemClickListener((baseQuickAdapter, view, position) -> {
                VideoPlayActivity.start(this, mSimiList1.get(position));
            });
            user_photo_rv_video.setVisibility(View.VISIBLE);
            user_photo_rv_video.setAdapter(mSimiAdapter1);
            user_photo_rv_video.setLayoutManager(new LinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false));


            if (mCurrentCallPhoneType != -1) {
                if (XYApplication.getCurrentUserStatus()) {
                    if (Integer.valueOf(mUserInfo.getSysMindNum()) <= Integer.valueOf(mUserInfo.getMindNum())) {
                        if (XYApplication.getCurrentIsMan()) {//如果我是男用户,判断我的金币
                            if (Common.VIP_CALL_PERMISSION) {
                                if (mUserInfo.getVipLevel() == 0 || XYApplication.vipTypeModelList.get(mUserInfo.getVipLevel() - 1).getIsCallPermission() == 0) {
                                    Toaster.show("您当前没有通话权限，可开通VIP进行开启");
                                    return;
                                }
                            }

                            BalanceInfo balanceInfo = XYApplication.getCurrentUserBalance();
                            if (mCurrentCallPhoneType == 0 && balanceInfo.getFreeVoiceNum() < 1 && balanceInfo.getGoldNum() < Double.valueOf(mUserFreeInfo.getVoiceFee())) {
                                Toaster.show("金币不足,无法语音");
                                return;
                            }

                            if (mCurrentCallPhoneType == 1 && (balanceInfo.getFreeVideoNum() < 1 && balanceInfo.getGoldNum() < Double.valueOf(mUserFreeInfo.getVideoFee()))) {
                                Toaster.show("金币不足,无法视频");
                                return;
                            }
                        }else{

                        }

                        RCCallPlusSession currentCallSession = RCCallPlusClient.getInstance().getCurrentCallSession();
                        if (currentCallSession != null) {
                            Toaster.show("当前正在通话，不允许拨打");
                            return;
                        }

                        presenter.getUserVideoStatus(mUserInfo.getUserId(), mUserId, mCurrentCallPhoneType, UserCenterUI.this);
                        mCurrentCallPhoneType = -1;
                    } else {
                        Toaster.show("亲密度不足，未解锁该功能");
                    }
                }
            }
        }
    }

    private void setSex(XYUserInfo userInfo) {
        Drawable dwLeft = getResources().getDrawable(TextUtils.equals("1", userInfo.getSex()) ? R.mipmap.icon_nan_select : R.mipmap.icon_nv_select);
        dwLeft.setBounds(0, 0, dwLeft.getMinimumWidth(), dwLeft.getMinimumHeight());
        userAgeTv.setBackground(getResources().getDrawable("1".equals(userInfo.getSex()) ? R.drawable.bg_border_5595ff_40 : R.drawable.badge_ff8c8c_to_ff5562_bg_40));
        userAgeTv.setCompoundDrawables(dwLeft, null, null, null);
    }

    private void setFollow(XYUserInfo userInfo) {
        Drawable dwLeft = getResources().getDrawable(R.mipmap.jia);
        dwLeft.setBounds(0, 0, dwLeft.getMinimumWidth(), dwLeft.getMinimumHeight());
        followTv.setCompoundDrawables("1".equals(userInfo.getIsFollow()) ? null : dwLeft, null, null, null);
        followTv.setBackground(getResources().getDrawable("1".equals(userInfo.getIsFollow()) ? R.drawable.badge_ffecee_bg_line_ff6d7a_40 : R.drawable.badge_ff8c8c_to_ff5562_bg_40));
        followTv.setText("1".equals(userInfo.getIsFollow()) ? "取消关注" : "关注");
        followTv.setTextColor(getResources().getColor("1".equals(userInfo.getIsFollow()) ? R.color.color_FF6D7A : R.color.color_white));
    }

    @Override
    public void showRyToken(RYTokenInfo data) {

    }

    @Override
    public void showFollwoCallback() {
        String tempFollow = mUserInfo.getIsFollow();
        mUserInfo.setIsFollow("1".equals(tempFollow) ? "0" : "1");
        setFollow(mUserInfo);
    }

    @Override
    public void showAddress(List<AddressInfo> addressInfos) {

    }

    @Override
    public void showLoginImCallback() {

    }

    @Override
    public void showUserFreeInfo(UserFreeInfo userFreeInfo) {
        mUserFreeInfo = userFreeInfo;
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (null != mBgmPlayer)
            mBgmPlayer.stop();
    }

    @Override
    public FragmentActivity context() {
        return this;
    }

    @Override
    public boolean onActionBarClickListener(int function) {
        switch (function) {
            case CustomActionBar.FUNCTION_BUTTON_LEFT:
                finish();
                break;

            case CustomActionBar.FUNCTION_TEXT_RIGHT:
                String userId = XYSPUtils.getString(Common.KEY_APP_USER_RY_ID);
                if (TextUtils.equals(userId, mUserId)) {
                    ImproveInformationUI.start(UserCenterUI.this);
                } else {
                    showMsgOptDialog();
                }
                break;
        }
        return false;
    }

    private void showMsgOptDialog() {

        if (null == mChoiceDialog)
            mChoiceDialog = new BottomUserOptDialog(UserCenterUI.this);
        mChoiceDialog.setOnDialogCallbackListener(new BottomUserOptDialog.OnDialogCallbackListener() {
            @Override
            public void onDialogSelectItem(int type) {
                optUser(type);
            }
        });
        mChoiceDialog.show();
        mChoiceDialog.hideEditNickName(true);
        mChoiceDialog.setUserBlack(mUserBlackStatus);
    }

    private void optUser(int type) {
        switch (type) {
            case 1:
                break;

            case 2:
                if (mUserBlackStatus == IRongCoreEnum.BlacklistStatus.IN_BLACK_LIST.getValue())
                    RongIMClient.getInstance().removeFromBlacklist(mUserId, blackCallback);
                else
                    RongIMClient.getInstance().addToBlacklist(mUserId, blackCallback);
                break;

            case 3:
                ReportUserActivity.start(UserCenterUI.this, mUserId);
                break;
        }
    }

    RongIMClient.OperationCallback blackCallback = new RongIMClient.OperationCallback() {
        @Override
        public void onSuccess() {
            if (mUserBlackStatus == IRongCoreEnum.BlacklistStatus.IN_BLACK_LIST.getValue()) {
                Toaster.show("已移除黑名单");
                mUserBlackStatus = IRongCoreEnum.BlacklistStatus.NOT_IN_BLACK_LIST.getValue();
            } else {
                Toaster.show("已加入黑名单");
                mUserBlackStatus = IRongCoreEnum.BlacklistStatus.IN_BLACK_LIST.getValue();
            }
        }

        @Override
        public void onError(RongIMClient.ErrorCode errorCode) {

        }
    };

    @Override
    public void OnBannerClick(Object data, int position) {
        ArrayList<DiaplayPicInfo> mDiaplayPicInfos = new ArrayList<>();
        for (String s : mUserInfo.getPhotoList().split(",")) {
            mDiaplayPicInfos.add(new DiaplayPicInfo(s));
        }

        DischargedPicBrowserActivity.start(UserCenterUI.this, mDiaplayPicInfos, position);

    }

    @Override
    protected void onResume() {
        super.onResume();
        presenter.getUserBalance();
        presenter.getUserFee(mUserId);
        presenter.getUserInfo(mUserId);
    }

    /**
     * 从缓存中获取状态
     *
     * @return
     */
    private boolean getStatusFromCache() {
        SharedPreferences sharedPreferences = getSharedPreferences("app_prefs", Context.MODE_PRIVATE);
        return sharedPreferences.getBoolean("qsnms_status", false); // 默认值为 false
    }
}
