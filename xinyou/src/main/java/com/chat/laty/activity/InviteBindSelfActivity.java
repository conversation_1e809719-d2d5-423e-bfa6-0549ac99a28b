package com.chat.laty.activity;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.util.Log;
import android.view.View;
import android.widget.EditText;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.RecyclerView;

import com.hjq.toast.Toaster;
import com.luck.picture.lib.basic.PictureSelector;
import com.luck.picture.lib.config.PictureConfig;
import com.luck.picture.lib.entity.LocalMedia;
import com.otaliastudios.transcoder.Transcoder;
import com.otaliastudios.transcoder.TranscoderListener;
import com.chat.laty.R;
import com.chat.laty.adapter.MediaPickerAdapter;
import com.chat.laty.controllers.DynamicController;
import com.chat.laty.controllers.InviteController;
import com.chat.laty.entity.MediaItemModel;
import com.chat.laty.entity.MediaItemStatus;
import com.chat.laty.entity.UploadImgInfo;
import com.chat.laty.entity.event.InviteEvent;
import com.chat.laty.mvp.BasePresenterActivity;
import com.chat.laty.presenters.DynamicPresenter;
import com.chat.laty.presenters.InvitePresenter;
import com.chat.laty.view.CustomActionBar;

import org.greenrobot.eventbus.EventBus;

import java.io.File;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import butterknife.BindView;
import butterknife.OnClick;

public class InviteBindSelfActivity extends BasePresenterActivity<InvitePresenter> implements InviteController.IBindSelfView, DynamicController.UploadView {

    public static void startAct(Context context) {
        Intent intent = new Intent(context, InviteBindSelfActivity.class);
        context.startActivity(intent);
    }

    @BindView(R.id.custom_bar)
    CustomActionBar customBar;

    @BindView(R.id.et_userId)
    EditText etUserId;
    @BindView(R.id.et_note)
    EditText etNote;

    @BindView(R.id.recycler_view)
    RecyclerView recyclerView;

    MediaPickerAdapter adapter;
    private DynamicPresenter uploadPresenter;

    @Override
    protected void initDatas() {
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_bind_self_layout;
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        for (String url : urls) {
            File file = new File(url);
            if (file.exists()) {
                Log.e(TAG, "onDestroy: " + file.length());
                file.delete();
            }
        }
    }

    @Override
    protected void initViews() {
        customBar.setTitleText("自主绑定");
        customBar.setRightText("绑定记录");
        customBar.setAllColor(ContextCompat.getColor(this, R.color.black));

        customBar.addFunction(CustomActionBar.FUNCTION_TEXT_RIGHT);
        customBar.setOnActionBarClickListerner(function -> {
            if (function == CustomActionBar.FUNCTION_BUTTON_LEFT) {

                InviteBindSelfActivity.this.finish();
                return true;
            } else if (function == CustomActionBar.FUNCTION_TEXT_RIGHT) {
                InviteBindRecordsActivity.startAct(this);
                return true;
            }
            return false;
        });

        adapter = new MediaPickerAdapter(this, true);
        recyclerView.setAdapter(adapter);
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, @Nullable @org.jetbrains.annotations.Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK) {
            switch (requestCode) {
                case PictureConfig.CHOOSE_REQUEST:
                    List<LocalMedia> list = PictureSelector.obtainSelectorList(data);
                    List<MediaItemModel> selected = new ArrayList<>();
                    for (LocalMedia item : list) {
                        MediaItemModel model = new MediaItemModel(item.getRealPath(), item.getMimeType());
                        model.status = MediaItemStatus.uploading;
                        selected.add(model);
                        if (item.getMimeType().contains("image")) {
                            uploadPresenter.uploadFiles(item);
                        } else {
                            compress(item, model);
                        }
                    }
                    if (selected.isEmpty()) return;
                    adapter.setList(selected);
                    break;
            }
        }
    }

    @Override
    protected InvitePresenter setPresenter() {
        uploadPresenter = new DynamicPresenter(this, null);
        InvitePresenter presenter = new InvitePresenter(null);
        presenter.addView(this);
        return presenter;
    }


    @Override
    public FragmentActivity context() {
        return this;
    }

    private final Set<String> urls = new HashSet<>();

    @SuppressLint("NotifyDataSetChanged")
    private void compress(LocalMedia item, MediaItemModel model) {

        File origin = new File(item.getRealPath());
        if (origin.exists() && origin.isFile()) {
            Log.e(TAG, "Origin compress: ==>" + origin.length());
            if (origin.length() > MineDynamicReleaseUI.maxLength) {
                model.status = MediaItemStatus.fail;
                adapter.notifyDataSetChanged();
                Toaster.show("文件过大，请重新选择～");
                return;
            }
            if (origin.length() <= 5 * 1024 * 1024) {
                model.status = MediaItemStatus.uploading;
                adapter.notifyDataSetChanged();
                uploadPresenter.uploadFiles(item);
                return;
            }
        }

        // https://github.com/yellowcath/VideoProcessor
        String dir = getCacheDir().getAbsolutePath() + "/video/";
        File filePath = new File(dir);
        if (!filePath.exists()) {
            filePath.mkdir();
        }
        String fileName = System.currentTimeMillis() + ".mp4";
        File target = new File(filePath, fileName);

        model.status = MediaItemStatus.compress;
        adapter.notifyDataSetChanged();

        Toaster.show("正在压缩资源～");
        Transcoder.into(target.getAbsolutePath())
                .addDataSource(item.getRealPath()) // or
                // ...
                .setListener(new TranscoderListener() {
                    public void onTranscodeProgress(double progress) {
                        Log.e(TAG, "onTranscodeProgress: " + progress);
                    }

                    public void onTranscodeCompleted(int successCode) {
                        Log.e(TAG, "onTranscodeCompleted: " + target.length());
                        model.status = MediaItemStatus.uploading;
                        adapter.notifyDataSetChanged();
                        item.setCompressPath(target.getAbsolutePath());
                        uploadPresenter.uploadFiles(item);
                        urls.add(item.getCompressPath());
                    }

                    public void onTranscodeCanceled() {

                    }

                    public void onTranscodeFailed(@NonNull Throwable exception) {
                        Log.e(TAG, "onTranscodeFailed: " + exception);
                        model.status = MediaItemStatus.fail;
                        adapter.notifyDataSetChanged();
                    }
                }).transcode();
    }

    @SuppressLint("NonConstantResourceId")
    @OnClick({R.id.btn_confirm})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.btn_confirm:

                String userId = etUserId.getText().toString().trim();
                if (userId.isEmpty()) {
                    Toaster.show("请输入用户ID");
                    return;
                }

                StringBuilder urls = new StringBuilder();

                List<MediaItemModel> list = adapter.getList();

                if (list.isEmpty()) {
                    Toaster.show("请上传图片/视频");
                    return;
                }
                boolean data = false;

                for (MediaItemModel item : list) {
                    if (item.status == MediaItemStatus.uploading) {
                        Toaster.show("图片/视频正在上传，请稍等～");
                        return;
                    } else if (item.status == MediaItemStatus.succeed) {
                        data = true;
                    }
                    if (urls.length() > 0) {
                        urls.append(",");
                    }
                    urls.append(item.url);
                }
                if (!data) {
                    Toaster.show("图片/视频上传失败，请重新上传～");
                    return;
                }

                showProgressDialog(R.string.app_release);

                String note = etNote.getText().toString().trim();

                presenter.apply(userId, note, urls.toString());
                break;
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    @Override
    public void uploadSucceed(LocalMedia item, List<UploadImgInfo> infos) {

        for (MediaItemModel model : adapter.getList()) {
            if (item.getRealPath().equals(model.getPath())) {
                model.status = MediaItemStatus.succeed;
                model.url = infos.get(0).getImgUrl();
                model.thumbUrl = infos.get(0).getTumhImgUrl();
            }
        }
        adapter.notifyDataSetChanged();
    }

    @SuppressLint("NotifyDataSetChanged")
    @Override
    public void uploadFailed(LocalMedia item) {
        Toaster.show("上传失败～");
        for (MediaItemModel model : adapter.getList()) {
            if (item.getRealPath().equals(model.getPath())) {
                model.status = MediaItemStatus.fail;
            }
        }

        adapter.notifyDataSetChanged();
    }

    @SuppressLint("NotifyDataSetChanged")
    @Override
    public void bindSucceed() {
//        etNote.setText("");
//        etUserId.setText("");
//        adapter.clear();
        dismissProgressDialog();
        EventBus.getDefault().post(new InviteEvent());
        InviteBindRecordsActivity.startAct(this);
        finish();
    }

    @Override
    public void bindFailed() {
        dismissProgressDialog();
    }

}