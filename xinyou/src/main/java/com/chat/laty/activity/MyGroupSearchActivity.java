package com.chat.laty.activity;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.view.View;
import android.widget.EditText;
import android.widget.TextView;

import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.hjq.toast.Toaster;
import com.chat.laty.R;
import com.chat.laty.adapter.MyGroupMoreAdapter;
import com.chat.laty.controllers.MyGroupController;
import com.chat.laty.dialog.BottomSignWheelViewDialog;
import com.chat.laty.entity.MyGroupListInfoModel;
import com.chat.laty.entity.PickerBean;
import com.chat.laty.mvp.BasePresenterActivity;
import com.chat.laty.presenters.MyGroupPresenter;
import com.chat.laty.view.CustomActionBar;

import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;

public class MyGroupSearchActivity extends BasePresenterActivity<MyGroupPresenter> implements MyGroupController.IGroupListView, MyGroupController.ISetLevelView {

    private static final String ARGS_KEY_TYPE = "args_key_type";
    private static final String ARGS_KEY_LEVEL = "args_key_level";
    private static String ARGS_KEY_USER_ID = "args_key_user_id";

    public static void startAct(Context context, int type, int level, String userId) {
        Intent intent = new Intent(context, MyGroupSearchActivity.class);
        intent.putExtra(ARGS_KEY_TYPE, type);
        intent.putExtra(ARGS_KEY_LEVEL, level);
        intent.putExtra(ARGS_KEY_USER_ID, userId);
        context.startActivity(intent);
    }

    @BindView(R.id.custom_bar)
    CustomActionBar customBar;

    @BindView(R.id.tv_search)
    TextView tvSearch;
    @BindView(R.id.et_search)
    EditText etSearch;

    @BindView(R.id.recycler_view)
    RecyclerView recyclerView;


    // (1-直推 2-团队)
    private int type = 1;
    private int level = 1;
    private String userId = "";

    private final int pageNo = 1;
    private final int pageSize = 100;

    private MyGroupMoreAdapter adapter;

    @Override
    protected void initDatas() {
        if (getIntent() != null) {
            type = getIntent().getIntExtra(ARGS_KEY_TYPE, 1);
            level = getIntent().getIntExtra(ARGS_KEY_LEVEL, 0);
            userId = getIntent().getStringExtra(ARGS_KEY_USER_ID);
        }
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_my_search_group_layout;
    }

    @Override
    protected void initViews() {
        customBar.setTitleText("团队搜索");
        customBar.setAllColor(ContextCompat.getColor(this, R.color.black));
        customBar.setOnActionBarClickListerner(function -> {
            if (function == CustomActionBar.FUNCTION_BUTTON_LEFT) {
                MyGroupSearchActivity.this.finish();
                return true;
            }
            return false;
        });


        adapter = new MyGroupMoreAdapter(type, level, userId);
        adapter.setItemAnimation(BaseQuickAdapter.AnimationType.SlideInRight);
        adapter.setStateView(getLayoutInflater().inflate(R.layout.community_empty_layout, null));

        // item click
        adapter.setOnItemClickListener((baseQuickAdapter, view, i) -> {
            MyGroupListInfoModel item = baseQuickAdapter.getItem(i);
//            if (TextUtils.equals("1", item.getIsLogOff())) return;
            if (TextUtils.equals(userId, item.getUserId())) return;
            MyMoreGroupActivity.startAct(this, type, item);
        });

        // 设置级别
        adapter.addOnItemChildClickListener(R.id.tv_set_level, (baseQuickAdapter, view, i) -> {

            MyGroupListInfoModel infoModel = baseQuickAdapter.getItem(i);
            if (TextUtils.equals("1", infoModel.getIsLogOff())) return;
            if (TextUtils.equals("1", infoModel.getIsSettingsLevel())) return;

            BottomSignWheelViewDialog dialog = new BottomSignWheelViewDialog(this);
            int userLevel = level;
            int dbnLevel = infoModel.getDbnLevel();

            List<PickerBean> list = adapter.getLevelList(userLevel, dbnLevel);


            if (list.isEmpty()) return;

            dialog.setOnDialogCallbackListener(info -> {
                showProgressDialog(R.string.app_loadding);
                presenter.setLevelInfo(infoModel.getUserId(), info.getKey(), i);
            });

            dialog.show();
            dialog.setNewDatas(list);
        });

        recyclerView.setAdapter(adapter);
    }

    @OnClick({R.id.tv_search})
    public void search(View view) {
        String content = etSearch.getText().toString().trim();
        if (content.isEmpty()) {
            Toaster.show("请输入搜索关键字");
            return;
        }

        showProgressDialog(R.string.app_loadding);
        presenter.getMyGroupList(content, type, userId, pageNo, pageSize, false, false, false);
    }

    @Override
    protected MyGroupPresenter setPresenter() {
        MyGroupPresenter presenter = new MyGroupPresenter(null, this);
        presenter.setSetLevelView(this);
        return presenter;
    }

    @Override/**/
    public FragmentActivity context() {
        return this;
    }

    @SuppressLint("NotifyDataSetChanged")
    @Override
    public void getGroupListSucceed(List<MyGroupListInfoModel> list, boolean load, boolean refresh, boolean loadmore) {

//                if (list.isEmpty()) {
//            MyGroupListInfoModel model = new MyGroupListInfoModel();
//            model.setAvatar("");
//            model.setNickname("NikeName");
//            model.setDbnLevel(20);
//            model.setDirectNum(34);
//            model.setUserId("4834925");
//            model.setRechargeNum(45);
//            model.setRegisterTime("2023-03-45");
//            model.setSocializeNum(54);
//            list.add(model);
//        }
        if (list.isEmpty()) {
            Toaster.show("未搜索到数据～");
        }
        adapter.setItems(list);
        adapter.setStateViewEnable(true);

        adapter.notifyDataSetChanged();

        dismissProgressDialog();
    }

    @Override
    public void getGroupListFailed(boolean load, boolean refresh, boolean loadmore) {
        dismissProgressDialog();
    }

    @Override
    public void setLevelSucceed(int index, int level) {
        MyGroupListInfoModel item = adapter.getItem(index);
        if (item != null) {
            item.setDbnLevel(level);
            item.setIsSettingsLevel("1");
            adapter.notifyItemChanged(index);
        }
        dismissProgressDialog();
    }

    @Override
    public void setLevelFailed() {
        dismissProgressDialog();
    }
}