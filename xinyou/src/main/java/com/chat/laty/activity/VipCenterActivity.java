package com.chat.laty.activity;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.graphics.Color;
import android.os.Handler;
import android.text.TextUtils;
import android.view.View;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.hjq.toast.Toaster;
import com.chat.laty.R;
import com.chat.laty.adapter.PayTypeAdapter;
import com.chat.laty.adapter.VipCardAdapter;
import com.chat.laty.adapter.VipGridAdapter;
import com.chat.laty.base.AppHelper;
import com.chat.laty.base.XYApplication;
import com.chat.laty.controllers.VipCenterController;
import com.chat.laty.entity.PayResultEntity;
import com.chat.laty.entity.PayTypeModel;
import com.chat.laty.entity.RyUserInfo;
import com.chat.laty.entity.VipInfoModel;
import com.chat.laty.entity.WeiChatPayInfo;
import com.chat.laty.greenDao.RyUserInfoDao;
import com.chat.laty.mvp.BasePresenterActivity;
import com.chat.laty.presenters.PayManager;
import com.chat.laty.presenters.VipCenterPresenter;
import com.chat.laty.utils.AgreementController;
import com.chat.laty.utils.PicassoUtils;
import com.chat.laty.view.CustomActionBar;
import com.youth.banner.Banner;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import butterknife.BindView;
import butterknife.OnClick;

public class VipCenterActivity extends BasePresenterActivity<VipCenterPresenter> implements VipCenterController.IInfoView {


    public static void startAct(Context context, String vipType) {
        Intent intent = new Intent(context, VipCenterActivity.class);
        intent.putExtra("vipType", vipType);
        context.startActivity(intent);
    }


    @BindView(R.id.custom_bar)
    CustomActionBar customBar;

    @BindView(R.id.tv_user_name)
    TextView tvUserName;
    @BindView(R.id.iv_avatar)
    ImageView ivAvatar;

    @BindView(R.id.v_vip_layout)
    View vVipLayout;
    @BindView(R.id.tv_vip_level)
    TextView tvVipLevel;
    @BindView(R.id.tv_vip_label)
    TextView tvVipLabel;
    @BindView(R.id.tv_v_label)
    TextView tvVLabel;

    @BindView(R.id.recycler_view)
    RecyclerView rvVipCard;

    @BindView(R.id.banner)
    Banner banner;

    @BindView(R.id.rv_vip_grid)
    RecyclerView rvVipGrid;
    @BindView(R.id.rv_pay_grid)
    RecyclerView rvPayGrid;
    @BindView(R.id.checkbox)
    CheckBox cbAgree;
    @BindView(R.id.btn_recharge)
    Button btnRecharge;
    private String mVipType;

    @Override
    protected void initDatas() {
        mVipType = getIntent().getStringExtra("vipType");

//        userInfo = XYApplication.getDaoInstant().getRyUserInfoDao().queryBuilder().where(RyUserInfoDao.Properties.Id.eq(data.getUserId())).unique();
    }


    @Override
    protected void onResume() {
        super.onResume();
        if (payed) {
            presenter.getVipInfo();
            payed = false;
        }
    }

    boolean payed = false;

    @Override
    protected boolean isUseEventBus() {
        return true;
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void payResult(PayResultEntity event) {
        if (event.code == 0) {
            payed = true;
            Toaster.show("开通成功");
            new Handler().postDelayed(() -> presenter.getVipInfo(), 3000);
        }
    }


    VipCardAdapter vipCardAdapter;
    //    VipCardAdapterV2 bannerAdapter;
    VipGridAdapter vipGridAdapter;
    PayTypeAdapter payTypeAdapter;

    @Override
    protected int getContentViewId() {
        return R.layout.activity_vip_center;
    }

    VipInfoModel model;
    RyUserInfo userInfo;
    VipInfoModel.VipTypeModel vipTypeModel;
    List<VipInfoModel.VipTypeModel> list = new ArrayList<>();

    int payTypeIndex = -1;

    @Override
    protected void initViews() {

        customBar.setTitleText("会员中心");
        customBar.setRightText("vip开通记录");
        customBar.setAllColor(ContextCompat.getColor(this, R.color.white));


        customBar.addFunction(CustomActionBar.FUNCTION_TEXT_RIGHT);
        customBar.setOnActionBarClickListerner(function -> {
            if (function == CustomActionBar.FUNCTION_BUTTON_LEFT) {
                VipCenterActivity.this.finish();
                return true;
            } else if (function == CustomActionBar.FUNCTION_TEXT_RIGHT) {
                VipDetailActivity.startAct(VipCenterActivity.this);
                return true;
            }
            return false;
        });
        customBar.setLeftBackground(ContextCompat.getDrawable(this, R.mipmap.icon_back_white));
//        customBar.setLeftColorFilter(ContextCompat.getColor(this, R.color.white));


        vipCardAdapter = new VipCardAdapter();
        rvVipCard.setAdapter(vipCardAdapter);
        rvVipCard.setLayoutManager(new LinearLayoutManager(this));

        vipGridAdapter = new VipGridAdapter();
        rvVipGrid.setAdapter(vipGridAdapter);

        payTypeAdapter = new PayTypeAdapter();
        rvPayGrid.setAdapter(payTypeAdapter);

        presenter.getVipInfo();
    }

    @SuppressLint({"SetTextI18n", "NotifyDataSetChanged"})
    private void toggle() {
        tvVLabel.setText(vipTypeModel.getVipName() + "会员特权");

        /*List<VipGridModel> vipGridModels = new ArrayList<>();

        if (TextUtils.equals("1", vipTypeModel.getIsLogo())) {
            vipGridModels.add(new VipGridModel(R.mipmap.biaoshi_vip, "会员专属标识/头像框"));
        }
        if (vipTypeModel.getIsPrivateAlbum()==1) {
            vipGridModels.add(new VipGridModel(R.mipmap.simi_vip, "解锁私密照片"));
        }
        if (vipTypeModel.getIsCallPermission()==1) {
            vipGridModels.add(new VipGridModel(R.mipmap.yuyin_vip, "解锁语音视频功能"));
        }
        if (vipTypeModel.getExclusiveNameColor()!=null && !TextUtils.isEmpty(vipTypeModel.getExclusiveNameColor())) {
            vipGridModels.add(new VipGridModel(R.mipmap.nicheng_vip, "昵称变金"));
        }

        if (TextUtils.equals("1", vipTypeModel.getIsVisitor())) {
            vipGridModels.add(new VipGridModel(R.mipmap.vip_center_visitor, "查看访客中心"));
        }

        if (vipTypeModel.getGiveTextNum() > 0) {
            vipGridModels.add(new VipGridModel(R.mipmap.wenzi_vip, "每天赠送文字聊天" + vipTypeModel.getGiveTextNum() + "条"));
        }
        float discount = vipTypeModel.getVideoDiscount() / 10;

        vipGridModels.add(new VipGridModel(R.mipmap.tonghua_vip, "语音/视频通话" + discount + "折"));

        if (!TextUtils.isEmpty(vipTypeModel.getGiveVoiceNum()) && Integer.valueOf(vipTypeModel.getGiveVoiceNum()) > 0)
            vipGridModels.add(new VipGridModel(R.mipmap.vip_yuyin_time_long, "赠送的语音" + vipTypeModel.getGiveVoiceNum() + "分钟"));

        if (!TextUtils.isEmpty(vipTypeModel.getGiveVideoNum()) && Integer.valueOf(vipTypeModel.getGiveVideoNum()) > 0)
            vipGridModels.add(new VipGridModel(R.mipmap.vip_video_time_long, "赠送的视频" + vipTypeModel.getGiveVideoNum() + "分钟"));*/

        if (vipTypeModel.getPowerList() != null) {
            vipGridAdapter.setItems(vipTypeModel.getPowerList());
            vipGridAdapter.notifyDataSetChanged();
        }

        btnRecharge.setText("¥" + vipTypeModel.getPrice() + " 立即开通");
    }


    private final AgreementController controller = new AgreementController();

    @SuppressLint("NonConstantResourceId")
    @OnClick({R.id.tv_agree, R.id.btn_recharge})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.tv_agree:
                controller.getAccordByNum(this, 8);
                break;
            case R.id.btn_recharge:
                if (getStatusFromCache()){
                    Toaster.show("您处于青少年模式无法使用此功能");
                    return;
                }
                if (payTypeIndex == -1) {
                    Toaster.show("请选择支付方式");
                    return;
                }
                if (!cbAgree.isChecked()) {
                    Toaster.show("请先阅读并同意会员充值协议");
                    return;
                }

                showProgressDialog(R.string.app_loadding);
                PayTypeModel payTypeModel = model.getPayTypeList().get(payTypeIndex);
                presenter.buyVip(payTypeModel.getCode(), vipTypeModel.getId());
                break;
        }
    }

    @Override
    protected VipCenterPresenter setPresenter() {
        return new VipCenterPresenter(this, null);
    }

    @SuppressLint("NotifyDataSetChanged")
    @Override
    public void getInfoSucceed(VipInfoModel data) {

        this.model = data;

        tvUserName.setText(data.getNickname());
        PicassoUtils.showImage(ivAvatar, data.getAvatar());

        if (userInfo == null) {
            userInfo = XYApplication.getDaoInstant().getRyUserInfoDao().queryBuilder().where(RyUserInfoDao.Properties.Id.eq(data.getUserId())).unique();
        }

        if (userInfo != null && TextUtils.equals("2", userInfo.getSex())) {
            tvVipLabel.setVisibility(View.GONE);
//            btnRecharge.setVisibility(View.GONE);
//            cbAgree.setEnabled(false);
            vVipLayout.setVisibility(View.GONE);
        } else {
            String label = "你还没有成为会员～";
            vVipLayout.setVisibility(View.GONE);

            if (!TextUtils.equals("0", data.getVipLevel())) {
                label = "到期时间：" + data.getVipEndTime();
                vVipLayout.setVisibility(View.VISIBLE);
                int color = Color.parseColor(getVipColors(data.getVipLevel()));
                tvVipLevel.setText(data.getVipName());
                tvVipLevel.setTextColor(color);
            }
            tvVipLabel.setText(label);
        }

//        list = data.getVipList();
//        vipCardAdapter.setItems(list);
//        vipCardAdapter.notifyDataSetChanged();
//        bannerAdapter.setDatas(list);
        for (VipInfoModel.VipTypeModel m : data.getVipList()) {
            if (TextUtils.equals(mVipType, m.getId())) {
                list.add(m);
                vipCardAdapter.setItems(list);
                vipCardAdapter.notifyDataSetChanged();
                vipTypeModel = m;
                toggle();
            }
        }


        for (PayTypeModel m : model.getPayTypeList()) {
            if (m.getCode().contains("wechat") || m.getCode().contains("wxPayAll")) {
                m.setIcon(R.mipmap.pay_weixin);
            } else if (m.getCode().contains("ali") || m.getCode().contains("aliPayAll")) {
                m.setIcon(R.mipmap.pay_zhifubao);
            } else {
                m.setIcon(R.mipmap.pay_yinlian);
            }
        }
        payTypeAdapter.setItems(model.getPayTypeList());
        payTypeAdapter.setOnItemClickListener((baseQuickAdapter, view, i) -> {

            if (payTypeIndex == i) return;

            if (payTypeIndex != -1) {
                Objects.requireNonNull(baseQuickAdapter.getItem(payTypeIndex)).selected = false;
                baseQuickAdapter.notifyItemChanged(payTypeIndex);
            }
            payTypeIndex = i;
            Objects.requireNonNull(baseQuickAdapter.getItem(payTypeIndex)).selected = true;
            baseQuickAdapter.notifyItemChanged(payTypeIndex);

        });
        payTypeAdapter.notifyDataSetChanged();
    }

    @Override
    public void getInfoFailed() {
    }

    private String getVipColors(String level) {
        switch (level) {
            case "1":
                return "#824D2F";
            case "2":
                return "#5A7AB4";
            case "3":
                return "#7E496C";
            case "4":
                return "#953D4C";
        }
        return "#824D2F";
    }


    @Override
    public void buySucceed(WeiChatPayInfo info) {
        dismissProgressDialog();
        PayTypeModel data = model.getPayTypeList().get(payTypeIndex);
        if (data.getCode().contains("wxPayAll") || data.getCode().contains("aliPayAll")) {
            AppHelper.openUrl(info.getPayUrl());
        } else if (data.getCode().contains("wechat")) {
            PayManager.weiChatPay(this, info);
        } else if (data.getCode().contains("ali")) {
            PayManager.alipayPay(this, info.getPayUrl());
        } else {
            Toaster.show("银联支付");
        }
    }

    @Override
    public void failed() {
        dismissProgressDialog();
    }

    @Override
    public FragmentActivity context() {
        return this;
    }

    @Override
    protected void onDestroy() {
        banner.destroy();
        super.onDestroy();
    }

    /**
     * 从缓存中获取状态
     *
     * @return
     */
    private boolean getStatusFromCache() {
        SharedPreferences sharedPreferences = getSharedPreferences("app_prefs", Context.MODE_PRIVATE);
        return sharedPreferences.getBoolean("qsnms_status", false); // 默认值为 false
    }
}