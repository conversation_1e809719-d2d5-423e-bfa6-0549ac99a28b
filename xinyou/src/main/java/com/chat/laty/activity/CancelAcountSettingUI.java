package com.chat.laty.activity;

import android.content.Context;
import android.content.Intent;
import android.view.View;
import android.widget.CheckBox;
import android.widget.CompoundButton;

import androidx.fragment.app.FragmentActivity;

import com.hjq.toast.Toaster;
import com.chat.laty.R;
import com.chat.laty.controllers.GeneralSettingController;
import com.chat.laty.entity.BaseBean;
import com.chat.laty.entity.GeneralSettingInfo;
import com.chat.laty.entity.SimiInfo;
import com.chat.laty.entity.WebBeanInfo;
import com.chat.laty.mvp.BasePresenterActivity;
import com.chat.laty.presenters.GeneralSettingPresenter;
import com.chat.laty.utils.XYSPUtils;
import com.chat.laty.utils.net.Common;
import com.chat.laty.view.CustomActionBar;

import butterknife.BindView;
import butterknife.OnClick;
import io.rong.imkit.RongIM;

/**
 * <AUTHOR>
 * @date 2023/12/24 20:46
 * @description:
 */
public class CancelAcountSettingUI extends BasePresenterActivity<GeneralSettingPresenter> implements GeneralSettingController.View {

    @BindView(R.id.custom_bar)
    CustomActionBar mActionBar;

    @BindView(R.id.agree_checkbox)
    CheckBox mAgreeCb;

    private boolean mIsAgree = false;

    public static void start(Context context) {
        Intent intent = new Intent(context, CancelAcountSettingUI.class);
        context.startActivity(intent);
    }

    @Override
    protected void initDatas() {

    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_cancel_acount_activity;
    }

    @Override
    protected void initViews() {
        mActionBar.setTitleText("注销账号");
        mActionBar.setTitleTextColor(getResources().getColor(R.color.color_333333));

        mAgreeCb.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                mIsAgree = isChecked;
            }
        });
    }

    @OnClick({R.id.sure_tv,R.id.cancel_tv})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.cancel_tv:
                finish();
                break;
            case R.id.sure_tv://注销账号
                if (!mIsAgree) {
                    Toaster.show("请仔细阅读并同意注销事项");
                    return;
                }
                presenter.logOffUser();
                break;
        }
    }


    @Override
    protected GeneralSettingPresenter setPresenter() {
        return new GeneralSettingPresenter(this);
    }

    @Override
    public FragmentActivity context() {
        return this;
    }

    @Override
    public void showGeneralSetting(GeneralSettingInfo info) {
    }

    @Override
    public void showWebInfo(WebBeanInfo info) {
    }

    @Override
    public void showDeleteCallback(BaseBean baseBean) {
        if (null != baseBean) {
            Toaster.show(baseBean.getMessage());
            if (200 == baseBean.getCode()) {
                XYSPUtils.delete(Common.KEY_APP_TOKEN);
                XYSPUtils.delete(Common.KEY_APP_USER_RY_ID);
                XYSPUtils.delete(Common.KEY_APP_USER_GENDER);
                XYSPUtils.delete(Common.KEY_APP_USER_PHONE);
                RongIM.getInstance().logout();
                LoginByWeiChatUI.startNew(CancelAcountSettingUI.this);
                finish();
            }
        }
    }

    @Override
    public void showLogoutCallback(BaseBean uploadBean) {

    }

    @Override
    public void getSimiInfo(SimiInfo simiInfo) {

    }
}
