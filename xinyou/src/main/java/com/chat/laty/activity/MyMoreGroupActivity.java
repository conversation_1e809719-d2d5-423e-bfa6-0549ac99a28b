package com.chat.laty.activity;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.header.ClassicsHeader;
import com.chat.laty.R;
import com.chat.laty.adapter.MyGroupMoreAdapter;
import com.chat.laty.controllers.MyGroupController;
import com.chat.laty.entity.MyGroupListInfoModel;
import com.chat.laty.mvp.BasePresenterActivity;
import com.chat.laty.presenters.MyGroupPresenter;
import com.chat.laty.utils.PicassoUtils;
import com.chat.laty.view.CustomActionBar;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;

public class MyMoreGroupActivity extends BasePresenterActivity<MyGroupPresenter> implements MyGroupController.IGroupListView {

    private static final String ARGS_KEY_TYPE = "args_key_type";
    private static final String ARGS_KEY_LEVEL = "args_key_level";
    private static final String ARGS_KEY_USER_DATA = "args_key_data";

    public static void startAct(Context context, int type, MyGroupListInfoModel data) {
        Intent intent = new Intent(context, MyMoreGroupActivity.class);
        intent.putExtra(ARGS_KEY_TYPE, type);
        intent.putExtra(ARGS_KEY_USER_DATA, data);
        context.startActivity(intent);
    }

    @BindView(R.id.custom_bar)
    CustomActionBar customBar;

    @BindView(R.id.iv_avatar)
    ImageView ivAvatar;
    @BindView(R.id.tv_user_name)
    TextView tvUserName;
    @BindView(R.id.tv_vip_label)
    TextView tvVipLabel;
    @BindView(R.id.tv_user_id)
    TextView tvUserId;
    @BindView(R.id.tv_value)
    TextView tvZtValue;

    @BindView(R.id.recycler_view)
    RecyclerView recyclerView;
    @BindView(R.id.smart_refresh_layout)
    SmartRefreshLayout refreshLayout;


    // (1-直推 2-团队)
    private int type = 2;
    private int level = 0;
    private String userId = "";
    private int pageNo = 1;
    private final int pageSize = 10;

    private MyGroupListInfoModel model;

    private MyGroupMoreAdapter adapter;

    @Override
    protected void initDatas() {
        if (getIntent() != null) {
            model = getIntent().getParcelableExtra(ARGS_KEY_USER_DATA);
            userId = model.getUserId();
        }
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_my_more_group_layout;
    }

    private List<MyGroupListInfoModel> list = new ArrayList<>();

    @SuppressLint("SetTextI18n")
    @Override
    protected void initViews() {

        PicassoUtils.showImage(ivAvatar, model.getAvatar());
        tvUserName.setText(model.getNickname());
        tvVipLabel.setText(MyGroupMoreAdapter.getLabel(model.getDbnLevel()));
        tvZtValue.setText(model.getDirectNum() + "");
        tvUserId.setText("ID:" + model.getUserId());

        customBar.setTitleText("我的团队");
        customBar.setAllColor(ContextCompat.getColor(this, R.color.white));

        customBar.setOnActionBarClickListerner(function -> {
            if (function == CustomActionBar.FUNCTION_BUTTON_LEFT) {
                MyMoreGroupActivity.this.finish();
                return true;
            }
            return false;
        });


        ClassicsHeader classicsHeader = new ClassicsHeader(context());
        classicsHeader.setProgressDrawable(ContextCompat.getDrawable(context(), R.drawable.pull_refresh_view_refresh_custom));
        classicsHeader.setArrowDrawable(ContextCompat.getDrawable(context(), R.drawable.pull_refresh_view_refresh_custom));
        classicsHeader.setEnableLastTime(false);
        refreshLayout.setRefreshHeader(classicsHeader);

        refreshLayout.setOnRefreshListener(refreshLayout -> presenter.getMyGroupList("", type, userId, pageNo, pageSize, false, true, false));
        refreshLayout.setOnLoadMoreListener(refreshLayout -> presenter.getMyGroupList("", type, userId, pageNo, pageSize, false, false, true));

        adapter = new MyGroupMoreAdapter(type, model.getDbnLevel(), userId);
        adapter.setItemAnimation(BaseQuickAdapter.AnimationType.SlideInRight);
        adapter.setStateView(getLayoutInflater().inflate(R.layout.community_empty_layout, null));
        recyclerView.setAdapter(adapter);
        // item click
        adapter.setOnItemClickListener((baseQuickAdapter, view, i) -> {
            MyGroupListInfoModel item = baseQuickAdapter.getItem(i);
//            if (TextUtils.equals("1", item.getIsLogOff())) return;
            if (TextUtils.equals(userId, item.getUserId())) return;

            MyMoreGroupActivity.startAct(this, type, item);
        });

        presenter.getMyGroupList("", type, userId, pageNo, pageSize, true, false, false);
    }

    @Override
    protected MyGroupPresenter setPresenter() {
        return new MyGroupPresenter(null, this);
    }

    @Override
    public FragmentActivity context() {
        return this;
    }

    /**
     * 获取团队列表成功
     * @param list
     * @param load
     * @param refresh
     * @param loadmore
     */
    @SuppressLint("NotifyDataSetChanged")
    @Override
    public void getGroupListSucceed(List<MyGroupListInfoModel> list, boolean load, boolean refresh, boolean loadmore) {
        if (load) {
            this.list = list;
            adapter.setStateViewEnable(true);
        } else if (refresh) {
            pageNo = 1;
            this.list = list;
            refreshLayout.finishRefresh();
        } else if (loadmore) {
            this.list.addAll(list);
            refreshLayout.finishLoadMore();
        }
        if (list.size() >= pageSize) {
            pageNo++;
            refreshLayout.setEnableLoadMore(true);
        } else {
            refreshLayout.setEnableLoadMore(false);
        }

        adapter.setItems(this.list);
        adapter.notifyDataSetChanged();
    }

    @Override
    public void getGroupListFailed(boolean load, boolean refresh, boolean loadmore) {
        if (refresh) {
            refreshLayout.finishRefresh();
        } else if (loadmore) {
            refreshLayout.finishLoadMore();
        }
    }
}