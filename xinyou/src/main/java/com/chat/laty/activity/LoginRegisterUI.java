package com.chat.laty.activity;

import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.ImageView;
import android.widget.LinearLayout;

import androidx.appcompat.widget.AppCompatTextView;
import androidx.fragment.app.FragmentActivity;

import com.flyco.tablayout.CommonTabLayout;
import com.flyco.tablayout.listener.CustomTabEntity;
import com.flyco.tablayout.listener.OnTabSelectListener;
import com.hjq.toast.Toaster;
import com.chat.laty.R;
import com.chat.laty.base.AppHelper;
import com.chat.laty.contants.Constant;
import com.chat.laty.controllers.LoginController;
import com.chat.laty.entity.LoginEntity;
import com.chat.laty.entity.LoinEntity;
import com.chat.laty.entity.RYTokenInfo;
import com.chat.laty.entity.TabEntity;
import com.chat.laty.entity.WXLoginInfo;
import com.chat.laty.entity.WebBeanInfo;
import com.chat.laty.mvp.BasePresenterActivity;
import com.chat.laty.presenters.LoginPresenter;
import com.chat.laty.utils.XYSPUtils;
import com.chat.laty.view.ClearEditText;

import java.util.ArrayList;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * <AUTHOR>
 * @date 2023/9/11 15:36
 * @description:登录注册
 */
public class LoginRegisterUI extends BasePresenterActivity<LoginPresenter> implements LoginController.View {

    @BindView(R.id.common_tab_layout)
    CommonTabLayout mCommonTabLayout;

    private ArrayList<CustomTabEntity> mTabEntities = new ArrayList<>();
    @BindView(R.id.edit_username)
    ClearEditText editUsername;
    @BindView(R.id.edit_passwd)
    ClearEditText editPasswd;
    @BindView(R.id.vcode_layout)
    LinearLayout vcodeLayout;
    @BindView(R.id.edit_vcode)
    ClearEditText editVcode;
    @BindView(R.id.get_vcode_tv)
    AppCompatTextView getVcodeTv;

    @BindView(R.id.login_find_pwd_tv)
    AppCompatTextView findPwdTv;
    @BindView(R.id.invitation_code_layout)
    LinearLayout invitationCodeLayout;
    @BindView(R.id.edit_invitation_code)
    ClearEditText editInvitationCode;
    @BindView(R.id.login_button)
    Button loginButton;

    @BindView(R.id.agree_checkbox)
    CheckBox mAgreeCb;
    @BindView(R.id.iv_login_bg)
    ImageView loginBg;

    private int mCurrentType = 0;
    private boolean mIsAgree = false;


    public static void start(Context context) {
        Intent intent = new Intent(context, LoginRegisterUI.class);
        context.startActivity(intent);
    }

    @Override
    protected void initDatas() {

    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_login_register_layout;
    }

    @Override
    protected void initViews() {
        startBgAnimation();
        mTabEntities.add(new TabEntity("登录", 0, 0));
        mTabEntities.add(new TabEntity("注册", 0, 0));
        mCommonTabLayout.setTabData(mTabEntities);

        mCommonTabLayout.setOnTabSelectListener(new OnTabSelectListener() {
            @Override
            public void onTabSelect(int position) {
//                showType(position);
            }

            @Override
            public void onTabReselect(int position) {
            }
        });

        mAgreeCb.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                mIsAgree = isChecked;
            }
        });

//        editUsername.setText(JLSPUtils.getString(Common.KEY_APP_USER_PHONE));

    }

    private void showType(int position) {
        mCurrentType = position;
        switch (position) {
            case 0://登录
                vcodeLayout.setVisibility(View.GONE);
                invitationCodeLayout.setVisibility(View.GONE);
                findPwdTv.setVisibility(View.VISIBLE);
                loginButton.setText("登录");
                break;

            case 1://注册
                vcodeLayout.setVisibility(View.VISIBLE);
                invitationCodeLayout.setVisibility(View.VISIBLE);
                findPwdTv.setVisibility(View.GONE);
                loginButton.setText("注册");
                break;
        }
    }

    @OnClick({R.id.login_button, R.id.get_vcode_tv, R.id.user_agreement_tv, R.id.privacy_policy_tv, R.id.login_find_pwd_tv})
    public void onClick(View view) {
        switch (view.getId()) {

            case R.id.login_button:
                AppHelper.hideSoftPad(LoginRegisterUI.this);

                if (TextUtils.isEmpty(editUsername.getText().toString())) {
                    Toaster.show("请输入您的手机号");
                    return;
                }
                if (TextUtils.isEmpty(editPasswd.getText().toString())) {
                    Toaster.show("请输入密码");
                    return;
                }
                if (!mIsAgree) {
                    Toaster.show("请阅读并同意用户协议");
                    return;
                }

                if (0 == mCurrentType) {
                    showProgressDialog(R.string.app_uploadding);
                    presenter.login(editUsername.getText().toString(), editPasswd.getText().toString());
                } else if (1 == mCurrentType) {

                    if (TextUtils.isEmpty(editVcode.getText().toString())) {
                        Toaster.show("请输入验证码");
                        return;
                    }

                    showProgressDialog(R.string.app_uploadding);
                    presenter.register(editUsername.getText().toString(), editPasswd.getText().toString(), editVcode.getText().toString(), editInvitationCode.getText().toString());
                }
                break;

            case R.id.get_vcode_tv:
                if (TextUtils.isEmpty(editUsername.getText().toString())) {
                    Toaster.show("请输入手机号");
                    return;
                }
                presenter.getVcode(editUsername.getText().toString());
                break;

            case R.id.user_agreement_tv:
                break;

            case R.id.privacy_policy_tv:
                break;

            case R.id.login_find_pwd_tv:
                break;
        }
    }

    @Override
    protected LoginPresenter setPresenter() {
        return new LoginPresenter(this);
    }


    @Override
    public FragmentActivity context() {
        return this;
    }

    @Override
    public void showCountDown(long l) {

    }

    @Override
    public void showCountDownFinish() {

    }

    @Override
    public void showLogin(LoinEntity data) {
        dismissProgressDialog();
        if (null != data) {
            presenter.getRongCloudToken();
            Toaster.show(data.getUserInfo().getNickname());

        }
    }

    @Override
    public void showRyToken(RYTokenInfo data) {
        if (null != data && !TextUtils.isEmpty(data.getToken())) {
            XYSPUtils.put(Constant.KEY_USER_RONGYUN_TOKEN, data.getToken());
        }
    }

    @Override
    public void showLoginCallBack(LoginEntity data) {
        presenter.getRongCloudToken();
    }

    @Override
    public void showWeiXinLogin(WXLoginInfo data) {

    }

    @Override
    public void showLoginImCallback() {

    }

    @Override
    public void showWebInfo(WebBeanInfo data) {

    }

    @Override
    public void loadVipData() {

    }

    private void startBgAnimation() {
        Animation animation =
                AnimationUtils.loadAnimation(this, R.anim.seal_login_bg_translate_anim);
        loginBg.startAnimation(animation);
    }

//
//    public void connectIM(String token) {
////        String targetId = "1725849550466932737";//1729341395424034818（176） 1729776846723137538（133）
//        int timeLimit = 0;
//        RongIM.connect(token, timeLimit, new RongIMClient.ConnectCallback() {
//            @Override
//            public void onDatabaseOpened(RongIMClient.DatabaseOpenStatus code) {
//                if (RongIMClient.DatabaseOpenStatus.DATABASE_OPEN_SUCCESS.equals(code)) {
//                    //本地数据库打开，跳转到会话列表页面
//                } else {
//                    //数据库打开失败，可以弹出 toast 提示。
//                }
//            }
//
//            @Override
//            public void onSuccess(String userId) {
//                //连接成功，如果 onDatabaseOpened() 时没有页面跳转，也可在此时进行跳转。
//                LogUtil.e("响应结果", "==========userId###" + userId);
//                Toaster.show("连接成功-->userId是" + userId);
//                EventBus.getDefault().post("connectIM");
//                // RTCLib 初始化
////                initRTC(LoginRegisterUI.this);
//            }
//
//            @Override
//            public void onError(RongIMClient.ConnectionErrorCode errorCode) {
//                Toaster.show("onError-->code::"+errorCode.getValue());
//                if (errorCode.equals(RongIMClient.ConnectionErrorCode.RC_CONN_TOKEN_EXPIRE)) {
//                    //从 APP 服务请求新 token，获取到新 token 后重新 connect()
//                    Toaster.show("从 APP 服务请求新 token，获取到新 token 后重新 connect()");
//                } else if (errorCode.equals(RongIMClient.ConnectionErrorCode.RC_CONNECT_TIMEOUT)) {
//                    //连接超时，弹出提示，可以引导用户等待网络正常的时候再次点击进行连接
//                    Toaster.show("连接超时");
//                } else {
//                    //其它业务错误码，请根据相应的错误码作出对应处理。
//                }
//            }
//        });
//    }

}
