package com.chat.laty.activity;

import android.content.Context;
import android.content.Intent;

import androidx.recyclerview.widget.RecyclerView;

import com.lxj.xpopup.XPopup;
import com.lxj.xpopup.interfaces.OnConfirmListener;
import com.chat.laty.R;
import com.chat.laty.adapter.BlackListAdapter;
import com.chat.laty.base.BaseActivity;
import com.chat.laty.view.CustomActionBar;
import com.chat.laty.view.RecyclerItemDecoration;
import com.chat.laty.view.RotateAnimator;

import java.util.ArrayList;
import java.util.Arrays;

import butterknife.BindView;
import io.rong.imlib.RongIMClient;

/**
 * <AUTHOR>
 * @date 2024/1/31 14:01
 * @description:黑名单
 */
public class BlacklistActivity extends BaseActivity {

    @BindView(R.id.custom_bar)
    CustomActionBar mActionBar;
    @BindView(R.id.base_rv)
    RecyclerView mRecyclerView;

    BlackListAdapter mAdapter;

    private String mMoveUserId;


    public static void start(Context context) {
        Intent intent = new Intent(context, BlacklistActivity.class);
        context.startActivity(intent);
    }

    @Override
    protected void initDatas() {

    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_base_rc_layout;
    }

    @Override
    protected void initViews() {
        showProgressDialog(R.string.app_loadding);
        mActionBar.setTitleText("黑名单");
        mActionBar.setTitleTextColor(getResources().getColor(R.color.color_333333));
        mAdapter = new BlackListAdapter();
        mRecyclerView.addItemDecoration(new RecyclerItemDecoration());
        mRecyclerView.setAdapter(mAdapter);

        mAdapter.setStateViewEnable(true);
        mAdapter.setStateView(createEmptyListView("暂无黑名单", R.mipmap.icon_nodata));

        mAdapter.addOnItemChildClickListener(R.id.remove_button, (baseQuickAdapter, view, position) -> {
            mMoveUserId = (String) baseQuickAdapter.getItem(position);
            new XPopup.Builder(BlacklistActivity.this)
                    .isDestroyOnDismiss(true)
                    .customAnimator(new RotateAnimator())
                    .asConfirm("移除黑名单", "您是否确定将Ta移除黑名单?", new OnConfirmListener() {
                        @Override
                        public void onConfirm() {
                            RongIMClient.getInstance().removeFromBlacklist(mMoveUserId, blackCallback);
                        }
                    }).show();

        });

        mAdapter.addOnItemChildLongClickListener(R.id.item_layout, (baseQuickAdapter, view, i) -> {
            mMoveUserId = (String) baseQuickAdapter.getItem(i);
            new XPopup.Builder(BlacklistActivity.this)
                    .isDestroyOnDismiss(true) //对于只使用一次的弹窗，推荐设置这个
                    .customAnimator(new RotateAnimator())
                    .asConfirm("移除黑名单", "您是否确定将Ta移除黑名单?", new OnConfirmListener() {
                        @Override
                        public void onConfirm() {
                            RongIMClient.getInstance().removeFromBlacklist(mMoveUserId, blackCallback);
                        }
                    }).show();
            return true;
        });

        RongIMClient.getInstance()
                .getBlacklist(new RongIMClient.GetBlacklistCallback() {
                    @Override
                    public void onSuccess(String[] strings) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                dismissProgressDialog();
                                mAdapter.setItems(new ArrayList<String>(Arrays.asList(strings)));
                                mAdapter.notifyDataSetChanged();
                            }
                        });

                    }

                    @Override
                    public void onError(RongIMClient.ErrorCode e) {

                    }
                });


    }

    RongIMClient.OperationCallback blackCallback = new RongIMClient.OperationCallback() {
        @Override
        public void onSuccess() {
            mAdapter.remove(mMoveUserId);
        }

        @Override
        public void onError(RongIMClient.ErrorCode errorCode) {

        }
    };
}
