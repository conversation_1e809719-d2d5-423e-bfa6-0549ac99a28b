package com.chat.laty.activity;

import android.content.Intent;
import android.os.CountDownTimer;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;

import androidx.appcompat.widget.AppCompatTextView;
import androidx.fragment.app.FragmentActivity;


import com.makeramen.roundedimageview.RoundedImageView;
import com.chat.laty.MainActivity;
import com.chat.laty.R;
import com.chat.laty.base.authpack;
import com.chat.laty.contants.Constant;
import com.chat.laty.controllers.GeneralSettingController;
import com.chat.laty.dialog.UserNoteDialog;
import com.chat.laty.entity.BaseBean;
import com.chat.laty.entity.GeneralSettingInfo;
import com.chat.laty.entity.SimiInfo;
import com.chat.laty.entity.WebBeanInfo;
import com.chat.laty.mvp.BasePresenterActivity;
import com.chat.laty.presenters.GeneralSettingPresenter;
import com.chat.laty.utils.LogUtil;
import com.chat.laty.utils.XYSPUtils;
import com.chat.laty.utils.net.Common;

import butterknife.BindView;
import butterknife.OnClick;
import cn.rongcloud.fubeautifier.FUBeautifierResultCallback;
import cn.rongcloud.fubeautifier.RCRTCFUBeautifierEngine;

/**
 * <AUTHOR>
 * @date 2021/5/20.
 * description：xinyou开屏页。
 */
public class AppStartActivity extends BasePresenterActivity<GeneralSettingPresenter> implements GeneralSettingController.View {

//    @BindView(R.id.back_riv)
//    ImageView mBackIv;
    @BindView(R.id.app_bg_riv)
    RoundedImageView mAppBgIv;

    @BindView(R.id.btn_skip)
    AppCompatTextView mSkipTv;

    @Override
    protected void initDatas() {

    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_appstart_layout;
    }

    @Override
    protected void initViews() {
        //隐藏全面屏虚拟键
        Window mWindow = getWindow();
        WindowManager.LayoutParams params = mWindow.getAttributes();
        params.systemUiVisibility = View.SYSTEM_UI_FLAG_HIDE_NAVIGATION | View.SYSTEM_UI_FLAG_IMMERSIVE;
        mWindow.setAttributes(params);

//        PicassoUtils.showImage(mAppBgIv,"https://img2.baidu.com/it/u=2544545297,2349080937&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=889");
//        PicassoUtils.showImage(mBackIv, "https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fwww.mms591.com%2Fwww.mms591.com-photo%2F20170401%2F1-1F401144058_480x800.jpg&refer=http%3A%2F%2Fwww.mms591.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=jpeg?sec=1624095553&t=f0dc0a3e41bda82ae8039f2a1e008648");
//        PicassoUtils.showAppStartImage(mBackIv, R.mipmap.splash);

//        if (!this.isTaskRoot()) {//文件扫描开启
//            Intent mainIntent = getIntent();
//            String action = mainIntent.getAction();
//            if (mainIntent.hasCategory(Intent.CATEGORY_LAUNCHER) && null != action && Intent.ACTION_MAIN.equals(action)) {
//                finish();
//                return;
//            }
//        }




        if (!this.isTaskRoot()) {
            Intent intent = getIntent();
            if (intent != null) {
                String action = intent.getAction();
                if (intent.hasCategory(Intent.CATEGORY_LAUNCHER) && Intent.ACTION_MAIN.equals(action)) {
                    finish();
                    return;
                }
            }
        }

        /*XXPermissions.with(AppStartActivity.this).permission(Permission.POST_NOTIFICATIONS).interceptor(new PermissionInterceptor()).request(new OnPermissionCallback() {
            @Override
            public void onGranted(List<String> permissions, boolean all) {
                if (!all) {
                    return;
                }
            }
        });*/

/*        XXPermissions.with(AppStartActivity.this).permission(Permission.READ_PHONE_STATE).interceptor(new PermissionInterceptor()).request(new OnPermissionCallback() {
            @Override
            public void onGranted(List<String> permissions, boolean all) {
                if (!all) {
                    return;
                }
            }
        });*/

        if (XYSPUtils.getBoolean(Constant.KEY_USER_NOTE_TIPS)) {
            agreeApp();
        } else {
            showUserNoteDialog();
        }
    }

    /**
     * 显示用户协议dialog
     */
    private void showUserNoteDialog() {
        UserNoteDialog userNoteDialog = UserNoteDialog.getInstance();

        userNoteDialog.setOnClickListener(new UserNoteDialog.OnUserNoteClickListener() {
            @Override
            public void onAgree() {
                agreeApp();
            }

            @Override
            public void onDisagree() {
                System.exit(0);
            }

            @Override
            public void onPolicyClick(String url) {
//                WebViewActivity.startActivity(AppStartActivity.this, "onPolicyClick", url);
                presenter.getAccordByNum(12);
            }

            @Override
            public void onServerClick(String url) {
//                WebViewActivity.startActivity(AppStartActivity.this, "onServerClick", url);
                presenter.getAccordByNum(10);
            }
        });
        userNoteDialog.show(getSupportFragmentManager(), "");
    }

    /**
     * 倒计时显示
     */
    private void countDown() {
        new CountDownTimer(3000, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                mSkipTv.setText((1 + millisUntilFinished / 1000) + "s");
            }

            @Override
            public void onFinish() {
                enterApp();
            }
        }.start();
    }

    /**
     * 退出app
     */
    private void enterApp() {
        Intent intent = new Intent(this, MainActivity.class);
//        if (JLSPUtils.getBoolean(Constant.KEY_APP_GUIDE_TAG))
//            intent = new Intent(this, MainActivity.class);
//        else
//            intent = new Intent(this, GuideActivity.class);

        if (null != intent) {
            startActivity(intent);
            finish();
        }
    }

    /**
     * 同意协议
     */
    private void agreeApp() {
        XYSPUtils.put(Common.KEY_USER_NOTE_TIPS, true);

        RCRTCFUBeautifierEngine.getInstance().register(AppStartActivity.this, null, authpack.A(), new FUBeautifierResultCallback() {
            @Override
            public void onSuccess() {
                LogUtil.e("RCRTCFUBeautifierEngine结果", "==========msg:::onSuccess()");
            }

            @Override
            public void onFailed(int code) {
                LogUtil.e("RCRTCFUBeautifierEngine结果", "==========msg:::onFailed()-->code=" + code);
            }
        });

        presenter.getVipInfo();
//        enterApp();

    }

    @OnClick({R.id.btn_skip})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.btn_skip:
                enterApp();
                break;
        }
    }

    @Override
    protected GeneralSettingPresenter setPresenter() {
        return new GeneralSettingPresenter(this);
    }

    @Override
    public void showGeneralSetting(GeneralSettingInfo info) {

    }

    @Override
    public void showWebInfo(WebBeanInfo info) {
//        AppShowOnePicActivity.startActivity(AppStartActivity.this,  info.getUrl());
        WebViewActivity.startActivity(AppStartActivity.this, "", info.getUrl());
    }

    @Override
    public void showDeleteCallback(BaseBean baseBean) {

        Intent intent = new Intent(this, MainActivity.class);
        if (null != intent) {
            startActivity(intent);
            finish();
        }
    }

    @Override
    public void showLogoutCallback(BaseBean uploadBean) {

    }

    @Override
    public void getSimiInfo(SimiInfo simiInfo) {

    }

    @Override
    public FragmentActivity context() {
        return this;
    }
}
