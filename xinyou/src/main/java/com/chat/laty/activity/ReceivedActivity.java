package com.chat.laty.activity;

import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.util.Log;

import androidx.fragment.app.FragmentActivity;

import com.hjq.permissions.OnPermissionCallback;
import com.hjq.permissions.Permission;
import com.hjq.permissions.XXPermissions;
import com.chat.laty.R;
import com.chat.laty.controllers.ReceiveCallController;
import com.chat.laty.dialog.TopReceiveCallDialog;
import com.chat.laty.entity.BaseBean;
import com.chat.laty.entity.RyUserInfo;
import com.chat.laty.entity.XinYouCallInfo;
import com.chat.laty.entity.event.FateSessionEvent;
import com.chat.laty.entity.event.RCCallPlusSessionEvent;
import com.chat.laty.entity.event.RCHangUpSessionEvent;
import com.chat.laty.entity.event.RCReceiveCallPlusSessionEvent;
import com.chat.laty.im.message.XYCallVideoContent;
import com.chat.laty.mvp.BasePresenterActivity;
import com.chat.laty.presenters.ReceiveCallPresenter;
import com.chat.laty.utils.LogUtil;
import com.chat.laty.utils.PermissionInterceptor;
import com.chat.laty.utils.SystemRoundUtils;
import com.chat.laty.utils.XYVideoUtils;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.List;

import cn.rongcloud.callplus.api.RCCallPlusClient;
import cn.rongcloud.callplus.api.RCCallPlusSession;
import io.rong.imkit.RongIM;
import io.rong.imlib.IRongCallback;
import io.rong.imlib.RongIMClient;
import io.rong.imlib.model.Conversation;
import io.rong.imlib.model.Message;

/**
 * <AUTHOR>
 * @date 2024/1/7 0:34
 * @description:
 */
public class ReceivedActivity extends BasePresenterActivity<ReceiveCallPresenter> implements ReceiveCallController.View {

    TopReceiveCallDialog mDialog;
    RCCallPlusSession mRCCallPlusSession;

    String mExtraStr;

    private int receiveType = -1;

    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    public void getCallPlushListenerEvent(RCReceiveCallPlusSessionEvent event) {
        mRCCallPlusSession = event.getSession();
        mExtraStr = event.getExtra();
        presenter.getUserInfo(mRCCallPlusSession.getCallerUserId());
        EventBus.getDefault().removeStickyEvent(RCReceiveCallPlusSessionEvent.class);
    }

    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    public void getFateSessionEvent(FateSessionEvent event) {
        finish();
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    public void getHangUpListenerEvent(RCHangUpSessionEvent event) {
        Log.d(TAG, "getHangUpListenerEvent ------------" + waitCall);
        if (waitCall && XYVideoUtils.getWaitSession() != null) {
            Log.d(TAG, "getHangUpListenerEvent ------------走这里");
            RCCallPlusClient.getInstance().accept(XYVideoUtils.getWaitSession().getCallId());
            if (null != mDialog) {
                mDialog.dismiss();
            }
            finish();
        } else {
            Log.d(TAG, "getHangUpListenerEvent ------------走else");
            if (null != mDialog) {
                mDialog.dismiss();
            }
            finish();
        }
    }

    public static void start(Context context) {
        Intent intent = new Intent(context, ReceivedActivity.class);
//        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        context.startActivity(intent);
    }


//    public static void start(Context context) {
//        Intent intent = new Intent(context, ReceivedActivity.class);
////        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
//        context.startActivity(intent);
//    }

    @Override
    protected void initDatas() {

    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_receive_call_layout;
    }

    boolean waitCall = false;

    @Override
    protected void initViews() {

        LogUtil.i(TAG, "intiViews()");
        if (XYVideoUtils.getWaitSession() != null) {
            mRCCallPlusSession = XYVideoUtils.getWaitSession();
            mExtraStr = "";
            presenter.getUserInfo(mRCCallPlusSession.getCallerUserId());
//            EventBus.getDefault().removeStickyEvent(RCReceiveCallPlusSessionEvent.class);
        } else {
            finish();
        }

        if (!TextUtils.isEmpty(mExtraStr)) {
            EventBus.getDefault().post(new FateSessionEvent(mExtraStr));
            finish();
//            FatePairingInfo base = GsonUtils.JsonToBean(mExtraStr, FatePairingInfo.class);
//            if (null != base && TextUtils.equals("1", base.getType())) {
//                String callId = mRCCallPlusSession.getCallId();
//                RCCallPlusClient.getInstance().accept(callId);
//                Toaster.show("配对成功");
//                SystemRoundUtils.cancelVibrator();
//                SystemRoundUtils.stopRing();
//
//                EventBus.getDefault().postSticky(new RCCallPlusSessionEvent(mRCCallPlusSession));
////                XYVideoUtils.session = XYVideoUtils.waitSession;
////                XYVideoUtils.waitSession = null;
////                XYVideoUtils.waitSessionExtra = null;
////                RCCallPlusClient.getInstance().hangup(currentCallSession.getCallId());
////                CallPhoneActivity.start(ReceivedActivity.this, callId, 1,type ,mExtraStr);
//            } else {
//                mDialog = new TopReceiveCallDialog(ReceivedActivity.this);
//                mDialog.setOnDialogCallbackListener(new TopReceiveCallDialog.OnDialogCallbackListener() {
//                    @Override
//                    public void onDialogSelectItem(int type) {
//                        SystemRoundUtils.cancelVibrator();
//                        SystemRoundUtils.stopRing();
//                        if (1 == type) {
////                            EventBus.getDefault().post(new XYStopVideoContent());
//                            int type1 = mRCCallPlusSession.getMediaType().getValue();
//                            EventBus.getDefault().postSticky(new RCCallPlusSessionEvent(mRCCallPlusSession));
////                            XYVideoUtils.session = XYVideoUtils.waitSession;
////                            XYVideoUtils.waitSession = null;
////                            XYVideoUtils.waitSessionExtra = null;
////                            RCCallPlusSession currentCallSession = RCCallPlusClient.getInstance().getCurrentCallSession();
////                            RCCallPlusClient.getInstance().hangup(currentCallSession.getCallId());
////                            CallPhoneActivity.start(ReceivedActivity.this, callId, 1,type1 ,mExtraStr);
//                        } else if (2 == type) {
//                            sendVideoMsg(new XinYouCallInfo("已拒绝", mRCCallPlusSession.getMediaType().getValue() + "", "", mRCCallPlusSession.getCallerUserId()), mRCCallPlusSession.getCallerUserId());
//                            RCCallPlusClient.getInstance().hangup(mRCCallPlusSession.getCallId());
//                        }
//                        mDialog.dismiss();
//                        finish();
//                    }
//                });
//                mDialog.show();
//            }
        } else {


            mDialog = new TopReceiveCallDialog(ReceivedActivity.this);
            mDialog.setOnDialogCallbackListener(new TopReceiveCallDialog.OnDialogCallbackListener() {
                @Override
                public void onDialogSelectItem(int type) {
                    SystemRoundUtils.cancelVibrator();
                    SystemRoundUtils.stopRing();
                    if (1 == type) {

                        if (null != mRCCallPlusSession)
                            presenter.setRedisUserKey(mRCCallPlusSession.getCallerUserId());
                        else if (null != XYVideoUtils.session) {
                            presenter.setRedisUserKey(XYVideoUtils.session.getCallerUserId());
                        }
//                        XYVideoUtils.session =  XYVideoUtils.waitSession;
                        if (XYVideoUtils.session != null) {
                            receiveType = 0;
//                            presenter.setRedisUserKey(mRCCallPlusSession.getCallerUserId());
//                            return;
                        } else {
                            receiveType = 1;
                        }

//                        EventBus.getDefault().post(new XYStopVideoContent());
//                        XYVideoUtils.session =  XYVideoUtils.waitSession;
//                        XYVideoUtils.waitSession = null;
//                        XYVideoUtils.waitSessionExtra = null;
//                        RCCallPlusSession currentCallSession = RCCallPlusClient.getInstance().getCurrentCallSession();
//                        RCCallPlusClient.getInstance().hangup(currentCallSession.getCallId());
//                        CallPhoneActivity.start(ReceivedActivity.this, callId, 1,type1 ,mExtraStr);
                    } else if (2 == type) {
                        sendVideoMsg(new XinYouCallInfo("已拒绝", mRCCallPlusSession.getMediaType().getValue() + "", "", mRCCallPlusSession.getCallerUserId()), mRCCallPlusSession.getCallerUserId());
                        RCCallPlusClient.getInstance().hangup(mRCCallPlusSession.getCallId());
                        mDialog.dismiss();
                        finish();
                    }


                }
            });
            mDialog.show();


        }

//                                 }
//                             }
//                    );


    }

    @Override
    public void showRedisUserKey(BaseBean data) {

        XXPermissions.with(ReceivedActivity.this).permission(Permission.RECORD_AUDIO, Permission.CAMERA).interceptor(new PermissionInterceptor())
                .request(new OnPermissionCallback() {
                    @Override
                    public void onGranted(List<String> permissions, boolean all) {
                        if (!all) {
                            return;
                        }

                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                if (200 == data.getCode()) {
                                    if (receiveType == 0) {
                                        waitCall = true;
                                        RCCallPlusClient.getInstance().hangup(XYVideoUtils.session.getCallId());
                                        EventBus.getDefault().postSticky(new RCCallPlusSessionEvent(mRCCallPlusSession));
                                    } else if (receiveType == 1) {
                                        if (XYVideoUtils.getWaitSession() != null && !TextUtils.isEmpty(XYVideoUtils.getWaitSession().getCallId())) {
                                            RCCallPlusClient.getInstance().accept(XYVideoUtils.getWaitSession().getCallId());
                                        }
                                    }
                                    if (null != mDialog)
                                        mDialog.dismiss();
                                    finish();
                                }
                            }
                        });
                    }
                });


    }

    public void sendVideoMsg(XinYouCallInfo callInfo, String userId) {
        Conversation.ConversationType conversationType = Conversation.ConversationType.PRIVATE;
        XYCallVideoContent messages = XYCallVideoContent.obtain(callInfo);
        Message message = Message.obtain(userId, conversationType, messages);
        message.setCanIncludeExpansion(true);
        RongIM.getInstance().sendMessage(message, null, null, new IRongCallback.ISendMediaMessageCallback() {
            @Override
            public void onProgress(Message message, int i) {

            }

            @Override
            public void onCanceled(Message message) {

            }

            @Override
            public void onAttached(Message message) {

            }

            @Override
            public void onSuccess(Message message) {

            }

            @Override
            public void onError(final Message message, final RongIMClient.ErrorCode errorCode) {

            }
        });
    }

    @Override
    protected boolean isUseEventBus() {
        return true;
    }

    @Override
    protected ReceiveCallPresenter setPresenter() {
        return new ReceiveCallPresenter(this);
    }

    @Override
    public void showUserInfo(RyUserInfo data) {
        if (null != mDialog) {
            mDialog.setUserInfo(data, mRCCallPlusSession.getMediaType());
        }
    }


    @Override
    public FragmentActivity context() {
        return this;
    }
}
