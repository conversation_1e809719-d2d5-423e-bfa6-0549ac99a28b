package com.chat.laty.activity;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.chat.laty.utils.ItemDecoration;
import com.hjq.toast.Toaster;
import com.chat.laty.R;
import com.chat.laty.controllers.CreditsController;
import com.chat.laty.dialog.BottomSelectFriendDialog;
import com.chat.laty.entity.CreditsFriendInfoModel;
import com.chat.laty.entity.MyCreditsInfoModel;
import com.chat.laty.mvp.BasePresenterActivity;
import com.chat.laty.presenters.MyCreditsPresenter;
import com.chat.laty.view.CustomActionBar;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import butterknife.OnClick;

public class MyCreditsActivity extends BasePresenterActivity<MyCreditsPresenter> implements CreditsController.IMyInfoView {

    public static void startAct(Context context) {
        Intent intent = new Intent(context, MyCreditsActivity.class);
        context.startActivity(intent);
    }

    @BindView(R.id.custom_bar)
    CustomActionBar customBar;

    @BindView(R.id.tv_credits)
    TextView tvCredits;
    @BindView(R.id.tv_today)
    TextView tvToday;
    @BindView(R.id.tv_friend)
    TextView tvFriend;

    @BindView(R.id.v_friend)
    View vFriend;

    @BindView(R.id.recycler_view)
    RecyclerView recyclerView;

    @Override
    protected void initDatas() {

    }

    CreditsAdapter adapter;

    @Override
    protected int getContentViewId() {
        return R.layout.activity_my_credits_layout;
    }

    MyCreditsInfoModel model;

    CreditsFriendInfoModel friend;

    List<MyCreditsInfoModel.MyCreditsItemModel> list = new ArrayList<>();
    int index = -1;

    @Override
    protected void initViews() {

        customBar.setTitleText("我的积分");
        customBar.setRightText("积分明细");
        customBar.setAllColor(ContextCompat.getColor(this, R.color.white));

        customBar.addFunction(CustomActionBar.FUNCTION_TEXT_RIGHT);
        customBar.setOnActionBarClickListerner(function -> {
            if (function == CustomActionBar.FUNCTION_BUTTON_LEFT) {
                MyCreditsActivity.this.finish();
                return true;
            } else if (function == CustomActionBar.FUNCTION_TEXT_RIGHT) {
                CreditsDetailActivity.startAct(MyCreditsActivity.this);
                return true;
            }
            return false;
        });

        adapter = new CreditsAdapter();
        recyclerView.setAdapter(adapter);
        if (recyclerView.getItemDecorationCount() == 0) {
            recyclerView.addItemDecoration(new ItemDecoration(this,0,7f,7f));
        }
        adapter.setOnItemClickListener((baseQuickAdapter, view, i) -> {
            if (index == i) return;
            if (index != -1) {
                list.get(index).selected = false;
                baseQuickAdapter.notifyItemChanged(index);
            }
            index = i;
            list.get(index).selected = true;
            baseQuickAdapter.notifyItemChanged(index);
        });

        presenter.getMyCreditsInfo();
//        presenter.getFriendList("");
    }


    @SuppressLint("NonConstantResourceId")
    @OnClick({R.id.v_friend, R.id.btn_confirm})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.v_friend:

                BottomSelectFriendDialog dialog = new BottomSelectFriendDialog(MyCreditsActivity.this,  friend);
                dialog.setOnCallbackListener(this::toggleFriendView);
                dialog.create();
                dialog.show();
                break;
            case R.id.btn_confirm:
                if (index == -1) {
                    Toaster.show("请选择赠送套餐");
                    return;
                }
                if (friend == null) {
                    Toaster.show("请选择赠送的好友");
                    return;
                }

                showProgressDialog(R.string.app_loadding);
                MyCreditsInfoModel.MyCreditsItemModel it = model.getIntegralList().get(index);
                presenter.give(it.getId(), friend.getId());
                break;
        }
    }

    @Override
    protected MyCreditsPresenter setPresenter() {
        return new MyCreditsPresenter(this, null);
    }

    @SuppressLint({"NotifyDataSetChanged", "SetTextI18n"})
    @Override
    public void getInfoSucceed(MyCreditsInfoModel data) {
        this.model = data;

        tvCredits.setText(data.getIntegralBalance() + "");
        tvToday.setText(data.getTodayIntegral() + "");

        list = data.getIntegralList();
        adapter.setItems(list);
        adapter.notifyDataSetChanged();
    }

    @Override
    public void getFriendListSucceed(List<CreditsFriendInfoModel> list,boolean refresh,boolean loadmore) {
//        friends = list;
    }

    @Override
    public void getInfoFailed() {
    }

    private void toggleFriendView(CreditsFriendInfoModel friend) {
        if (friend == null) {
            this.friend = null;
            tvFriend.setText("");
            tvFriend.setSelected(false);
            vFriend.setSelected(false);
        } else {
            this.friend = friend;
            tvFriend.setText(friend.getNickname());
            tvFriend.setSelected(true);
            vFriend.setSelected(true);
        }
    }

    @Override
    public void giveSucceed() {
        list.get(index).selected = false;
        adapter.notifyItemChanged(index);
        index = -1;
        friend = null;
        toggleFriendView(null);

        presenter.getMyCreditsInfo();
        dismissProgressDialog();
    }

    @Override
    public void giveFailed() {
        dismissProgressDialog();
    }

    @Override
    public FragmentActivity context() {
        return this;
    }


    private static class CreditsAdapter extends BaseQuickAdapter<MyCreditsInfoModel.MyCreditsItemModel, QuickViewHolder> {

        @Override
        protected void onBindViewHolder(@NonNull QuickViewHolder helper, int i, @Nullable MyCreditsInfoModel.MyCreditsItemModel item) {

            helper.setText(R.id.tv_value, item.getNum() + "");
            helper.itemView.setSelected(item.selected);

            helper.getView(R.id.tv_value).setSelected(item.selected);
            helper.getView(R.id.tv_label).setSelected(item.selected);
        }

        @NonNull
        @Override
        protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int i) {
            return new QuickViewHolder(R.layout.adapter_credits_layout, viewGroup);
        }
    }
}