package com.chat.laty.activity;

import android.content.Context;
import android.content.Intent;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;

import com.chat.laty.R;
import com.chat.laty.base.BaseActivity;
import com.chat.laty.utils.LogUtil;
import com.chat.laty.utils.PicassoUtils;
import com.chat.laty.view.CustomActionBar;

import butterknife.BindView;

/**
 * <AUTHOR>
 * @date 2023/6/10.
 * email：<EMAIL>
 * description： Webview界面
 */
public class FullImagePreActivity extends BaseActivity {
    private static final String EXTRA_KEY_TITLE = "extra_key_title";
    private static final String EXTRA_KEY_URL = "extra_key_url";
    @BindView(R.id.custom_bar)
    CustomActionBar actionBar;

    @BindView(R.id.iv_image)
    ImageView imageView;


    private String url;
    private String title;


    public static void startActivity(Context context, @NonNull String title, @NonNull String url) {
        Intent intent = new Intent(context, FullImagePreActivity.class);
        intent.putExtra(EXTRA_KEY_TITLE, title);
        intent.putExtra(EXTRA_KEY_URL, url);
        context.startActivity(intent);
    }

    @Override
    protected void initDatas() {

    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_image_pre_view;
    }

    @Override
    protected void initViews() {
        initData();
    }

    private void initData() {
        title = getIntent().getStringExtra(EXTRA_KEY_TITLE);
        url = getIntent().getStringExtra(EXTRA_KEY_URL);
        url = "https://img.jbzj.com/file_images/article/202104/2021427114319258.jpg?2021327114458";
        LogUtil.e("响应结果", "url==============" + url);
        actionBar.setTitleText(title);
        actionBar.setAllColor(ContextCompat.getColor(this,R.color.black));
        PicassoUtils.showImage(imageView, url);
    }
}
