package com.chat.laty.activity;

import android.content.Context;
import android.content.Intent;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.viewpager2.adapter.FragmentStateAdapter;
import androidx.viewpager2.widget.ViewPager2;

import com.google.android.material.tabs.TabLayout;
import com.chat.laty.R;
import com.chat.laty.base.BaseActivity;
import com.chat.laty.fragment.MasonryDetailFragment;
import com.chat.laty.view.CustomActionBar;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import butterknife.BindView;

public class MasonryDetailActivity extends BaseActivity {

    public static void start(Context context) {
        Intent intent = new Intent(context, MasonryDetailActivity.class);
        context.startActivity(intent);
    }

    @BindView(R.id.custom_bar)
    CustomActionBar customBar;
    @BindView(R.id.tab_layout)
    TabLayout tabLayout;

    @BindView(R.id.view_pager)
    ViewPager2 viewPager2;

    List<Fragment> fragments = new ArrayList<>();

    ArrayList<String> titles = new ArrayList<>(Arrays.asList("社交奖励", "推广奖励", "充值奖励", "会员奖励", "钻石详情"));

    @Override
    protected void initDatas() {
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_gold_coin_detail_layout;
    }

    @Override
    protected void initViews() {
        customBar.setTitleText("钻石明细");
        customBar.setAllColor(ContextCompat.getColor(this, R.color.black));

        customBar.setOnActionBarClickListerner(function -> {
            if (function == CustomActionBar.FUNCTION_BUTTON_LEFT) {
                MasonryDetailActivity.this.finish();
                return true;
            }
            return false;
        });

        for (int i = 0; i < titles.size(); i++) {
            View view = getLayoutInflater().inflate(R.layout.tab_layout_item_view, null);

            TextView textView = view.findViewById(R.id.tv_title);
            ImageView imageView = view.findViewById(R.id.iv_indicator);
            if (textView != null) {
                textView.setText(titles.get(i));
                toggleTextStyle(textView, i == 0);
            }
            if (imageView != null && i == 0) {
                imageView.setVisibility(View.VISIBLE);
            }

            TabLayout.Tab tab = tabLayout.newTab();
            tab.setCustomView(view);
            tabLayout.addTab(tab);

            fragments.add(MasonryDetailFragment.newInstance(i + 1));
        }

        Adapter adapter = new Adapter(this);
        viewPager2.setAdapter(adapter);
        viewPager2.setUserInputEnabled(false);
        tabLayout.addOnTabSelectedListener(new TabLayout.OnTabSelectedListener() {
            @Override
            public void onTabSelected(TabLayout.Tab tab) {
                toggleTextStyle(tab.getCustomView(), true);
                viewPager2.setCurrentItem(tab.getPosition());
            }

            @Override
            public void onTabUnselected(TabLayout.Tab tab) {
                toggleTextStyle(tab.getCustomView(), false);
            }

            @Override
            public void onTabReselected(TabLayout.Tab tab) {

            }
        });
    }


    private void toggleTextStyle(View view, boolean selected) {

        TextView textView = view.findViewById(R.id.tv_title);
        ImageView imageView = view.findViewById(R.id.iv_indicator);

        if (textView != null) {
            textView.setSelected(selected);
            textView.setTextSize(selected ? 18 : 15);
        }
        if (imageView != null) {
            imageView.setVisibility(selected ? View.VISIBLE : View.INVISIBLE);
        }
    }


    private class Adapter extends FragmentStateAdapter {


        public Adapter(@NonNull FragmentActivity fragmentActivity) {
            super(fragmentActivity);
        }

        @NonNull
        @Override
        public Fragment createFragment(int position) {
            return fragments.get(position);
        }

        @Override
        public int getItemCount() {
            return fragments.size();
        }
    }
}