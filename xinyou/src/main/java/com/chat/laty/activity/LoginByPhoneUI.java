package com.chat.laty.activity;

import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.view.View;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.ImageView;
import android.widget.LinearLayout;

import androidx.appcompat.widget.AppCompatButton;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.fragment.app.FragmentActivity;

import com.hjq.toast.Toaster;
import com.chat.laty.MainActivity;
import com.chat.laty.R;
import com.chat.laty.base.AppHelper;
import com.chat.laty.contants.Constant;
import com.chat.laty.controllers.LoginController;
import com.chat.laty.entity.LoginEntity;
import com.chat.laty.entity.LoinEntity;
import com.chat.laty.entity.RYTokenInfo;
import com.chat.laty.entity.WXLoginInfo;
import com.chat.laty.entity.WebBeanInfo;
import com.chat.laty.mvp.BasePresenterActivity;
import com.chat.laty.presenters.LoginPresenter;
import com.chat.laty.utils.XYSPUtils;
import com.chat.laty.view.ClearEditText;

import butterknife.BindView;
import butterknife.OnClick;
import io.rong.imkit.RongIM;

/**
 * <AUTHOR>
 * @date 2023/9/11 15:36
 * @description:登录注册
 */
public class LoginByPhoneUI extends BasePresenterActivity<LoginPresenter> implements LoginController.View {

    @BindView(R.id.edit_username)
    ClearEditText editUsername;
    @BindView(R.id.edit_passwd)
    ClearEditText editPasswd;
    @BindView(R.id.vcode_layout)
    LinearLayout vcodeLayout;
    @BindView(R.id.edit_vcode)
    ClearEditText editVcode;
    @BindView(R.id.get_vcode_tv)
    AppCompatButton getVcodeTv;

//    @BindView(R.id.login_find_pwd_tv)
//    AppCompatTextView findPwdTv;
    @BindView(R.id.invitation_code_layout)
    LinearLayout invitationCodeLayout;
    @BindView(R.id.edit_invitation_code)
    ClearEditText editInvitationCode;
    @BindView(R.id.login_button)
    Button loginButton;

    @BindView(R.id.agree_checkbox)
    CheckBox mAgreeCb;
//    @BindView(R.id.iv_login_bg)
//    ImageView loginBg;

    private int mCurrentType = 0;
    private boolean mIsAgree = false;


    public static void start(Context context) {
        Intent intent = new Intent(context, LoginByPhoneUI.class);
        context.startActivity(intent);
    }

    @Override
    protected void initDatas() {

    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_login_byphone;
    }

    @Override
    protected void initViews() {
        mAgreeCb.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                mIsAgree = isChecked;
            }
        });
    }


    @OnClick({R.id.login_button, R.id.get_vcode_tv, R.id.user_agreement_tv, R.id.privacy_policy_tv})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.login_button:

                AppHelper.hideSoftPad(LoginByPhoneUI.this);

                if (TextUtils.isEmpty(editUsername.getText().toString())) {
                    Toaster.show("请输入您的手机号");
                    return;
                }
                if (TextUtils.isEmpty(editVcode.getText().toString())) {
                    Toaster.show("请输入验证码");
                    return;
                }
                if (!mIsAgree) {
                    Toaster.show("请阅读并同意用户协议");
                    return;
                }
                RongIM.getInstance().logout();
                showProgressDialog(R.string.app_uploadding);
                getVcodeTv.setText("获取验证码");
                getVcodeTv.setEnabled(true);
                presenter.loginBySms(editUsername.getText().toString(), editVcode.getText().toString());
                break;

            case R.id.get_vcode_tv:
                if (TextUtils.isEmpty(editUsername.getText().toString())) {
                    Toaster.show("请输入手机号");
                    return;
                }
                presenter.getVcode(editUsername.getText().toString());
                break;

            case R.id.user_agreement_tv:
                presenter.getAccordByNum(10);
                break;

            case R.id.privacy_policy_tv:
                presenter.getAccordByNum(12);
                break;


        }
    }

    @Override
    protected LoginPresenter setPresenter() {
        return new LoginPresenter(this);
    }


    @Override
    public FragmentActivity context() {
        return this;
    }

    @Override
    public void showCountDown(long millis) {
        getVcodeTv.setText(millis + "S");
        getVcodeTv.setEnabled(false);
    }

    @Override
    public void showCountDownFinish() {
        getVcodeTv.setText("重新获取");
        getVcodeTv.setEnabled(true);
    }

    @Override
    public void showLogin(LoinEntity data) {
        if (null != data) {
            presenter.getRongCloudToken();
        }
    }

    @Override
    public void showRyToken(RYTokenInfo data) {
        if (null != data && !TextUtils.isEmpty(data.getToken())) {
            XYSPUtils.put(Constant.KEY_USER_RONGYUN_TOKEN, data.getToken());
            MainActivity.start(LoginByPhoneUI.this);
            finish();
        }
    }

    @Override
    public void showLoginCallBack(LoginEntity loginInfo) {

        if (null != loginInfo) {
            if (1 == loginInfo.getIsNewUser()) {
                ComplementUserInfoUI.start(LoginByPhoneUI.this, editInvitationCode.getText().toString());
                finish();
            } else {
                presenter.getRongCloudToken();
//                finish();
            }
        } else {
            dismissProgressDialog();
        }
    }

    @Override
    public void showWeiXinLogin(WXLoginInfo data) {

    }

    @Override
    public void showLoginImCallback() {
        dismissProgressDialog();
        MainActivity.start(LoginByPhoneUI.this);
        finish();
    }

    @Override
    public void showWebInfo(WebBeanInfo info) {
        WebViewActivity.startActivity(LoginByPhoneUI.this, "", info.getUrl());
    }

    @Override
    public void loadVipData() {
        dismissProgressDialog();
        MainActivity.start(LoginByPhoneUI.this);
        finish();
    }

}
