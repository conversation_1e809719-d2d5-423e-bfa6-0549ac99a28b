package com.chat.laty.activity;

import android.content.Context;
import android.content.Intent;

import androidx.annotation.NonNull;

import com.luck.picture.lib.photoview.PhotoView;
import com.chat.laty.R;
import com.chat.laty.base.BaseActivity;
import com.chat.laty.utils.PicassoUtils;

import butterknife.BindView;

/**
 * <AUTHOR>
 * @date 2024/5/20.
 */
public class AppShowOnePicActivity extends BaseActivity {
    private static final String EXTRA_KEY_URL = "extra_key_url";

    @BindView(R.id.app_bg_riv)
    PhotoView mAppBgIv;

    @Override
    protected void initDatas() {

    }

    public static void startActivity(Context context, @NonNull String url) {
        Intent intent = new Intent(context, AppShowOnePicActivity.class);
        intent.putExtra(EXTRA_KEY_URL, url);
        context.startActivity(intent);
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_app_show_one_pic_layout;
    }

    @Override
    protected void initViews() {

        PicassoUtils.showImage(mAppBgIv, getIntent().getStringExtra(EXTRA_KEY_URL));

    }
}
