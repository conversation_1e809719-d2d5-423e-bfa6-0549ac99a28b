package com.chat.laty.activity;

import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.RecyclerView;

import com.hjq.permissions.OnPermissionCallback;
import com.hjq.permissions.Permission;
import com.hjq.permissions.XXPermissions;
import com.chat.laty.utils.PermissionInterceptor;
import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;
import com.chat.laty.R;
import com.chat.laty.adapter.AccostLanguageAdapter;
import com.chat.laty.base.XYApplication;
import com.chat.laty.controllers.AccostLanguageController;
import com.chat.laty.entity.AccostLanguageInfo;
import com.chat.laty.entity.AccostManLanguageInfo;
import com.chat.laty.entity.BaseBean;
import com.chat.laty.entity.DiaplayPicInfo;
import com.chat.laty.entity.UploadImgInfo;
import com.chat.laty.entity.event.AccostLanguageEvent;
import com.chat.laty.mvp.BasePresenterActivity;
import com.chat.laty.presenters.AccostLanguagePresenter;
import com.chat.laty.utils.BgmPlayer;
import com.chat.laty.view.CustomActionBar;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;

/**
 * <AUTHOR>
 * @date 2023/12/25 19:32
 * @description:搭讪语设置
 */
public class AccostLanguageSettingUI extends BasePresenterActivity<AccostLanguagePresenter> implements AccostLanguageController.View, CustomActionBar.OnActionBarClickListerner {
    @BindView(R.id.custom_bar)
    CustomActionBar mActionBar;

    @BindView(R.id.activity_srl)
    SmartRefreshLayout mSmartRefreshView;
    @BindView(R.id.base_rv)
    RecyclerView mRecyclerView;

    AccostLanguageAdapter mAdapter;

    private int mCurrentPosition = -1;

    private BgmPlayer mBgmPlayer;

    public static void start(Context context) {
        Intent intent = new Intent(context, AccostLanguageSettingUI.class);
        context.startActivity(intent);
    }

    @Override
    protected void initDatas() {

    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_base_smartrefresh_recyclerview_layout;
    }

    @Override
    protected void initViews() {
        mActionBar.setTitleText("搭讪语设置");
        mActionBar.setTitleTextColor(getResources().getColor(R.color.color_333333));
        mActionBar.addFunction(CustomActionBar.FUNCTION_TEXT_RIGHT);
        mActionBar.setRightTextColor(ContextCompat.getColor(this, R.color.colorPrimary));
        mActionBar.setRightText("添加模板");

        mActionBar.setOnActionBarClickListerner(this);

        mAdapter = new AccostLanguageAdapter();
        mRecyclerView.setAdapter(mAdapter);

        mAdapter.setStateViewEnable(true);
        mAdapter.setStateView(createEmptyListView("暂无数据", R.mipmap.icon_nodata));

        mAdapter.addOnItemChildClickListener(R.id.delete_tv, (baseQuickAdapter, view, position) -> {
            if (XYApplication.appLogin(true)) {
                mCurrentPosition = position;
                AccostLanguageInfo info = (AccostLanguageInfo) baseQuickAdapter.getItem(position);
                presenter.deleteAccost(info.getId());
            }
        });
        mAdapter.addOnItemChildClickListener(R.id.use_template_tv, (baseQuickAdapter, view, position) -> {
            if (XYApplication.appLogin(true)) {
                AccostLanguageInfo info = (AccostLanguageInfo) baseQuickAdapter.getItem(position);
                presenter.setDefaultAccost(info.getId());
            }
        });
        mAdapter.addOnItemChildClickListener(R.id.accost_riv, (baseQuickAdapter, view, position) -> {
            if (XYApplication.appLogin(true)) {
                AccostLanguageInfo info = (AccostLanguageInfo) baseQuickAdapter.getItem(position);
                if (!TextUtils.isEmpty(info.getTempImg())) {
                    ArrayList<DiaplayPicInfo> mDiaplayPicInfos = new ArrayList<>();
                    mDiaplayPicInfos.add(new DiaplayPicInfo(info.getTempImg()));
                    DischargedPicBrowserActivity.start(AccostLanguageSettingUI.this, mDiaplayPicInfos, position);
                }
            }
        });

        mAdapter.addOnItemChildClickListener(R.id.edit_tv, (baseQuickAdapter, view, position) -> {
            if (XYApplication.appLogin(true)) {
                XXPermissions.with(AccostLanguageSettingUI.this).permission(Permission.RECORD_AUDIO).interceptor(new PermissionInterceptor()).request(new OnPermissionCallback() {
                    @Override
                    public void onGranted(@NonNull List<String> permissions, boolean allGranted) {
                        if (!allGranted) {
                            return;
                        }
                        AccostLanguageInfo info = (AccostLanguageInfo) baseQuickAdapter.getItem(position);
//                        if (TextUtils.equals("1", info.getStatus())) {
//                            Toaster.show("正在审核中，无法修改!");
//                            return;
//                        }
                        EventBus.getDefault().postSticky(new AccostLanguageEvent(info));
                        AddAccostLanguageUI.start(AccostLanguageSettingUI.this);
                    }
                });
            }
        });

        mAdapter.addOnItemChildClickListener(R.id.accost_language_play_tv, (baseQuickAdapter, view, position) -> {
            if (XYApplication.appLogin(true)) {
                AccostLanguageInfo info = (AccostLanguageInfo) baseQuickAdapter.getItem(position);
                if (null == mBgmPlayer) {
                    mBgmPlayer = BgmPlayer.getInstance(AccostLanguageSettingUI.this);
                }

                if (mBgmPlayer.isPlay()) {
                    mBgmPlayer.stop();
                }
                mBgmPlayer.playNet(info.getVoiceUrl());

            }
        });

        mSmartRefreshView.setOnRefreshListener(new OnRefreshListener() {
            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                presenter.getAccostWomanList();
            }
        });
    }

    @Override
    protected AccostLanguagePresenter setPresenter() {
        return new AccostLanguagePresenter(this);
    }

    @Override
    public FragmentActivity context() {
        return this;
    }

    @Override
    public boolean onActionBarClickListener(int function) {
        switch (function) {
            case CustomActionBar.FUNCTION_BUTTON_LEFT:
                finish();
                break;

            case CustomActionBar.FUNCTION_TEXT_RIGHT:
//                XXPermissions.with(AccostLanguageSettingUI.this).permission(Permission.RECORD_AUDIO).request(new OnPermissionCallback() {
//                    @Override
//                    public void onGranted(@NonNull List<String> permissions, boolean allGranted) {
//                        if (!allGranted) {
//                            return;
//                        }
//                        AddAccostLanguageUI.start(AccostLanguageSettingUI.this);
//                    }
//                });
                AddAccostLanguageUI.start(AccostLanguageSettingUI.this);

                break;
        }
        return false;
    }

    @Override
    protected void onResume() {
        super.onResume();
        mActionBar.setRightTextColor(ContextCompat.getColor(this, R.color.colorPrimary));
        presenter.getAccostWomanList();
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (null != mBgmPlayer)
            mBgmPlayer.stop();
    }

    @Override
    public void showUploadImgResult(List<UploadImgInfo> data) {

    }

    @Override
    public void showAccostWomanList(List<AccostLanguageInfo> accostInfo) {
        mSmartRefreshView.finishRefresh();
        mAdapter.setItems(accostInfo);
        mAdapter.notifyDataSetChanged();
    }

    @Override
    public void showAccostManList(AccostManLanguageInfo accostInfo) {

    }

    @Override
    public void showupdateCallback(int code) {

    }

    @Override
    public void showDeleteCallback(Integer code) {
        if (200 == code) {
            mAdapter.removeAt(mCurrentPosition);
        }
    }

    @Override
    public void showDefaultCallback(Integer code) {
        if (200 == code) {
            presenter.getAccostWomanList();
        }
    }

    @Override
    public void showCountDownFinish() {

    }

    @Override
    public void showUploadAudioResult(List<String> result) {

    }

    @Override
    public void showCountDownTravel(int l) {

    }

    @Override
    public void showUpdateCallback(BaseBean base) {

    }


    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (null != mBgmPlayer)
            mBgmPlayer.release();
    }
}
