package com.chat.laty.activity;

import android.annotation.SuppressLint;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.graphics.drawable.Drawable;
import android.hardware.Sensor;
import android.hardware.SensorEvent;
import android.hardware.SensorEventListener;
import android.hardware.SensorManager;
import android.os.IBinder;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.FrameLayout;

import androidx.appcompat.widget.AppCompatTextView;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentActivity;

import com.faceunity.core.enumeration.FUInputTextureEnum;
import com.faceunity.nama.FURenderer;
import com.faceunity.nama.data.FaceUnityDataFactory;
import com.chat.laty.entity.BaseBean;
import com.chat.laty.face.db.FaceSettingHelper;
import com.chat.laty.face.listener.FaceSettingListener;
import com.chat.laty.face.ui.FaceUnityView;
import com.google.gson.Gson;
import com.hjq.permissions.OnPermissionCallback;
import com.hjq.permissions.Permission;
import com.hjq.permissions.XXPermissions;
import com.hjq.toast.Toaster;
import com.lxj.xpopup.XPopup;
import com.lxj.xpopup.interfaces.OnConfirmListener;
import com.makeramen.roundedimageview.RoundedImageView;
import com.opensource.svgaplayer.SVGACallback;
import com.opensource.svgaplayer.SVGAImageView;
import com.opensource.svgaplayer.SVGAParser;
import com.opensource.svgaplayer.SVGAVideoEntity;
import com.chat.laty.R;
import com.chat.laty.base.XYApplication;
import com.chat.laty.contants.Constant;
import com.chat.laty.controllers.CallPhoneController;
import com.chat.laty.dialog.BottomGiftDialog;
import com.chat.laty.dialog.TopReceiveCallDialog;
import com.chat.laty.entity.BalanceInfo;
import com.chat.laty.entity.BlockMessageInfo;
import com.chat.laty.entity.FatePairingInfo;
import com.chat.laty.entity.GiftInfo;
import com.chat.laty.entity.RyUserInfo;
import com.chat.laty.entity.VideoTipsInfo;
import com.chat.laty.entity.XinYouCallInfo;
import com.chat.laty.entity.event.CallPlushListenerEvent;
import com.chat.laty.entity.event.FatePairingEvent;
import com.chat.laty.entity.event.FateSessionEvent;
import com.chat.laty.entity.event.RCCallPlusSessionEvent;
import com.chat.laty.entity.event.RCHangUpSessionEvent;
import com.chat.laty.entity.event.RCReceiveCallPlusSessionEvent;
import com.chat.laty.entity.event.RYCallConnectedEvent;
import com.chat.laty.im.message.XYCallVideoContent;
import com.chat.laty.im.message.XYGiftContent;
import com.chat.laty.im.message.XYStopVideoContent;
import com.chat.laty.mvp.BasePresenterActivity;
import com.chat.laty.presenters.CallPhonePresenter;
import com.chat.laty.service.FloatVideoWindowService;
import com.chat.laty.utils.BgmPlayer;
import com.chat.laty.utils.DateUtil;
import com.chat.laty.utils.LogUtil;
import com.chat.laty.utils.PermissionInterceptor;
import com.chat.laty.utils.PicassoUtils;
import com.chat.laty.utils.XYSPUtils;
import com.chat.laty.utils.XYVideoUtils;
import com.chat.laty.utils.net.Common;
import com.chat.laty.utils.net.GsonUtils;
import com.chat.laty.view.CallMarqueeTextView;
import com.chat.laty.view.CustomActionBar;
import com.chat.laty.view.RotateAnimator;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.jetbrains.annotations.NotNull;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;

import butterknife.BindView;
import butterknife.OnClick;
import cn.rongcloud.callplus.api.RCCallPlusClient;
import cn.rongcloud.callplus.api.RCCallPlusCode;
import cn.rongcloud.callplus.api.RCCallPlusLocalVideoView;
import cn.rongcloud.callplus.api.RCCallPlusMediaType;
import cn.rongcloud.callplus.api.RCCallPlusPushConfig;
import cn.rongcloud.callplus.api.RCCallPlusRemoteVideoView;
import cn.rongcloud.callplus.api.RCCallPlusRenderMode;
import cn.rongcloud.callplus.api.RCCallPlusSession;
import cn.rongcloud.callplus.api.RCCallPlusType;
import cn.rongcloud.callplus.api.RCCallPlusUser;
import cn.rongcloud.callplus.api.callback.IRCCallPlusResultListener;
import cn.rongcloud.fubeautifier.FUBeautifierResultCallback;
import cn.rongcloud.fubeautifier.RCRTCFUBeautifierEngine;
import cn.rongcloud.rtc.api.RCRTCEngine;
import cn.rongcloud.rtc.base.RCRTCParamsType;
import cn.xjc_soft.lib_utils.LibCollections;
import io.rong.imkit.IMCenter;
import io.rong.imkit.RongIM;
import io.rong.imlib.IRongCallback;
import io.rong.imlib.IRongCoreListener;
import io.rong.imlib.RongCoreClient;
import io.rong.imlib.RongIMClient;
import io.rong.imlib.listener.OnReceiveMessageWrapperListener;
import io.rong.imlib.model.BlockedMessageInfo;
import io.rong.imlib.model.Conversation;
import io.rong.imlib.model.Message;
import io.rong.imlib.model.ReceivedProfile;

/**
 * <AUTHOR>
 * @date 2023/12/5 15:29
 * @description:打电话电话界面
 */
public class CallPhoneActivity extends BasePresenterActivity<CallPhonePresenter> implements CustomActionBar.OnActionBarClickListerner, CallPhoneController.View {

    private static final String TAG = "CallPhoneActivity";

    public static final String KEY_USER_ID = "KEY_USER_ID";
    public static final String KEY_CALL_TYPE = "KEY_CALL_TYPE";//拨打类型 0是语音 1是视频
    public static final String KEY_ENTER_TYPE = "KEY_ENTER_TYPE";//进入类型 1是接听 非1是拨打
    public static final String KEY_ENTER_EXTRA = "KEY_ENTER_EXTRA";//扩展字段
    public static final String KEY_REMAIN_TIME = "KEY_REMAIN_TIME";//通话剩余时间
    private boolean isHangUpClicked = false;//防止重复点击

    private boolean isMicrophoneEnabled = true; // 默认麦克风开启

    @BindView(R.id.local)
    FrameLayout mLocalVideoViewFrameLayout;

    @BindView(R.id.custom_bar)
    CustomActionBar mActionBar;

    @BindView(R.id.remote)
    FrameLayout mRemoteVideoViewFrameLayout;

    @BindView(R.id.confinement_time_tv)
    AppCompatTextView mConfinementTimeTv;

    @BindView(R.id.balance_time_tv)
    AppCompatTextView mBalanceTimeTv;

    @BindView(R.id.chat_bg)
    RoundedImageView mChatBg;

    @BindView(R.id.svgaImage)
    SVGAImageView mGiftIV;

    @BindView(R.id.faceunity_control)
    FaceUnityView mFaceUnityView;

    @BindView(R.id.beaut_tv)
    View mBeautTv;

    @BindView(R.id.msg_tips_layout)
    CallMarqueeTextView mVideoTipsTv;

    @BindView(R.id.microphone_switch)
    AppCompatTextView mMicrophoneSwitchTv;

    @BindView(R.id.recharge_button)
    AppCompatTextView mRechargeBtn;

    @BindView(R.id.super_view)
    View mSuperView;

    private String mUserId;
    private int mEnterType;//0呼叫  1接听
    private int mCallType;//0语音  1视频
    private int isVideoing;//是否视频中 1是
    private long remainTime;//剩余时间

    RCCallPlusSession mCallSession;
    RCCallPlusSession mReceiveRCCallPlusSession;

    private TimerTask mStartTimeTask;
    private Timer mStartTimeTimer;

    private boolean isChange;

    TopReceiveCallDialog mDialog;

    private long mCallStartTime;

    private boolean mServiceBound = false;

    BottomGiftDialog mBottomRvDialog;

    FatePairingInfo fatePairingInfo;

    private FURenderer mFURenderer;
    //
    private FaceUnityDataFactory mFaceUnityDataFactory;
    private SensorManager mSensorManager;

    BgmPlayer mBgmPlayer;

    private FaceSettingListener mFaceSettingListener;
    private final FaceSettingHelper mFaceSettingHelper = new FaceSettingHelper();

    private List<String> mUnGifts = new ArrayList<>();

    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    public void getShortPlayInfo(RCCallPlusSessionEvent event) {

        LogUtil.e(TAG, "呼叫ID--->通话id---》已接听房id-->" + event.getSession().getCallId());
//<<<<<<< HEAD
////        LogUtil.i("视频接听", "这里触发");
////        if(event.getSession() != null) {
////            mCallType = 1;//修改成接听状态
////            XYVideoUtils.session = event.getSession();
////            RCCallPlusClient.getInstance().accept(callSession.getCallId());
////        }
//        finish();
//=======
        LogUtil.i("视频接听", "这里触发");
        mEnterType = 1;//修改成接听状态
        mCallSession = event.getSession();
        mUserId = mCallSession.getCallerUserId();
//>>>>>>> master
        EventBus.getDefault().removeStickyEvent(RCCallPlusSessionEvent.class);
    }

    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    public void getShortPlayInfo(FatePairingEvent event) {
        fatePairingInfo = event.getFatePairingInfo();
        EventBus.getDefault().removeStickyEvent(FatePairingEvent.class);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void getGiftInfo(GiftInfo giftInfo) {
        mUnGifts.add(giftInfo.getTrendsImg());
        if (mGiftIV.getVisibility() != View.VISIBLE) {
            playGifts();
        }
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    public void getGiftInfo(XYGiftContent giftInfo) {
        if (!mUnGifts.contains(giftInfo.getGiftTrendsImg()))
            mUnGifts.add(giftInfo.getGiftTrendsImg());
        if (mGiftIV.getVisibility() != View.VISIBLE) {
            playGifts();
        }
    }

    private void playGifts() {
        if (!LibCollections.isEmpty(mUnGifts)) loadAnimation(mUnGifts.get(0));
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onCallConnected(RYCallConnectedEvent sessionEvent) {
        RCCallPlusSession callSession = sessionEvent.session;
//        XYVideoUtils.callStartTime = System.currentTimeMillis();
//        XYVideoUtils.session = callSession;
        if (callSession != null) {
//            mCallSession = callSession;
            updCallTime();
            videoType = 1;
            setRemoteUserVideoView(callSession.getRemoteUserList());
            setLocalVideoView();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onReceivedCall(RCReceiveCallPlusSessionEvent sessionEvent) {
        RCCallPlusSession callSession = sessionEvent.getSession();
        RCCallPlusSession currentCallSession = RCCallPlusClient.getInstance().getCurrentCallSession();
        if (currentCallSession != null && !TextUtils.equals(callSession.getCallId(), currentCallSession.getCallId())) {
            //可以使用该方法判断出，有正在进行中的通话，又有第二通通话呼入的情况
            //todo 第二通通话可以直接调用 RCCallPlusClient.getInstance().accept 方法接听，SDK内部会将第一通通话挂断
//            runOnUiThread(new Runnable() {
//                @Override
//                public void run() {
////                    showReceiveDialog(callSession);
//                }
//            });
//            startVideoService(false);
//            finish();
        }


//        XYSPUtils.put(Constant.KEY_USER_FLOAT_WIN, true);
        //最小化Activity
//        XYVideoUtils.start(XYApplication.getAppApplicationContext(), mEnterType, mCallType, mUserId);
//        finish();
    }

    @Override
    public void finish() {
        Log.i(TAG, "测试电话关闭了吗");
        super.finish();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onCallEnded(RCHangUpSessionEvent sessionEvent) {
        LogUtil.e(TAG, "onCallEnded--->" + sessionEvent.getSession().getCallId());
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                RCCallPlusSession session = sessionEvent.getSession();
                RCCallPlusSession currentCallSession = XYVideoUtils.session;
                if (null == currentCallSession || (session != null && currentCallSession.getCallId() == session.getCallId())) {
                    mBgmPlayer.stop();
                    Toaster.show("通话结束");
                    cancelStartTimeTask();
                    finish();
                } else {
                    switchVideo();
                }
            }
        });
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void callEnd(XYStopVideoContent content) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
//                EventBus.getDefault().post(new CallPlushListenerEvent(1));
                cancelStartTimeTask();
                finish();
            }
        });
    }

    public static void start(Context context, String userId, int enterType, int callType) {
        Intent intent = new Intent(context, CallPhoneActivity.class);
        intent.putExtra(KEY_USER_ID, userId);
        intent.putExtra(KEY_ENTER_TYPE, enterType);
        intent.putExtra(KEY_CALL_TYPE, callType);
//        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        context.startActivity(intent);
    }

    public static void start(Context context, String userId, int enterType, int callType, String extra) {
        Intent intent = new Intent(context, CallPhoneActivity.class);
        intent.putExtra(KEY_USER_ID, userId);
        intent.putExtra(KEY_ENTER_TYPE, enterType);
        intent.putExtra(KEY_CALL_TYPE, callType);
        intent.putExtra(KEY_ENTER_EXTRA, extra);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        context.startActivity(intent);
    }

    @Override
    protected void initDatas() {
        //应用运行时，保持屏幕高亮，不锁屏
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN, WindowManager.LayoutParams.FLAG_FULLSCREEN);
        //获取用户设置美颜数据
        String userId = XYSPUtils.getString(Common.KEY_APP_USER_RY_ID);
        mFaceSettingListener = new FaceSettingListener(mFaceSettingHelper.getFaceSetting(userId, true));
        mBgmPlayer = BgmPlayer.getInstance(XYApplication.getAppApplicationContext());
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_call_plus_layout;
    }


    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        mEnterType = getIntent().getIntExtra(KEY_ENTER_TYPE, 0);
        mCallType = getIntent().getIntExtra(KEY_CALL_TYPE, 0);
        mUserId = getIntent().getStringExtra(KEY_USER_ID);
        isVideoing = getIntent().getIntExtra("is_video_ing", 0);
        remainTime = getIntent().getLongExtra(KEY_REMAIN_TIME, 0);
        Log.i(TAG, "调用了intent===>" + new Gson().toJson(intent));
        Log.i(TAG, "调用了intent===>" + new Gson().toJson(intent));
        Log.i(TAG, "调用了intent===>" + new Gson().toJson(intent));
    }

    private void showMicrophoneSwitch(int callType) {
        mMicrophoneSwitchTv.setVisibility(0 == callType ? View.VISIBLE : View.GONE);
    }


    @Override
    protected void initViews() {
        mActionBar.addFunction(CustomActionBar.FUNCTION_BUTTON_RIGHT);
        mActionBar.setRightBackground(getDrawable(R.mipmap.chuangkou));
        mActionBar.setOnActionBarClickListerner(this);
        mActionBar.setTitleTextColor(getResources().getColor(R.color.color_white));
        mEnterType = getIntent().getIntExtra(KEY_ENTER_TYPE, 0);
        mCallType = getIntent().getIntExtra(KEY_CALL_TYPE, 0);
        mUserId = getIntent().getStringExtra(KEY_USER_ID);
        isVideoing = getIntent().getIntExtra("is_video_ing", 0);
        remainTime = getIntent().getLongExtra(KEY_REMAIN_TIME, 0);

        LogUtil.i(TAG, String.format("视频打开 calltype==%s,,, mEnterType == %s, userId === %s, extra === %s", mCallType, mEnterType, mUserId, getIntent().getStringExtra(KEY_ENTER_EXTRA)));

        videoType = XYVideoUtils.session != null ? 1 : 0;
        if (mCallType != 0) {
            getWindow().setFlags(WindowManager.LayoutParams.FLAG_SECURE, WindowManager.LayoutParams.FLAG_SECURE);
        }

        XYVideoUtils.stop(this);
        mVideoTipsTv.setVisibility(0 == mCallType ? View.GONE : View.VISIBLE);

        presenter.getVideoMsg();

        RongCoreClient.addOnReceiveMessageListener(receiveMsg);

        XXPermissions.with(CallPhoneActivity.this).permission(Permission.RECORD_AUDIO).permission(Permission.CAMERA).interceptor(new PermissionInterceptor()).request(new OnPermissionCallback() {
            @Override
            public void onGranted(List<String> permissions, boolean all) {
                if (!all) {
                    return;
                }
                setLocalVideoView();
            }
        });

        RongIMClient.getInstance().setMessageBlockListener(blockListener);

        RCRTCFUBeautifierEngine.getInstance().setBeautyEnable(true, new FUBeautifierResultCallback() {
            @Override
            public void onSuccess() {
                mFaceUnityView.setSettingListener(mFaceSettingListener);
            }

            @Override
            public void onFailed(int code) {
                Toaster.show("美颜打开失败--->code==" + code);
            }
        });

        LogUtil.i(TAG, String.format("视频打开 calltype==%s,,, mEnterType == %s, userId === %s", mCallType, mEnterType, mUserId));

        if (1 == mCallType) {
            mLocalVideoViewFrameLayout.setVisibility(View.VISIBLE);
            mRemoteVideoViewFrameLayout.setVisibility(View.VISIBLE);
            setLocalVideoView();

            RCRTCEngine.getInstance().getDefaultAudioStream().setAudioQuality(RCRTCParamsType.AudioQuality.MUSIC, RCRTCParamsType.AudioScenario.MUSIC_CHATROOM);
        } else {
            PicassoUtils.showImage(mChatBg, "https://nimg.ws.126.net/?url=http%3A%2F%2Fspider.ws.126.net%2Fcfd02c685a0e39cad8dc838e0273423d.jpeg&thumbnail=660x2147483647&quality=80&type=jpg");
            mLocalVideoViewFrameLayout.setVisibility(View.GONE);
            mRemoteVideoViewFrameLayout.setVisibility(View.GONE);
            mFaceUnityView.setVisibility(View.GONE);
            mBeautTv.setVisibility(View.GONE);

            RCRTCEngine.getInstance().getDefaultAudioStream().setAudioQuality(RCRTCParamsType.AudioQuality.SPEECH, RCRTCParamsType.AudioScenario.DEFAULT);
        }

        if (1 == mEnterType) {
            acceptCall(mUserId);
        } else {
            startCall(mUserId);
        }

        if (1 == isVideoing) {
            updCallTime();
            if (XYVideoUtils.session != null)
                setRemoteUserVideoView(XYVideoUtils.session.getRemoteUserList());
        } else {
            updCallTime();
            if (XYVideoUtils.session != null)
                setRemoteUserVideoView(XYVideoUtils.session.getRemoteUserList());
        }

        mFURenderer = FURenderer.getInstance();
        mFURenderer.setInputTextureType(FUInputTextureEnum.FU_ADM_FLAG_COMMON_TEXTURE);
        mFURenderer.setMarkFPSEnable(true);
        mFaceUnityDataFactory = FaceUnityDataFactory.getInstance();
        mFaceUnityView.bindDataFactory(mFaceUnityDataFactory);
        mFaceSettingHelper.initFaceSettingData(mFaceUnityDataFactory, mFaceSettingListener.getFaceSetting());
        mFaceUnityView.postDelayed(() -> {
            mFaceUnityView.setFaceBeautyControlChecked(mFaceSettingListener.getFaceSetting().getEnableFaceBeauty());
        }, 200);

        mSensorManager = (SensorManager) getSystemService(SENSOR_SERVICE);
        Sensor sensor = mSensorManager.getDefaultSensor(Sensor.TYPE_ACCELEROMETER);
        mSensorManager.registerListener(new SensorEventListener() {
            @Override
            public void onSensorChanged(SensorEvent event) {
                float x = event.values[0];
                float y = event.values[1];
                if (Math.abs(x) > 3 || Math.abs(y) > 3) {
                    if (Math.abs(x) > Math.abs(y)) {
                        mFURenderer.setDeviceOrientation(x > 0 ? 0 : 180);
                    } else {
                        mFURenderer.setDeviceOrientation(y > 0 ? 90 : 270);
                    }
                }
            }

            @Override
            public void onAccuracyChanged(Sensor sensor, int accuracy) {

            }
        }, sensor, SensorManager.SENSOR_DELAY_NORMAL);
        mFaceUnityView.setSettingListener(mFaceSettingListener);

        mLocalVideoViewFrameLayout.setOnClickListener(v -> {
            LogUtil.i("TAG", "点击了mLocalVideoViewFrameLayout");
            videoType = videoType == 0 ? 1 : 0;
            switchVideo();
        });

        mRemoteVideoViewFrameLayout.setOnClickListener(v -> {
            LogUtil.i("TAG", "点击了mRemoteVideoViewFrameLayout");
            videoType = videoType == 0 ? 1 : 0;
            switchVideo();
        });
        EventBus.getDefault().post(new FateSessionEvent(""));

        if (!TextUtils.isEmpty(getIntent().getStringExtra(KEY_ENTER_EXTRA))) {
            FatePairingInfo base = GsonUtils.JsonToBean(getIntent().getStringExtra(KEY_ENTER_EXTRA), FatePairingInfo.class);
            if (null != base && TextUtils.equals("1", base.getType())) {
                presenter.submitRoom(base.getUserId(), XYSPUtils.getString(Common.KEY_APP_USER_RY_ID), mUserId);
            }
        }

        if (remainTime != 0) startBalanceCountDown(remainTime);

        // 初始化麦克风状态
        updateMicrophoneButtonUI();
    }


    private void switchVideo() {
        mFaceUnityView.setVisibility(View.GONE);
        if (videoType == 0) {
            setLocalVideoView();
//            if (mCallSession != null) {
//                setRemoteUserVideoView(mCallSession.getRemoteUserList());
            if (XYVideoUtils.session != null) {
                setRemoteUserVideoView(XYVideoUtils.session.getRemoteUserList());
            } else {
                FrameLayout videoView = mRemoteVideoViewFrameLayout;
                videoView.removeAllViews();
            }
        } else {
//            if (mCallSession != null) {
//                setRemoteUserVideoView(mCallSession.getRemoteUserList());

            if (XYVideoUtils.session != null) {
                setRemoteUserVideoView(XYVideoUtils.session.getRemoteUserList());
            } else {
                FrameLayout videoView = mLocalVideoViewFrameLayout;
                videoView.removeAllViews();
            }

            setLocalVideoView();
        }
    }

    private int videoType = 0; //0 大窗显示对方 小窗显示对方


    IRongCoreListener.MessageBlockListener blockListener = new IRongCoreListener.MessageBlockListener() {
        @Override
        public void onMessageBlock(BlockedMessageInfo info) {
            LogUtil.e("响应结果", "==========CallPhoneActivity-->BlockedMessageInfo:" + info.toString());
            BlockMessageInfo msgInfo = GsonUtils.JsonToBean(info.getExtra(), BlockMessageInfo.class);
            switchTipInfo(msgInfo);
        }
    };

    OnReceiveMessageWrapperListener receiveMsg = new OnReceiveMessageWrapperListener() {
        @Override
        public void onReceivedMessage(Message message, ReceivedProfile profile) {
            LogUtil.e("响应结果", "==========onReceivedMessage-->" + message.getObjectName());
            if (TextUtils.equals("app:giftcontent", message.getObjectName())) {
                XYGiftContent gift = (XYGiftContent) message.getContent();
                EventBus.getDefault().post(gift);
            }
            BlockMessageInfo msgInfo = GsonUtils.JsonToBean(message.getContent().getExtra(), BlockMessageInfo.class);
            switchTipInfo(msgInfo);
        }

        @Override
        public boolean onReceived(Message message, ReceivedProfile profile) {
            return super.onReceived(message, profile);
        }
    };

    private void switchTipInfo(BlockMessageInfo msgInfo) {
        if (null != msgInfo) {
            switch (msgInfo.getType()) {
                case "1"://金币不足
                    if (XYApplication.getCurrentIsMan()) {
                        Toaster.show("剩余金币不足，一分钟后将被挂断通话，请及时充值！");
                        GoldCoinActivity.startAct(CallPhoneActivity.this);
                    } else {
                        Toaster.show("男用户剩余金币不足，一分钟后将被挂断通话，请提醒对方进行充值！");
                    }

                    mRechargeBtn.setVisibility(View.VISIBLE);
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            startBalanceCountDown(60 * 1000);
                        }
                    });
                    break;

                case "2":
                    Toaster.show(msgInfo.getMessage());
                    break;

                case "3"://违规
                    LogUtil.e("响应结果", "==========违规");
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            Toaster.show(msgInfo.getMessage());
                            presenter.countDown();
                            RCCallPlusClient.getInstance().stopCamera();
                            mConfinementTimeTv.setVisibility(View.VISIBLE);
                            mLocalVideoViewFrameLayout.removeAllViews();
                            mRemoteVideoViewFrameLayout.removeAllViews();
                        }
                    });
                    break;
            }
        }

    }

    private void startBalanceCountDown(long remainTime) {
        mBalanceTimeTv.setVisibility(View.VISIBLE);
        presenter.balanceCountDown(remainTime);
    }

    private void updCallTime() {
        cancelStartTimeTask();
        mStartTimeTask = new TimerTask() {
            @Override
            public void run() {
                runOnUiThread(new Runnable() {
                    @SuppressLint("DefaultLocale")
                    @Override
                    public void run() {
                        if (XYVideoUtils.callStartTime <= -1) {
                            return;
                        }
                        long duration = XYVideoUtils.getCallTime() / 1000;
                        LogUtil.i(TAG, "duration===>" + duration);
                        mVideoTipsTv.setFocusable(true);
                        if (duration >= 3600) {
                            mActionBar.setTitleText(String.format("%d:%02d:%02d", duration / 3600, (duration % 3600) / 60, (duration % 60)));
                        } else {
                            mActionBar.setTitleText(String.format("%02d:%02d", (duration % 3600) / 60, (duration % 60)));
                        }
                        mVideoTipsTv.setFocusable(true);
                    }
                });
            }
        };
        mStartTimeTimer = new Timer();
        mStartTimeTimer.schedule(mStartTimeTask, 0, 1000);

    }


    /**
     * 通话时间
     */
    @SuppressLint({"DefaultLocale", "SetTextI18n"})
    private void callTime(long callStartTime) {
        mCallStartTime = callStartTime;
        cancelStartTimeTask();
        mStartTimeTask = new TimerTask() {
            @Override
            public void run() {
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        long duration = (System.currentTimeMillis() - callStartTime) / 1000;
                        if (duration >= 3600) {
                            mActionBar.setTitleText(String.format("%d:%02d:%02d", duration / 3600, (duration % 3600) / 60, (duration % 60)));
                        } else {
                            mActionBar.setTitleText(String.format("%02d:%02d", (duration % 3600) / 60, (duration % 60)));
                        }
                    }
                });
            }
        };
        mStartTimeTimer = new Timer();
        mStartTimeTimer.schedule(mStartTimeTask, 0, 1000);
    }

    void cancelStartTimeTask() {
        if (mStartTimeTask != null) {
            mStartTimeTask.cancel();
            mStartTimeTask = null;
        }
        if (mStartTimeTimer != null) {
            mStartTimeTimer.cancel();
            mStartTimeTimer = null;
        }
    }

    /**
     * 设置本地视频渲染视图
     */
    private void setLocalVideoView() {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                LogUtil.e(TAG, "设置本地视频--->");
                RCCallPlusClient.getInstance().startCamera();
                RCCallPlusClient.getInstance().enableMicrophone(true);
                if (mBgmPlayer.isBluetoothHeadsetConnected()) mBgmPlayer.changeToBluetoothSco();
                else mBgmPlayer.changeToSpeaker();
                //创建本地视图对象
                RCCallPlusLocalVideoView localVideoView = new RCCallPlusLocalVideoView(CallPhoneActivity.this);
                //FIT: 视频帧通过保持宽高比(可能显示黑色边框)来缩放以适应视图的大小
//        localVideoView.setRenderMode(RCCallPlusRenderMode.FIT);
                //设置本地视图给SDK
                RCCallPlusClient.getInstance().setVideoView(localVideoView);

                FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(FrameLayout.LayoutParams.MATCH_PARENT, FrameLayout.LayoutParams.MATCH_PARENT);
                params.gravity = Gravity.CENTER_HORIZONTAL;//在父布局中横向居中显示
                //将本地视图添加到XML中显示
                //示例代码中 mLocalVideoViewFrameLayout 为 android.widget.FrameLayout 对象

//        mLocalVideoViewFrameLayout.removeAllViews();
//        mLocalVideoViewFrameLayout.addView(localVideoView);
                FrameLayout videoView = videoType == 0 ? mLocalVideoViewFrameLayout : mRemoteVideoViewFrameLayout;
                videoView.removeAllViews();
                videoView.addView(localVideoView);
            }
        });
    }


    private void startCall(String remoteUserId) {

        if (1 == mCallType) {
            //创建远端视图对象 remoteUserId为远端用户userId
            RCCallPlusRemoteVideoView remoteVideoView = new RCCallPlusRemoteVideoView(remoteUserId, this.getApplicationContext(), false);
            //FIT: 视频帧通过保持宽高比(可能显示黑色边框)来缩放以适应视图的大小
            remoteVideoView.setRenderMode(RCCallPlusRenderMode.FIT);
            //因为远端视图显示在最顶层，为了防止远端视频视图被底部控件遮挡，所以添加如下设置：
            remoteVideoView.setZOrderOnTop(true);
            remoteVideoView.setZOrderMediaOverlay(true);

            List<RCCallPlusRemoteVideoView> remoteVideoViewList = new ArrayList<>();
            remoteVideoViewList.add(remoteVideoView);
            //设置远端视图给SDK
            RCCallPlusClient.getInstance().setVideoView(remoteVideoViewList);

            FrameLayout.LayoutParams remoteVideoViewParams = new FrameLayout.LayoutParams(FrameLayout.LayoutParams.MATCH_PARENT, FrameLayout.LayoutParams.MATCH_PARENT);
            remoteVideoViewParams.gravity = Gravity.CENTER_HORIZONTAL;
        }

        //将远端视图添加到XML中显示
        //示例代码中 mRemoteVideoViewFrameLayout 为 android.widget.FrameLayout 对象
//        mLocalVideoViewFrameLayout.removeAllViews();
//        mLocalVideoViewFrameLayout.addView(remoteVideoView, remoteVideoViewParams);

        List<String> userIds = new ArrayList<>();
        userIds.add(remoteUserId);
        RCCallPlusType callType = RCCallPlusType.PRIVATE;//PRIVATE: 1V1通话
        RCCallPlusMediaType mediaType = mCallType == 0 ? RCCallPlusMediaType.AUDIO : RCCallPlusMediaType.VIDEO;
        RCCallPlusPushConfig pushConfig = RCCallPlusPushConfig.Builder.create().build();


        /**
         * 开始发起呼叫
         * 该方法内部为异步执行，结果回调是注册的{@link RCCallPlusClient#setCallPlusResultListener(IRCCallPlusResultListener)} 监听的 {@link IRCCallPlusResultListener#onStartCall(RCCallPlusCode, String, List)}方法
         */
        Log.i("RYCallPlusManager", "准备拨打电话===>" + remoteUserId);
        RCCallPlusClient.getInstance().startCall(userIds, callType, mediaType, pushConfig, getIntent().getStringExtra(KEY_ENTER_EXTRA));
    }

    private void acceptCall(String callId) {
        RCCallPlusClient.getInstance().accept(callId);
        if (1 == mCallType) {
            RCCallPlusSession currentCallSession = RCCallPlusClient.getInstance().getCurrentCallSession();
            if (currentCallSession != null)
                setRemoteUserVideoView(currentCallSession.getRemoteUserList());
        }
    }

    private void restoreRemoteVideo(String remoteUserId) {
        List<RCCallPlusRemoteVideoView> rcCallPlusRemoteVideoViewList = new ArrayList<>();
        RCCallPlusRemoteVideoView rcCallPlusVideoView = new RCCallPlusRemoteVideoView(remoteUserId, getApplicationContext(), false);
        mRemoteVideoViewFrameLayout.removeAllViews();
        mRemoteVideoViewFrameLayout.addView(rcCallPlusVideoView);
        rcCallPlusRemoteVideoViewList.add(rcCallPlusVideoView);
        RCCallPlusClient.getInstance().setVideoView(rcCallPlusRemoteVideoViewList);
    }

    /**
     * 设置远端用户视频渲染视图
     */
    private void setRemoteUserVideoView(List<RCCallPlusUser> remoteUserList) {
        runOnUiThread(() -> {
            List<String> userIds = new ArrayList<>();
            List<RCCallPlusRemoteVideoView> rcCallPlusRemoteVideoViewList = new ArrayList<>();
            for (RCCallPlusUser rcCallPlusUser : remoteUserList) {
                String remoteUserId = rcCallPlusUser.getUserId();
                userIds.add(remoteUserId);
                RCCallPlusClient.getInstance().removeVideoView(userIds);
                RCCallPlusRemoteVideoView rcCallPlusVideoView = new RCCallPlusRemoteVideoView(remoteUserId, getApplicationContext(), false);
//                mRemoteVideoViewFrameLayout.removeAllViews();
//                mRemoteVideoViewFrameLayout.addView(rcCallPlusVideoView);
                FrameLayout videoView = videoType == 0 ? mRemoteVideoViewFrameLayout : mLocalVideoViewFrameLayout;
                videoView.removeAllViews();
                videoView.addView(rcCallPlusVideoView);
                rcCallPlusRemoteVideoViewList.add(rcCallPlusVideoView);
            }
            RCCallPlusClient.getInstance().setVideoView(rcCallPlusRemoteVideoViewList);
        });
    }

//    private void changeVideoView() {
//        isChange = !isChange;
//    }

    @OnClick({R.id.hang_up, R.id.gift_tv, R.id.beaut_tv, R.id.svgaImage, R.id.microphone_switch, R.id.recharge_button, R.id.switch_camera})
    public void onClick(View view) {
        switch (view.getId()) {

            case R.id.svgaImage:
                mGiftIV.stopAnimation();
                mGiftIV.setVisibility(View.GONE);
                break;

            case R.id.recharge_button:
                GoldCoinActivity.startAct(CallPhoneActivity.this);
                break;

            case R.id.microphone_switch:
//                if (mBgmPlayer.getSpeakerModel() == AudioManager.MODE_NORMAL) {
//                    mBgmPlayer.changeToReceiver();
//                    setTextIcon(false);
//                } else {
//                    mBgmPlayer.changeToSpeaker();
//                    setTextIcon(true);
//                }
                toggleMicrophone();
                break;

            case R.id.hang_up:
                if (!isHangUpClicked) {
                    //mEnterType;//0呼叫  1接听
                    presenter.setRedisUserKey();
                    isHangUpClicked = true;
                }
                break;

            case R.id.gift_tv:
                mFaceUnityView.setVisibility(View.GONE);
                showTechnologicalDialog();
                break;

            case R.id.beaut_tv:
                mFaceUnityView.setVisibility((mFaceUnityView.getVisibility() == View.VISIBLE) ? View.GONE : View.VISIBLE);
                break;

            case R.id.switch_camera:
                switchCamera();
                break;
        }
    }

    private void setTextIcon(boolean isOpen) {
        mMicrophoneSwitchTv.setText(isOpen ? "扬声器开" : "扬声器关");
        Drawable dwTop = getResources().getDrawable(isOpen ? R.mipmap.icon_microphone_open : R.mipmap.icon_microphone_close);
        dwTop.setBounds(0, 0, dwTop.getMinimumWidth(), dwTop.getMinimumHeight());
        mMicrophoneSwitchTv.setCompoundDrawables(null, dwTop, null, null);
    }

    @Override
    protected boolean isUseEventBus() {
        return true;
    }

    @Override
    protected void onStop() {
        super.onStop();
        mFaceSettingHelper.saveFaceSetting(mFaceSettingListener.getFaceSetting());
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        RongCoreClient.removeOnReceiveMessageListener(receiveMsg);
        RongIMClient.getInstance().setMessageBlockListener(null);
//        //解绑 不显示悬浮框
//        if (mServiceBound) {
//            unbindService(mVideoCallServiceConnection);
//            mServiceBound = false;
//        }
    }


    @Override
    protected void onRestart() {
        super.onRestart();
        //不显示悬浮框
//        setLocalVideoView();
////        if (null != mCallSession && !LibCollections.isEmpty(mCallSession.getRemoteUserList())) {
////            setRemoteUserVideoView(mCallSession.getRemoteUserList());
////        }
//
//        if (XYVideoUtils.session != null)
//            setRemoteUserVideoView(XYVideoUtils.session.getRemoteUserList());

//        if (mServiceBound) {
//            unbindService(mVideoCallServiceConnection);
//            mServiceBound = false;
//        }
    }

    @Override
    protected CallPhonePresenter setPresenter() {
        return new CallPhonePresenter(this);
    }


    @Override
    public boolean onActionBarClickListener(int function) {
        switch (function) {
            case CustomActionBar.FUNCTION_BUTTON_LEFT:
                new XPopup.Builder(CallPhoneActivity.this).isDestroyOnDismiss(true) //对于只使用一次的弹窗，推荐设置这个
                        .customAnimator(new RotateAnimator()).asConfirm("通话提示", "您正在与对方进行通话中，是否确定挂断通话", new OnConfirmListener() {
                            @Override
                            public void onConfirm() {
//                                if (null != FloatWindow.get("smallVideo"))
//                                    FloatWindow.destroy("smallVideo");
                                sendVideoMsg(new XinYouCallInfo("已结束", mCallType + "", mCallStartTime == 0 ? "" : DateUtil.generateTime(System.currentTimeMillis() - mCallStartTime), mUserId));
                                RCCallPlusClient.getInstance().hangup();
                                EventBus.getDefault().post(new CallPlushListenerEvent(1));
                                cancelStartTimeTask();
                                XYVideoUtils.stop(getThisActivity());
                                XYVideoUtils.callStartTime = -1;
//                                finish();
                            }
                        }).show();
                break;

            case CustomActionBar.FUNCTION_BUTTON_RIGHT:
//                RCCallPlusClient.getInstance().switchCamera();

                if (XYSPUtils.getBoolean(Constant.KEY_USER_FLOAT_WIN)) {
                    startVideoService(false);
                } else {
                    new XPopup.Builder(CallPhoneActivity.this).isDestroyOnDismiss(true) //对于只使用一次的弹窗，推荐设置这个
                            .customAnimator(new RotateAnimator()).asConfirm("小窗提示", "浮窗需要开启浮窗权限，去开启?", new OnConfirmListener() {
                                @Override
                                public void onConfirm() {
                                    startVideoService(false);
                                }
                            }).show();
                }
                break;

        }
        return false;
    }

    @SuppressLint("RestrictedApi")
    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        if (event.getAction() == KeyEvent.ACTION_DOWN && event.getKeyCode() == KeyEvent.KEYCODE_BACK) {
            new XPopup.Builder(CallPhoneActivity.this).isDestroyOnDismiss(true) //对于只使用一次的弹窗，推荐设置这个
                    .customAnimator(new RotateAnimator()).asConfirm("通话提示", "您正在与对方进行通话中，是否确定挂断通话", new OnConfirmListener() {
                        @Override
                        public void onConfirm() {
//                            if (null != FloatWindow.get("smallVideo"))
//                                FloatWindow.destroy("smallVideo");
                            RCCallPlusClient.getInstance().hangup();
                            sendVideoMsg(new XinYouCallInfo("已结束", mCallType + "", mCallStartTime == 0 ? "" : mCallStartTime == 0 ? "" : DateUtil.generateTime(System.currentTimeMillis() - mCallStartTime), mUserId));
                            EventBus.getDefault().post(new CallPlushListenerEvent(1));
                            XYVideoUtils.stop(getThisActivity());
                            cancelStartTimeTask();
                            XYVideoUtils.callStartTime = -1;
//                            finish();
                        }
                    }).show();
            return true;
        }
        return super.dispatchKeyEvent(event);
    }


    public void sendVideoMsg(XinYouCallInfo callInfo) {
        Conversation.ConversationType conversationType = Conversation.ConversationType.PRIVATE;
        XYCallVideoContent messages = XYCallVideoContent.obtain(callInfo);
        Message message = Message.obtain(callInfo.getCallId(), conversationType, messages);
        message.setCanIncludeExpansion(true);
        RongIM.getInstance().sendMessage(message, null, null, new IRongCallback.ISendMediaMessageCallback() {
            @Override
            public void onProgress(Message message, int i) {

            }

            @Override
            public void onCanceled(Message message) {

            }

            @Override
            public void onAttached(Message message) {

            }

            @Override
            public void onSuccess(Message message) {

            }

            @Override
            public void onError(final Message message, final RongIMClient.ErrorCode errorCode) {

            }
        });
    }

    /**
     * 定义服务绑定的回调 开启视频通话服务连接
     */
    private ServiceConnection mVideoCallServiceConnection = new ServiceConnection() {

        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
            // 获取服务的操作对象
            FloatVideoWindowService.MyBinder binder = (FloatVideoWindowService.MyBinder) service;
            binder.getService();
        }

        @Override
        public void onServiceDisconnected(ComponentName name) {

        }
    };

    /*
     * 开启悬浮Video服务
     */
    private void startVideoService(boolean isCheck) {
        XXPermissions.with(CallPhoneActivity.this).permission(Permission.SYSTEM_ALERT_WINDOW, Permission.CAMERA).interceptor(new PermissionInterceptor()).request(new OnPermissionCallback() {
            @Override
            public void onGranted(List<String> permissions, boolean all) {
                if (!all) {
                    XYSPUtils.put(Constant.KEY_USER_FLOAT_WIN, false);
                    return;
                }
                XYSPUtils.put(Constant.KEY_USER_FLOAT_WIN, true);
                if (!isCheck) {
                    //最小化Activity
                    XYVideoUtils.start(XYApplication.getAppApplicationContext(), mEnterType, mCallType, mUserId, remainTime);
                    finish();
                }

            }
        });
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        CallPhoneActivity.this.moveTaskToBack(false);
        CallPhoneActivity.this.onPause();
    }

    @Override
    public FragmentActivity context() {
        return this;
    }

    @Override
    public void showCountDown(long l) {
        mConfinementTimeTv.setText("剩余" + l + "s后开启摄像头");
    }

    @Override
    public void showCountDownFinish() {
        mConfinementTimeTv.setText("");
        mConfinementTimeTv.setVisibility(View.GONE);
        setLocalVideoView();
        if (XYVideoUtils.session != null)
            setRemoteUserVideoView(XYVideoUtils.session.getRemoteUserList());
    }

    @Override
    public void showBalanceCountDown(long l) {
        remainTime = l * 1000;
        mBalanceTimeTv.setText("剩余" + l + "s后关闭通话");
    }

    @Override
    public void showBalanceCountDownFinish() {
        mBalanceTimeTv.setText("");
        mBalanceTimeTv.setVisibility(View.GONE);
    }

    @Override
    public void showGiftResult(List<GiftInfo> data) {
        mBottomRvDialog.setNewDatas(data);
    }

    @Override
    public void showUserBalance(BalanceInfo balanceInfo) {
        mBottomRvDialog.setUserBalance(balanceInfo);
    }

    @Override
    public void showVideoTips(VideoTipsInfo videoTipsInfo) {
        LogUtil.e("响应结果", "==========---》" + videoTipsInfo.getVideoMsg());
        mVideoTipsTv.setText(videoTipsInfo.getVideoMsg());
    }

    @Override
    public void showUserInfo(RyUserInfo data) {
        mDialog.setUserInfo(data, mReceiveRCCallPlusSession.getMediaType());
    }

    @Override
    public void showRedisUserKey(BaseBean data) {
        if (data.getCode() == 200) {
            mBgmPlayer.release();
            if (1 == mEnterType) {
                if (null != mCallSession && TextUtils.isEmpty(mCallSession.getCallerUserId())) {
                    sendVideoMsg(new XinYouCallInfo("已结束", mCallType + "", mCallStartTime == 0 ? "" : DateUtil.generateTime(System.currentTimeMillis() - mCallStartTime), mCallSession.getCallerUserId()));
                }
            } else {
                sendVideoMsg(new XinYouCallInfo("已结束", mCallType + "", "", mUserId));
            }
//                if (null != FloatWindow.get("smallVideo"))
//                    FloatWindow.destroy("smallVideo");
            RCCallPlusClient.getInstance().hangup();
//                EventBus.getDefault().post(new CallPlushListenerEvent(1));
//                XYVideoUtils.stop(getThisActivity());
//                XYVideoUtils.callStartTime = -1;
            cancelStartTimeTask();
            finish();
            LogUtil.i("RYCallPlusManager", "通话界面点击中间红色按钮关闭");
        }
    }

    private void showTechnologicalDialog() {
        mBottomRvDialog = new BottomGiftDialog(CallPhoneActivity.this);
        mBottomRvDialog.setTitleTv("礼物选择");
        mBottomRvDialog.setOnDialogCallbackListener(new BottomGiftDialog.OnDialogCallbackListener() {
            @Override
            public void onDialogRefresh() {
                presenter.getGift();
                presenter.getUserBalance();
            }

            @Override
            public void onChoiceGift(GiftInfo gift, int type, int num) {
                if (1 == mEnterType) {
                    if (XYVideoUtils.session != null)
                        sendGiftMsg(XYVideoUtils.session.getCallerUserId(), gift, type, num);
//                    if (XYVideoUtils.session != null)
//                        sendGiftMsg(XYVideoUtils.session.getCallerUserId(), gift, type, num);
                } else {
                    sendGiftMsg(mUserId, gift, type, num);
                }
                mBottomRvDialog.dismiss();
            }
        });
        mBottomRvDialog.show();
    }

    public void sendGiftMsg(String userId, GiftInfo giftInfo, int type, int num) {
        EventBus.getDefault().post(giftInfo);
        Conversation.ConversationType conversationType = Conversation.ConversationType.PRIVATE;
        XYGiftContent messages = XYGiftContent.obtain(giftInfo, type + "", num + "");
        Message message = Message.obtain(userId, conversationType, messages);
        message.setCanIncludeExpansion(true);

        IMCenter.getInstance().sendMessage(message, null, null, new IRongCallback.ISendMediaMessageCallback() {
            @Override
            public void onProgress(Message message, int i) {

            }

            @Override
            public void onCanceled(Message message) {

            }

            @Override
            public void onAttached(Message message) {

            }

            @Override
            public void onSuccess(Message message) {

            }

            @Override
            public void onError(final Message message, final RongIMClient.ErrorCode errorCode) {

            }
        });
    }

    private void loadAnimation(String giftUrl) {
        mGiftIV.setVisibility(View.VISIBLE);
        try { // new URL needs try catch.
            SVGAParser svgaParser = SVGAParser.Companion.shareParser();
//            svgaParser.setFrameSize(100, 100);
            svgaParser.decodeFromURL(new URL(giftUrl), new SVGAParser.ParseCompletion() {
                @Override
                public void onComplete(@NotNull SVGAVideoEntity videoItem) {
                    mGiftIV.setVideoItem(videoItem);
                    mGiftIV.startAnimation();
                }

                @Override
                public void onError() {

                }


            }, null);
        } catch (MalformedURLException e) {
            e.printStackTrace();
        }

        mGiftIV.setCallback(new SVGACallback() {
            @Override
            public void onPause() {

            }

            @Override
            public void onFinished() {
                if (!LibCollections.isEmpty(mUnGifts)) {
                    mUnGifts.remove(giftUrl);
                }

                if (LibCollections.isEmpty(mUnGifts)) {
                    mGiftIV.stopAnimation();
                    mGiftIV.setVisibility(View.GONE);
                } else {
                    playGifts();
                }
            }

            @Override
            public void onRepeat() {

            }

            @Override
            public void onStep(int i, double v) {

            }
        });
    }

    @Override
    protected void onResume() {
        super.onResume();
        Log.i(TAG, "进来resume页面！！！");
        XXPermissions.with(CallPhoneActivity.this).permission(Permission.RECORD_AUDIO).permission(Permission.CAMERA).interceptor(new PermissionInterceptor()).request(new OnPermissionCallback() {
            @Override
            public void onGranted(List<String> permissions, boolean all) {
                if (!all) {
                    return;
                }
            }
        });

        switchVideo();
//
//        setLocalVideoView();
////        if (null != mCallSession && !LibCollections.isEmpty(mCallSession.getRemoteUserList())) {
////            setRemoteUserVideoView(mCallSession.getRemoteUserList());
////        }
//
//        if (XYVideoUtils.session != null)
//            setRemoteUserVideoView(XYVideoUtils.session.getRemoteUserList());
//        if (null != FloatWindow.get("smallVideo"))
//            FloatWindow.get("smallVideo").hide();
    }

    private void toggleMicrophone() {
        isMicrophoneEnabled = !isMicrophoneEnabled;
        RCCallPlusClient.getInstance().enableMicrophone(isMicrophoneEnabled);
        updateMicrophoneButtonUI();
    }

    private void updateMicrophoneButtonUI() {
        mMicrophoneSwitchTv.setBackground(ContextCompat.getDrawable(this, isMicrophoneEnabled ? R.mipmap.maikefeng1 : R.mipmap.maikefeng0));

    }

    private void switchCamera() {
        RCCallPlusClient.getInstance().switchCamera();
    }
}
