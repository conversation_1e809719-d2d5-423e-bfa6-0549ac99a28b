package com.chat.laty.activity;

import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.view.MotionEvent;
import android.view.View;

import androidx.fragment.app.FragmentActivity;

import com.hjq.permissions.OnPermissionCallback;
import com.hjq.permissions.Permission;
import com.hjq.permissions.XXPermissions;
import com.chat.laty.R;
import com.chat.laty.controllers.AccostLanguageController;
import com.chat.laty.entity.AccostLanguageInfo;
import com.chat.laty.entity.AccostManLanguageInfo;
import com.chat.laty.entity.BaseBean;
import com.chat.laty.entity.UploadImgInfo;
import com.chat.laty.entity.event.AudioPathEvent;
import com.chat.laty.mvp.BasePresenterActivity;
import com.chat.laty.presenters.AccostLanguagePresenter;
import com.chat.laty.utils.BgmPlayer;
import com.chat.laty.utils.LogUtil;
import com.chat.laty.utils.PermissionInterceptor;
import com.chat.laty.view.DiffuseView;
import com.chat.laty.view.recordView.RecordManager;

import org.greenrobot.eventbus.EventBus;

import java.io.File;
import java.util.List;

import butterknife.BindView;

/**
 * <AUTHOR>
 * @date 2024/1/13 16:13
 * @description:
 */
public class UserAddAudioSignatureUI extends BasePresenterActivity<AccostLanguagePresenter> implements AccostLanguageController.View {

    @BindView(R.id.diffuseView)
    DiffuseView mRippleView;

    String mAudioFilePath;
    String mAudioPath;
    private RecordManager manager;
    private BgmPlayer mBgmPlayer;

    private int mCountDownTravel;


    public static void start(Context context) {
        Intent intent = new Intent(context, UserAddAudioSignatureUI.class);
        context.startActivity(intent);
    }

    @Override
    protected void initDatas() {

    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_add_audio_signature_layout;
    }

    @Override
    protected void initViews() {
        mBgmPlayer = BgmPlayer.getInstance(UserAddAudioSignatureUI.this);

        mRippleView.setOnLongClickListener(new View.OnLongClickListener() {
            @Override
            public boolean onLongClick(View view) {
                XXPermissions.with(UserAddAudioSignatureUI.this).permission(Permission.WRITE_EXTERNAL_STORAGE).permission(Permission.RECORD_AUDIO).interceptor(new PermissionInterceptor()).request(new OnPermissionCallback() {
                    @Override
                    public void onGranted(List<String> permissions, boolean all) {
                        if (!all) {
                            return;
                        }
                        startRecord();
                    }
                });
                return false;
            }
        });

        mRippleView.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                if (event.getAction() == MotionEvent.ACTION_UP) {
                    LogUtil.e("setOnTouchListener", "抬起了");
                    if (!TextUtils.isEmpty(mAudioFilePath)) {
                        mRippleView.stop();
                        presenter.stopRecord();
                        if (null != manager)
                            manager.stopRecord(new RecordManager.OnRecordFinish() {
                                @Override
                                public void onRecordFinishCallback(long time, String filePath) {
                                    mCountDownTravel = (int) (time / 1000);
                                    presenter.uploadAudio(filePath, "audio");
                                }
                            });


//                            mBgmPlayer.playLocal(new File(mAudioFilePath));

                    }


                } else if (event.getAction() == MotionEvent.ACTION_DOWN) {
                    LogUtil.e("setOnTouchListener", "按下了");
//                    mAudioFilePath = getExternalCacheDir().getPath() + "/" + System.currentTimeMillis() + ".amr";
//                    manager = new RecordManager(new File(mAudioFilePath), onVolume);
//                    manager.startRecord();
//                    presenter.startRecord();
//                    mRippleView.start();
                }
                return false;
            }
        });

    }

    RecordManager.OnVolume onVolume = new RecordManager.OnVolume() {
        @Override
        public void onVolume(int db) {
            mRippleView.setDiffuseSpeed(db);

        }
    };

    private void startRecord() {
        mAudioFilePath = getExternalCacheDir().getPath() + "/" + System.currentTimeMillis() + ".aac";
        manager = new RecordManager(new File(mAudioFilePath), onVolume);
        manager.startRecord();
        presenter.startRecord();
        mRippleView.start();
    }

    @Override
    protected AccostLanguagePresenter setPresenter() {
        return new AccostLanguagePresenter(this);
    }

    @Override
    public void showUploadImgResult(List<UploadImgInfo> data) {

    }

    @Override
    public void showAccostWomanList(List<AccostLanguageInfo> accostInfo) {

    }

    @Override
    public void showAccostManList(AccostManLanguageInfo accostInfo) {

    }

    @Override
    public void showupdateCallback(int code) {

    }

    @Override
    public void showDeleteCallback(Integer code) {

    }

    @Override
    public void showDefaultCallback(Integer code) {

    }

    @Override
    public void showCountDownFinish() {

    }

    @Override
    public void showUploadAudioResult(List<String> result) {
        mAudioPath = result.get(0);
        EventBus.getDefault().post(new AudioPathEvent(mAudioPath, mCountDownTravel));
        finish();
    }

    @Override

    public void showCountDownTravel(int l) {
        mCountDownTravel = l;
    }

    @Override
    public void showUpdateCallback(BaseBean base) {

    }

    @Override
    public FragmentActivity context() {
        return this;
    }
}
