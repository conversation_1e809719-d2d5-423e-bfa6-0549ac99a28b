package com.chat.laty.activity;

import android.content.Context;
import android.content.Intent;
import android.view.View;

import androidx.fragment.app.FragmentActivity;

import com.hjq.toast.Toaster;
import com.chat.laty.R;
import com.chat.laty.controllers.GeneralSettingController;
import com.chat.laty.entity.BaseBean;
import com.chat.laty.entity.GeneralSettingInfo;
import com.chat.laty.entity.SimiInfo;
import com.chat.laty.entity.WebBeanInfo;
import com.chat.laty.mvp.BasePresenterActivity;
import com.chat.laty.presenters.GeneralSettingPresenter;
import com.chat.laty.utils.XYSPUtils;
import com.chat.laty.utils.net.Common;
import com.chat.laty.view.CustomActionBar;

import butterknife.BindView;
import butterknife.OnClick;
import io.rong.imkit.RongIM;

/**
 * <AUTHOR>
 * @date 2023/12/24 20:46
 * @description:
 */
public class AcountSettingUI extends BasePresenterActivity<GeneralSettingPresenter> implements GeneralSettingController.View {

    @BindView(R.id.custom_bar)
    CustomActionBar mActionBar;

    public static void start(Context context) {
        Intent intent = new Intent(context, AcountSettingUI.class);
        context.startActivity(intent);
    }

    @Override
    protected void initDatas() {

    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_acount_settings_activity;
    }

    @Override
    protected void initViews() {
        mActionBar.setTitleText("账号与安全设置");
        mActionBar.setTitleTextColor(getResources().getColor(R.color.color_333333));
    }

    @OnClick({R.id.cancel_account_stv})
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.cancel_account_stv://注销账号
//                new XPopup.Builder(AcountSettingUI.this)
//                        .isDestroyOnDismiss(true)
//                        .customAnimator(new CallPhoneActivity.RotateAnimator())
//                        .asConfirm("注销账号", "您是否确定注销您当前账号?", new OnConfirmListener() {
//                            @Override
//                            public void onConfirm() {
//                                presenter.logOffUser();
//                            }
//                        }).show();

                CancelAcountSettingUI.start(AcountSettingUI.this);
                break;
        }
    }


    @Override
    protected GeneralSettingPresenter setPresenter() {
        return new GeneralSettingPresenter(this);
    }

    @Override
    public FragmentActivity context() {
        return this;
    }

    @Override
    public void showGeneralSetting(GeneralSettingInfo info) {
    }

    @Override
    public void showWebInfo(WebBeanInfo info) {
    }

    @Override
    public void showDeleteCallback(BaseBean baseBean) {
        if (null != baseBean) {
            Toaster.show(baseBean.getMessage());
            if (200 == baseBean.getCode()) {
                XYSPUtils.delete(Common.KEY_APP_TOKEN);
                XYSPUtils.delete(Common.KEY_APP_USER_RY_ID);
                XYSPUtils.delete(Common.KEY_APP_USER_GENDER);
                XYSPUtils.delete(Common.KEY_APP_USER_PHONE);
                RongIM.getInstance().logout();
                LoginByWeiChatUI.startNew(AcountSettingUI.this);
                finish();
            }
        }
    }

    @Override
    public void showLogoutCallback(BaseBean uploadBean) {

    }

    @Override
    public void getSimiInfo(SimiInfo simiInfo) {

    }
}
