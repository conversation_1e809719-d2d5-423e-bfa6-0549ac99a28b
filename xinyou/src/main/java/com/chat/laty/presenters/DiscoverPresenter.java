package com.chat.laty.presenters;

import static cn.xjc_soft.lib_utils.ViewUtils.runOnUiThread;

import android.text.TextUtils;
import android.widget.Toast;

import com.google.gson.Gson;
import com.hjq.toast.Toaster;
import com.chat.laty.base.AppHelper;
import com.chat.laty.base.XYApplication;
import com.chat.laty.contants.Constant;
import com.chat.laty.controllers.DiscoverController;
import com.chat.laty.entity.AppVersionInfo;
import com.chat.laty.entity.BannerInfo;
import com.chat.laty.entity.BaseBean;
import com.chat.laty.entity.FilterBean;
import com.chat.laty.entity.RYTokenInfo;
import com.chat.laty.entity.RecommendEntity;
import com.chat.laty.entity.RecommendationBean;
import com.chat.laty.entity.TodayFateEntity;
import com.chat.laty.entity.WebBeanInfo;
import com.chat.laty.entity.WeiChatPayInfo;
import com.chat.laty.entity.XYUserInfo;
import com.chat.laty.entity.event.DownLoadProgressEvent;
import com.chat.laty.im.message.XYCallVideoContent;
import com.chat.laty.im.message.XYCommentContent;
import com.chat.laty.im.message.XYGiftContent;
import com.chat.laty.im.message.XYGoldCoinsTextContent;
import com.chat.laty.im.message.XYMediaMessageContent;
import com.chat.laty.im.message.XYTextContent;
import com.chat.laty.utils.BgmPlayer;
import com.chat.laty.utils.FromJsonUtils;
import com.chat.laty.utils.LogUtil;
import com.chat.laty.utils.ResultData;
import com.chat.laty.utils.ResultData1;
import com.chat.laty.utils.XYSPUtils;
import com.chat.laty.utils.net.Common;
import com.chat.laty.utils.net.GsonUtils;
import com.chat.laty.utils.net.HttpUtils;
import com.chat.laty.utils.net.TextCallBack;

import org.greenrobot.eventbus.EventBus;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import cn.rongcloud.callplus.api.RCCallPlusClient;
import cn.rongcloud.callplus.api.RCCallPlusConfig;
import cn.rongcloud.callplus.api.RCCallPlusResultCode;
import cn.xjc_soft.lib_utils.ThreadUtils;
import io.rong.imkit.RongIM;
import io.rong.imlib.RongIMClient;
import io.rong.imlib.model.MessageContent;

/**
 * <AUTHOR>
 * @date 2022/8/16 14:01
 * @description:登录 presenter
 */
public class DiscoverPresenter implements DiscoverController.Presenter {

    private DiscoverController.View mView;


    public DiscoverPresenter(DiscoverController.View mView) {
        this.mView = mView;
        initDatas();
    }

    /**
     * 获取融云token
     */
    public void getRYToken() {
        HttpUtils.get(Common.APP_GET_RONG_CLOUD_TOKEN, new HashMap<>(), new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<RYTokenInfo> mData = FromJsonUtils.fromJson(text, RYTokenInfo.class);
                if (null != mData) {
                    ThreadUtils.runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            if (200 != mData.getCode()) {
                                Toaster.show(mData.getMessage());
//                                mView.showRyToken(null);
                                XYSPUtils.clear(XYApplication.getAppApplicationContext());
                            } else {
                                connectIM(mData.getData().getToken());
//                                mView.showRyToken(mData.getData());
                            }
                        }
                    });
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
//                mView.showLogin(null);
                XYSPUtils.clear(XYApplication.getAppApplicationContext());
            }
        });
    }

    public void getUserInfo(String userId) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("userId", userId);
        HttpUtils.get(Common.APP_GET_USER_INFO, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========他人信息：" + text);
                ResultData<XYUserInfo> mData = FromJsonUtils.fromJson(text, XYUserInfo.class);
                if (200 != mData.getCode()) {
                    Toaster.show(mData.getMessage());
                }
                if (null != mView) {
                    ThreadUtils.runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mView.showUserDetails(null == mData.getData() ? null : mData.getData());
                        }
                    });
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
            }
        });
    }

    public void getRecommendList() {
        HashMap<String, Object> params = new HashMap<>();
        HttpUtils.get(Common.GET_RECOMMENDATION, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========他人信息：" + text);
                ResultData<List<RecommendationBean>> mData = FromJsonUtils.fromJson(text, RecommendationBean.class);
                if (200 != mData.getCode()) {
                    Toaster.show(mData.getMessage());
                } else {
                    if (null != mView) {
                        ThreadUtils.runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showRecomPop(null == mData.getData() ? null : mData.getData());
                            }
                        });
                    }
                }

            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
            }
        });
    }

    public void getOneKeyAccost(List<String> userId) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("toUserList", userId);
        HttpUtils.getResult(Common.GET_ONE_KEY_ACCOST, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========他人信息：" + text);
                ResultData1 mData = new Gson().fromJson(text, ResultData1.class);

                /*ResultData<Object> mData = FromJsonUtils.fromJson(text, Object.class);*/
                if (null != mView) {
                    ThreadUtils.runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            if (mData.getCode() == 500) {
                                mView.showRecomPop1();
                            } else if (mData.getCode() == 200) {
                                Toast.makeText(mView.context(), mData.getMessage(), Toast.LENGTH_SHORT).show();
                            }
                        }
                    });
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
            }
        });
    }

    public void connectIM(String token) {
        ArrayList<Class<? extends MessageContent>> myMessages = new ArrayList<>();
        myMessages.add(XYTextContent.class);
        myMessages.add(XYGiftContent.class);
        myMessages.add(XYMediaMessageContent.class);
        myMessages.add(XYCallVideoContent.class);
        myMessages.add(XYCommentContent.class);
        myMessages.add(XYGoldCoinsTextContent.class);
        RongIMClient.registerMessageType(myMessages);
//        String targetId = "1725849550466932737";//1729341395424034818（176） 1729776846723137538（133）
        int timeLimit = 0;
        RongIM.connect(token, timeLimit, new RongIMClient.ConnectCallback() {
            @Override
            public void onDatabaseOpened(RongIMClient.DatabaseOpenStatus code) {
                LogUtil.e("响应结果", "==========本地数据库状态###" + code);
                if (RongIMClient.DatabaseOpenStatus.DATABASE_OPEN_SUCCESS.equals(code)) {
                    //本地数据库打开，跳转到会话列表页面
                } else {
                    //数据库打开失败，可以弹出 toast 提示。
                }
            }

            @Override
            public void onSuccess(String userId) {
                //连接成功，如果 onDatabaseOpened() 时没有页面跳转，也可在此时进行跳转。
                LogUtil.e("响应结果", "==========userId###" + userId);
                ThreadUtils.runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        RCCallPlusResultCode resultCode = RCCallPlusClient.getInstance().init(RCCallPlusConfig.Builder.create().enableTinyStream(false).build());
                        LogUtil.e("进入融云初始化", "进入融云初始化");
                        LogUtil.e("响应结果", "==========resultCode###" + resultCode.getReason());
                    }
                });
                BgmPlayer bgmPlayer = BgmPlayer.getInstance(XYApplication.getAppApplicationContext());
                bgmPlayer.changeToSpeaker();
//                RCCallPlusResultCode resultCode = RCCallPlusClient.getInstance().init(RCCallPlusConfig.Builder.create().build());
//                LogUtil.e("响应结果", "==========resultCode:::" + resultCode);
            }

            @Override
            public void onError(RongIMClient.ConnectionErrorCode errorCode) {
                LogUtil.e("响应结果", "==========errorCode###" + errorCode.getValue());
                if (errorCode.equals(RongIMClient.ConnectionErrorCode.RC_CONN_TOKEN_EXPIRE)) {
                    //从 APP 服务请求新 token，获取到新 token 后重新 connect()
                    Toaster.show("从 APP 服务请求新 token，获取到新 token 后重新 connect()");
                } else if (errorCode.equals(RongIMClient.ConnectionErrorCode.RC_CONNECT_TIMEOUT)) {
                    //连接超时，弹出提示，可以引导用户等待网络正常的时候再次点击进行连接
                    Toaster.show("连接超时");
                } else {
                    //其它业务错误码，请根据相应的错误码作出对应处理。
                }
            }
        });
    }

    public void getAccordByNum(int type) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("type", type);
        HttpUtils.get(Common.APP_GET_ACCORD_BY_TYPE, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<WebBeanInfo> mData = FromJsonUtils.fromJson(text, WebBeanInfo.class);

                if (200 == mData.getCode()) {
                    if (null != mView) {
                        ThreadUtils.runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showWebInfo(mData.getData());
                            }
                        });
                    }
                }

            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
//                mView.showLogin(null);
            }
        });
    }

    /**
     *
     */
    public void getPayParm() {
        HttpUtils.getResult(Common.APP_GET_GET_PAY_URL, new HashMap<>(), new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<RYTokenInfo> mData = FromJsonUtils.fromJson(text, RYTokenInfo.class);

//                LogUtil.e("响应结果码", "==========" + mData.getCode());
                if (200 != mData.getCode()) {
                    Toaster.show(mData.getMessage());
                    if (null != mView) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showPayParm(null);

                            }
                        });
                    }
                } else {
                    if (null != mView) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showPayParm(mData.getData());

                            }
                        });
                    }
                }

            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
//                mView.showLogin(null);
            }
        });
    }

    public void getWXPayParam() {
        HttpUtils.getResult(Common.APP_GET_GET_WX_PAY_URL, new HashMap<>(), new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<WeiChatPayInfo> mData = FromJsonUtils.fromJson(text, WeiChatPayInfo.class);
                if (200 != mData.getCode()) {
                    Toaster.show(mData.getMessage());
                    if (null != mView) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showAXPayParm(null);

                            }
                        });
                    }
                } else {
                    if (null != mView) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showAXPayParm(mData.getData());

                            }
                        });
                    }
                }

            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
//                mView.showLogin(null);
            }
        });
    }

    public void getRecommend(int pageNo, int pageType, FilterBean filterInfo, boolean isRequestType) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("pageNo", pageNo);
        params.put("pageSize", 20);
        params.put("pageType", pageType + 1);
        if (1 == pageType) {
            params.put("latitude", XYSPUtils.getString(Constant.KEY_USER_LATITUDE));
            params.put("longitude", XYSPUtils.getString(Constant.KEY_USER_LONGITUDE));
        }

        if (null != filterInfo) {
            if (!TextUtils.isEmpty(filterInfo.getKey()))
                params.put("keyword", filterInfo.getKey());
            if (!TextUtils.isEmpty(filterInfo.getStartAge()))
                params.put("begin_age", filterInfo.getStartAge());
            if (!TextUtils.isEmpty(filterInfo.getEndAge()))
                params.put("end_age", filterInfo.getEndAge());
            if (!TextUtils.isEmpty(filterInfo.getEndAge()))
                params.put("end_age", filterInfo.getEndAge());
//            if (0 != filterInfo.getSex())
//                params.put("sex", filterInfo.getSex());
        }
        if (isRequestType)
            params.put("requestType", 1);
        HttpUtils.getResult(Common.APP_GET_FIRST_PAGE_RECOMMEND, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<RecommendEntity> mData = FromJsonUtils.fromJson(text, RecommendEntity.class);
                if (200 != mData.getCode()) {
                    Toaster.show(mData.getMessage());
                    if (null != mView) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showRecommend(null);

                            }
                        });
                    }
                } else {
                    if (null != mView) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showRecommend(mData.getData().getRecords());

                            }
                        });
                    }
                }

            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
//                mView.showLogin(null);
            }
        });
    }


    public void getRecommend(int pageNo, int pageType) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("pageNo", pageNo);
        params.put("pageSize", 20);
        params.put("pageType", pageType + 1);
        HttpUtils.getResult(Common.APP_GET_FIRST_PAGE_RECOMMEND, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<RecommendEntity> mData = FromJsonUtils.fromJson(text, RecommendEntity.class);
                if (200 != mData.getCode()) {
                    Toaster.show(mData.getMessage());
                    if (null != mView) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showRecommend(null);

                            }
                        });
                    }
                } else {
                    if (null != mView) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showRecommend(mData.getData().getRecords());

                            }
                        });
                    }
                }

            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
//                mView.showLogin(null);
            }
        });
    }


    public void getBanners() {
        HttpUtils.get(Common.APP_GET_FIRST_PAGE_BANNERS, new HashMap<>(), new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========banner-->" + text);
                ResultData<List<BannerInfo>> mData = FromJsonUtils.fromJson(text, BannerInfo.class);
                if (200 != mData.getCode()) {
                    Toaster.show(mData.getMessage());
                    if (null != mView) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showBanners(null);
                            }
                        });
                    }
                } else {
                    if (null != mView) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showBanners(mData.getData());

                            }
                        });
                    }
                }

            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
//                mView.showLogin(null);
            }
        });
    }

    public void accostToUser(String toUserId) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("toUserId", toUserId);
        HttpUtils.get(Common.APP_GET_FIRST_PAGE_ACCOST_TO_USER, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                BaseBean callbackBean = GsonUtils.JsonToBean(text, BaseBean.class);
                if (200 != callbackBean.getCode()) {
                    Toaster.show(callbackBean.getMessage());
                } else {
                    if (null != mView) {
                        ThreadUtils.runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showAccostToUserSuccess();
                            }
                        });
                    }
                }
//                ResultData<List<BannerInfo>> mData = FromJsonUtils.fromJson(text, BannerInfo.class);
//                if (200 != mData.getCode()) {
//                    Toaster.show(mData.getMessage());
//                    if (null != mView) {
//                        runOnUiThread(new Runnable() {
//                            @Override
//                            public void run() {
//                                mView.showBanners(null);
//
//                            }
//                        });
//                    }
//                } else {
//                    if (null != mView) {
//                        runOnUiThread(new Runnable() {
//                            @Override
//                            public void run() {
//                                mView.showBanners(mData.getData());
//
//                            }
//                        });
//                    }
//                }

            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
//                mView.showLogin(null);
            }
        });
    }

    public void updateLocation(double latitude, double longitude) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("latitude", latitude);
        params.put("longitude", longitude);
        HttpUtils.getResult(Common.APP_GET_FIRST_PAGE_UPDATE_LOCATION, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "位置结果==========" + text);
//                BaseBean callbackBean = GsonUtils.JsonToBean(text, BaseBean.class);
//                if (200 != callbackBean.getCode()) {
//                    Toaster.show(callbackBean.getMessage());
//                } else {
//                    if (null != mView) {
//                        ThreadUtils.runOnUiThread(new Runnable() {
//                            @Override
//                            public void run() {
//                                mView.showAccostToUserSuccess();
//                            }
//                        });
//                    }
//                }
//                ResultData<List<BannerInfo>> mData = FromJsonUtils.fromJson(text, BannerInfo.class);
//                if (200 != mData.getCode()) {
//                    Toaster.show(mData.getMessage());
//                    if (null != mView) {
//                        runOnUiThread(new Runnable() {
//                            @Override
//                            public void run() {
//                                mView.showBanners(null);
//
//                            }
//                        });
//                    }
//                } else {
//                    if (null != mView) {
//                        runOnUiThread(new Runnable() {
//                            @Override
//                            public void run() {
//                                mView.showBanners(mData.getData());
//
//                            }
//                        });
//                    }
//                }

            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
//                mView.showLogin(null);
            }
        });
    }

    public void getTodayFate() {
        HttpUtils.get(Common.APP_GET_TODAY_USER, new HashMap<>(), new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "位置结果==========" + text);
                ResultData<TodayFateEntity> mData = FromJsonUtils.fromJson(text, TodayFateEntity.class);
                if (200 == mData.getCode()) {
                    if (null != mView) {
                        ThreadUtils.runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showTodayFate(mData.getData());
                            }
                        });
                    }
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
//                mView.showLogin(null);
            }
        });
    }

    public void getNewVersions() {
        HashMap<String, Object> params = new HashMap<>();
        params.put("appType", 1);
        HttpUtils.get(Common.APP_GET_NEW_VERSION, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<AppVersionInfo> mData = FromJsonUtils.fromJson(text, AppVersionInfo.class);
                if (null != mView) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mView.showAppVersion(mData.getData());
                        }
                    });
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
            }
        });
    }


    public void putClickAccost(String voiceTempId, String textTempId, com.alibaba.fastjson.JSONArray toUserList) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("textTempId", textTempId);
        params.put("toUserList", toUserList);
        params.put("voiceTempId", voiceTempId);
        HttpUtils.getResult(Common.APP_PUT_ONE_CLICK_ACCOST, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                BaseBean callbackBean = GsonUtils.JsonToBean(text, BaseBean.class);
//                ResultData<AppVersionInfo> mData = FromJsonUtils.fromJson(text, AppVersionInfo.class);
                if (200 == callbackBean.getCode() && null != mView) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mView.showOneClickAccostCallback();
                        }
                    });
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
            }
        });
    }

    /**
     * 下载pdf文件
     *
     * @param url
     * @param fileName
     */
    public void downLoadFile(String url, String fileName) {
        HttpUtils.doDownLoadApkFile(url, fileName, new HttpUtils.DownloadListener() {
            @Override
            public void onDownloadSuccess(String path) {
                LogUtil.e("文件结果", "onDownloadSuccess  path" + path);
//                ThreadUtils.runOnUiThread(new Runnable() {
//                    @Override
//                    public void run() {
//                        mView.showPdfPath(path);
//                    }
//                });

                AppHelper.openApkFile(new File(path));
            }

            @Override
            public void onDownloading(int progress) {
                EventBus.getDefault().post(new DownLoadProgressEvent(progress));
                LogUtil.e("文件结果", "onDownloading  " + progress);
            }

            @Override
            public void onDownloadFailed() {
                LogUtil.e("文件结果", "onDownloadFailed  ");
            }
        });
    }


    private void initDatas() {
    }

    @Override
    public void onCreate() {

    }

    @Override
    public void destroy() {
    }
}
