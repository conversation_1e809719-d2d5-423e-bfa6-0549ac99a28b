package com.chat.laty.presenters;

import com.chat.laty.controllers.MasonryController;
import com.chat.laty.entity.MasonryInfo;
import com.chat.laty.utils.FromJsonUtils;
import com.chat.laty.utils.LogUtil;
import com.chat.laty.utils.ResultData;
import com.chat.laty.utils.net.Common;
import com.chat.laty.utils.net.HttpUtils;
import com.chat.laty.utils.net.TextCallBack;

import java.util.HashMap;
import java.util.List;

import cn.xjc_soft.lib_utils.ThreadUtils;

/**
 * <AUTHOR>
 * @date 2023/11/28 16:46
 * @description:
 */
public class MasonryPresenter implements MasonryController.Presenter {

    private MasonryController.View mView;

    public MasonryPresenter(MasonryController.View mView) {
        this.mView = mView;
    }


    public void getWithdrawalList(int pageNo, int type, int status) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("pageNo", pageNo);
        params.put("pageSize", 20);
        params.put("type", type);
        if (0 != status)
            params.put("payType", status == 2 ? 1 : status);
        HttpUtils.getResult(Common.APP_GET_WITHDRAWAL_INFO_LIST, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);

                ResultData<List<MasonryInfo>> mData = FromJsonUtils.fromJson(text, MasonryInfo.class);
//
                if (null != mView) {
                    ThreadUtils.runOnUiThread(() -> mView.getDetailSucceed(mData.getData()));
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========onFailure===" + e.toString());

//                ThreadUtils.runOnUiThread(() -> {
//                    String msg = "获取金币详细失败～";
//                    if (!TextUtils.isEmpty(e.getMsg())) {
//                        msg = e.getMsg();
//                    }
//                    Toaster.show(msg);
//                    if (null != mView) {
//                        mView.getDetailFailed(load, refresh, loadmore);
//                    }
//                });
            }
        });
    }

    @Override
    public void onCreate() {

    }

    @Override
    public void destroy() {

    }
}
