package com.chat.laty.presenters;

import static cn.xjc_soft.lib_utils.ThreadUtils.runOnUiThread;

import android.content.Context;
import android.text.TextUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.hjq.toast.Toaster;
import com.chat.laty.activity.MyAuthenticationUI;
import com.chat.laty.base.XYApplication;
import com.chat.laty.controllers.CommunityController;
import com.chat.laty.entity.BaseBean;
import com.chat.laty.entity.CommunityInfoModel;
import com.chat.laty.entity.RyUserInfo;
import com.chat.laty.entity.XinYouCommentInfo;
import com.chat.laty.im.message.XYCommentContent;
import com.chat.laty.utils.FromJsonUtils;
import com.chat.laty.utils.LogUtil;
import com.chat.laty.utils.ResultData;
import com.chat.laty.utils.XYSPUtils;
import com.chat.laty.utils.net.Common;
import com.chat.laty.utils.net.GsonUtils;
import com.chat.laty.utils.net.HttpUtils;
import com.chat.laty.utils.net.TextCallBack;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import cn.xjc_soft.lib_utils.LibCollections;
import cn.xjc_soft.lib_utils.ThreadUtils;
import io.rong.imkit.IMCenter;
import io.rong.imlib.IRongCallback;
import io.rong.imlib.RongIMClient;
import io.rong.imlib.model.Conversation;
import io.rong.imlib.model.Message;

/**
 * <AUTHOR>
 * @date 2023/11/28 16:46
 * @description:
 */
public class CommunityPresenter implements CommunityController.Presenter {

    private int type;

    private CommunityController.IListView listView;
    private CommunityController.IAccostView accostView;

    RyUserInfo userInfo;

    public CommunityPresenter(CommunityController.IListView mView, CommunityController.IAccostView view) {
        this.listView = mView;
        this.accostView = view;
    }

    public boolean isNotRzUser(Context context) {
        if (userInfo == null) {
            userInfo = XYApplication.getCurrentUserInfo();
        }

        if (TextUtils.equals("2", userInfo.getSex()) && !TextUtils.equals("1", userInfo.getIsName())) {
            Toaster.show("请先完成实名认证");
            if (context != null) {
                MyAuthenticationUI.start(context);
            }
            return true;
        }

        return false;
    }

    public void setType(int type) {
        this.type = type;
    }

    /**
     * 获取融云token
     */
    public void getCommunityList(int pageNo, int pageSize, String lat, String lng, boolean load, boolean refresh, boolean loadmore) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("pageNo", pageNo);
        params.put("pageSize", pageSize);
        params.put("pageType", type);
        if (type == 2) {
            params.put("lat", lat);
            params.put("lng", lng);
        }

        HttpUtils.getResult(Common.APP_GET_COMMUNITY, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<List<CommunityInfoModel>> mData = FromJsonUtils.fromJson(text, CommunityInfoModel.class);
                if (200 == mData.getCode()) {
                    if (null != listView) {
                        List<CommunityInfoModel> list;
                        if (com.blankj.utilcode.util.CollectionUtils.isNotEmpty(mData.getData())) {
                            String code = JSON.toJSONString(mData.getData());
                            list = new ArrayList<>(JSONArray.parseArray(code, CommunityInfoModel.class));
                        } else {
                            list = mData.getData();
                        }
                        List<CommunityInfoModel> finalLi = list;
                        runOnUiThread(() -> listView.getListSucceed(finalLi, load, refresh, loadmore));
                    }
                } else if (!TextUtils.isEmpty(mData.getMessage())) {
                    Toaster.show(mData.getMessage());
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
                String msg = "";
                if (e.getMsg() != null) {
                    msg = e.getMsg();
                }
                if (null != listView) listView.getListFailed(msg, load, refresh, loadmore);
            }
        });
    }

    public void getUserCommunityList(int pageNo, String userId) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("pageNo", pageNo);
        params.put("pageSize", 20);
        params.put("userId", userId);

        HttpUtils.get(Common.APP_GET_USER_DYNAMICS, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<List<CommunityInfoModel>> mData = FromJsonUtils.fromJson(text, CommunityInfoModel.class);
                if (200 == mData.getCode()) {
                    if (null != listView) {
                        runOnUiThread(() -> listView.getUserCommunitys(mData.getData()));
                    }
                } else if (!TextUtils.isEmpty(mData.getMessage())) {
                    Toaster.show(mData.getMessage());
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
            }
        });
    }

    public void accostToUser(String toUserId, CommunityInfoModel data, int index) {

        HashMap<String, Object> params = new HashMap<>();
        params.put("toUserId", toUserId);
        HttpUtils.get(Common.APP_GET_FIRST_PAGE_ACCOST_TO_USER, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                BaseBean callbackBean = GsonUtils.JsonToBean(text, BaseBean.class);
                if (200 != callbackBean.getCode()) {
                    Toaster.show(callbackBean.getMessage());
                } else {
                    if (null != accostView) {
                        ThreadUtils.runOnUiThread(() -> accostView.accostSucceed(data, index));
                    }
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
            }
        });
    }

    public void likeToUser(CommunityInfoModel data, int index) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("communityId", data.getId());
        params.put("type", TextUtils.equals("1", data.getIsLike()) ? 2 : 1);
        HttpUtils.get(Common.APP_GET_COMMUNITY_LIKE, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                BaseBean callbackBean = GsonUtils.JsonToBean(text, BaseBean.class);
                if (200 == callbackBean.getCode()) {
                    if (null != accostView) {
                        ThreadUtils.runOnUiThread(() -> accostView.likeSucceed(data, index));
                    }
                } else if (!TextUtils.isEmpty(callbackBean.getMessage())) {
                    Toaster.show(callbackBean.getMessage());
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
            }
        });
    }

    public void reply(String content, CommunityInfoModel model, int index, int type, CommunityInfoModel.CommentInfoModel data) {

        boolean result = isNotRzUser(null);
        if (result) return;

        HashMap<String, Object> params = new HashMap<>();
        params.put("commentId", type == 0 ? "" : data.getId());
        params.put("communityId", model.getId());
        params.put("content", content);
        HttpUtils.getResult(Common.APP_POST_ADD_COMMENT, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                BaseBean callbackBean = GsonUtils.JsonToBean(text, BaseBean.class);
                if (200 == callbackBean.getCode()) {
                    if (null != accostView) {
//                        if (type == 0) {
//                            if (XYApplication.getCurrentIsMan())
//                                sendCommentMsg(model.getUserId(), new XinYouCommentInfo(content, "评论了你", "1", LibCollections.isEmpty(model.uploadImgInfos) ? "" : model.uploadImgInfos.get(0).getImgUrl(), model.getId()));
//                        } else {
                            sendCommentMsg(model.getUserId(), new XinYouCommentInfo(content, "评论了你", "1", LibCollections.isEmpty(model.uploadImgInfos) ? "" : model.uploadImgInfos.get(0).getImgUrl(), model.getId()));
//                        }
                        ThreadUtils.runOnUiThread(() -> {
                            accostView.replySucceed(model, index, data);
                        });
                    }
                } else if (!TextUtils.isEmpty(callbackBean.getMessage())) {
                    Toaster.show(callbackBean.getMessage());
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());

                String msg = "发送失败";
                if (TextUtils.isEmpty(e.getMsg())) {
                    msg = e.getMsg();
                }
                Toaster.show(msg);
                if (null != accostView) {
                    ThreadUtils.runOnUiThread(() -> accostView.failed());
                }
            }
        });
    }

    public void delete(String content, CommunityInfoModel model, int index, int type, CommunityInfoModel.CommentInfoModel data) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("commentId", type == 0 ? "" : data.getId());
        params.put("communityId", model.getId());
        params.put("content", content);
        HttpUtils.getResult(Common.APP_POST_ADD_COMMENT, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                BaseBean callbackBean = GsonUtils.JsonToBean(text, BaseBean.class);
                if (200 == callbackBean.getCode()) {
                    if (null != accostView) {
                        ThreadUtils.runOnUiThread(() -> accostView.replySucceed(model, index, data));
                    }
                } else if (!TextUtils.isEmpty(callbackBean.getMessage())) {
                    Toaster.show(callbackBean.getMessage());
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());

                String msg = "发送失败";
                if (TextUtils.isEmpty(e.getMsg())) {
                    msg = e.getMsg();
                }
                Toaster.show(msg);
                if (null != accostView) {
                    ThreadUtils.runOnUiThread(() -> accostView.failed());
                }
            }
        });
    }

    public void deleteDynamicsById(String communityId) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("communityId", communityId);
        HttpUtils.get(Common.APP_POST_DELETE_DYNAMICS, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                BaseBean callbackBean = GsonUtils.JsonToBean(text, BaseBean.class);
                if (200 == callbackBean.getCode()) {
                    if (null != accostView) {
                        ThreadUtils.runOnUiThread(() -> accostView.deleteCallback(callbackBean));
                    }
                } else if (!TextUtils.isEmpty(callbackBean.getMessage())) {
                    Toaster.show(callbackBean.getMessage());
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
            }
        });
    }


    public void update(CommunityInfoModel model, String communityId, int index) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("communityId", model != null ? model.getId() : communityId);
        HttpUtils.get(Common.APP_POST_GET_COMMENT, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<CommunityInfoModel> mData = FromJsonUtils.fromJson(text, CommunityInfoModel.class);

                if (200 == mData.getCode()) {
                    if (null != accostView) {
                        ThreadUtils.runOnUiThread(() -> accostView.updateSucceed(mData.getData(), index));
                    }
                } else if (!TextUtils.isEmpty(mData.getMessage())) {
                    Toaster.show(mData.getMessage());
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());

                if (null != accostView) {
                    String msg = "发送失败";
                    if (TextUtils.isEmpty(e.getMsg())) {
                        msg = e.getMsg();
                    }
                    Toaster.show(msg);
                    ThreadUtils.runOnUiThread(() -> accostView.failed());
                }

            }
        });
    }

    /**
     * 发送评论消息
     *
     * @param userId   用户id
     * @param callInfo 评论信息
     */
    public void sendCommentMsg(String userId, XinYouCommentInfo callInfo) {
        if (TextUtils.equals(userId, XYSPUtils.getString(Common.KEY_APP_USER_RY_ID))) {
            LogUtil.e("sendCommentMsg", "不能给自己发评论");
            return;
        }
        Conversation.ConversationType conversationType = Conversation.ConversationType.PRIVATE;
        XYCommentContent messages = XYCommentContent.obtain(callInfo);
        Message message = Message.obtain(userId, conversationType, messages);
        message.setCanIncludeExpansion(true);
        IMCenter.getInstance().sendMessage(message, null, null, new IRongCallback.ISendMediaMessageCallback() {
            @Override
            public void onProgress(Message message, int i) {
                LogUtil.e("onError", "onProgress--->" + message);
            }

            @Override
            public void onCanceled(Message message) {
                LogUtil.e("onError", "onCanceled--->" + message);
            }

            @Override
            public void onAttached(Message message) {
                LogUtil.e("onError", "onAttached--->" + message);
            }

            @Override
            public void onSuccess(Message message) {
//                Toaster.show("评论成功");
            }

            @Override
            public void onError(final Message message, final RongIMClient.ErrorCode errorCode) {
                LogUtil.e("onError", "errorCode--->" + errorCode.msg);
            }
        });
    }

    @Override
    public void onCreate() {

    }

    @Override
    public void destroy() {

    }
}
