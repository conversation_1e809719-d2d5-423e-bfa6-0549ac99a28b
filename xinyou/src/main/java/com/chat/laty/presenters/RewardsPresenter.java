package com.chat.laty.presenters;

import com.hjq.toast.Toaster;
import com.chat.laty.controllers.RewardsController;
import com.chat.laty.entity.BankCardInfo;
import com.chat.laty.entity.BaseBean;
import com.chat.laty.entity.RewardsEntity;
import com.chat.laty.utils.FromJsonUtils;
import com.chat.laty.utils.LogUtil;
import com.chat.laty.utils.ResultData;
import com.chat.laty.utils.net.Common;
import com.chat.laty.utils.net.GsonUtils;
import com.chat.laty.utils.net.HttpUtils;
import com.chat.laty.utils.net.TextCallBack;

import java.util.HashMap;

import static cn.xjc_soft.lib_utils.ThreadUtils.runOnUiThread;

/**
 * <AUTHOR>
 * @date 2023/11/28 16:46
 * @description:
 */
public class RewardsPresenter implements RewardsController.Presenter {

    private RewardsController.View mView;

    public RewardsPresenter(RewardsController.View mView) {
        this.mView = mView;
        initDatas();
    }


    public void getRewards() {
        HashMap<String, Object> params = new HashMap<>();
        HttpUtils.get(Common.APP_GET_MY_REWARDS_DETAILS, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<RewardsEntity> mData = FromJsonUtils.fromJson(text, RewardsEntity.class);

                if (null != mView) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            if (200 != mData.getCode()) {
                                Toaster.show(mData.getMessage());
                            } else {
                                mView.showRewards(mData.getData());
                            }
                        }
                    });
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
            }
        });
    }

    public void updateAliWxPayAccount(int type, HashMap<String, Object> params) {
        HttpUtils.getResult((1 == type || 2 == type) ? Common.APP_UPDATE_ALI_WX_PAY_ACCOUNT : Common.APP_UPDATE_BANK_CARD, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                BaseBean baseBean = GsonUtils.JsonToBean(text, BaseBean.class);
                if (null != mView) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mView.showUpdateCallback(baseBean);
                        }
                    });
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
            }
        });
    }


    public void saveWithdrawal(String id, int type, BankCardInfo bank) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("withdrawalSettingId", id);
        params.put("withdrawalType", type);
        if (null != bank)
            params.put("bankId", bank.getId());
        HttpUtils.getResult(Common.APP_APP_SAVE_WITHDRAWAL, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                BaseBean baseBean = GsonUtils.JsonToBean(text, BaseBean.class);
//                ResultData<RewardsEntity> mData = FromJsonUtils.fromJson(text, RewardsEntity.class);
                if (null != mView) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mView.showWithdrawalResult(baseBean);
                        }
                    });
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
            }
        });
    }

    private void initDatas() {
    }

    @Override
    public void onCreate() {

    }

    @Override
    public void destroy() {

    }
}
