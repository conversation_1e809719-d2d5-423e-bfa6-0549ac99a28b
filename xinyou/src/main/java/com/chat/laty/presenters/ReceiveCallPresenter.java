package com.chat.laty.presenters;

import com.chat.laty.controllers.ReceiveCallController;
import com.chat.laty.entity.BaseBean;
import com.chat.laty.entity.RyUserInfo;
import com.chat.laty.utils.FromJsonUtils;
import com.chat.laty.utils.LogUtil;
import com.chat.laty.utils.ResultData;
import com.chat.laty.utils.net.Common;
import com.chat.laty.utils.net.GsonUtils;
import com.chat.laty.utils.net.HttpUtils;
import com.chat.laty.utils.net.TextCallBack;

import java.util.HashMap;

import cn.xjc_soft.lib_utils.ViewUtils;

/**
 * <AUTHOR>
 * @date 2023/11/28 16:46
 * @description:
 */
public class ReceiveCallPresenter implements ReceiveCallController.Presenter {

    private ReceiveCallController.View mView;

    public ReceiveCallPresenter(ReceiveCallController.View mView) {
        this.mView = mView;
    }


    public void getUserInfo(String userId) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("userId", userId);
        HttpUtils.get(Common.APP_GET_CHAT_RY_USERINFO, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========userResult-->" + text);
                ResultData<RyUserInfo> mData = FromJsonUtils.fromJson(text, RyUserInfo.class);
                if (null != mView) {
                    ViewUtils.runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mView.showUserInfo(mData.getData());
                        }
                    });
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
            }
        });
    }

    public void submitRoom(String userId,String toUserId,String roomId) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("userId", userId);
        params.put("toUserId", toUserId);
        params.put("roomId", roomId);
        HttpUtils.get(Common.APP_GET_CHAT_SUBMIT_ROOMINFO, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========userResult-->" + text);
//                ResultData<RyUserInfo> mData = FromJsonUtils.fromJson(text, RyUserInfo.class);
//                if (null != mView) {
//                    ViewUtils.runOnUiThread(new Runnable() {
//                        @Override
//                        public void run() {
//                            mView.showUserInfo(mData.getData());
//                        }
//                    });
//                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
            }
        });
    }

    public void setRedisUserKey(String userId) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("userId", userId);
        HttpUtils.get(Common.APP_VIDEO_SET_REDIS_USER_KEY, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========RedisUserResult-->" + text);
                BaseBean callbackBean = GsonUtils.JsonToBean(text, BaseBean.class);
                if (null != mView) {
                    ViewUtils.runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mView.showRedisUserKey(callbackBean);
                        }
                    });
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
            }
        });
    }


    @Override
    public void onCreate() {

    }

    @Override
    public void destroy() {

    }
}
