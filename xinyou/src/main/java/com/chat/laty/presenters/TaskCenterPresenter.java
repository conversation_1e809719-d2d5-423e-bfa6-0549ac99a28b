package com.chat.laty.presenters;

import android.text.TextUtils;

import com.hjq.toast.Toaster;
import com.chat.laty.controllers.TaskCenterController;
import com.chat.laty.controllers.VipCenterController;
import com.chat.laty.entity.BaseBean;
import com.chat.laty.entity.TaskCenterInfoModel;
import com.chat.laty.utils.FromJsonUtils;
import com.chat.laty.utils.LogUtil;
import com.chat.laty.utils.ResultData;
import com.chat.laty.utils.net.Common;
import com.chat.laty.utils.net.GsonUtils;
import com.chat.laty.utils.net.HttpUtils;
import com.chat.laty.utils.net.TextCallBack;

import java.util.HashMap;

import cn.xjc_soft.lib_utils.ThreadUtils;

/**
 * <AUTHOR>
 * @date 2023/11/28 16:46
 * @description:
 */
public class TaskCenterPresenter implements VipCenterController.Presenter {

    int type = 0;
    private final TaskCenterController.IInfoView infoIView;

    public void setType(int type) {
        this.type = type;
    }

    public TaskCenterPresenter(TaskCenterController.IInfoView infoIView) {
        this.infoIView = infoIView;
    }

    public void getTaskCenterInfo() {
        HashMap<String, Object> params = new HashMap<>();

        HttpUtils.get(Common.APP_GET_TASK_CENTER_INFO, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<TaskCenterInfoModel> data = FromJsonUtils.fromJson(text, TaskCenterInfoModel.class);
                if (null != infoIView) {
                    ThreadUtils.runOnUiThread(() -> infoIView.getInfoSucceed(data.getData()));
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
                if (null != infoIView) {
                    ThreadUtils.runOnUiThread(() -> {
                        String msg = "获取金币信息失败～";
                        if (!TextUtils.isEmpty(e.getMsg())) {
                            msg = e.getMsg();
                        }
                        Toaster.show(msg);
                        infoIView.getInfoFailed();
                    });
                }
            }
        });
    }

    public void sign() {
        HashMap<String, Object> params = new HashMap<>();

        HttpUtils.get(Common.APP_GET_TASK_CENTER_SIGN, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                BaseBean mData = GsonUtils.JsonToBean(text, BaseBean.class);

                ThreadUtils.runOnUiThread(() -> {
                    if (mData.getCode() == 200) {
                        Toaster.show("签到成功～");
                        if (infoIView != null) {
                            infoIView.signSucceed();
                        }
                    } else {
                        Toaster.show(mData.getMessage());
                        if (infoIView != null) {
                            infoIView.signFailed();
                        }
                    }
                });
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());

                ThreadUtils.runOnUiThread(() -> {
                    String msg = "签到失败～";
                    if (!TextUtils.isEmpty(e.getMsg())) {
                        msg = e.getMsg();
                    }
                    Toaster.show(msg);
                    if (null != infoIView) {
                        infoIView.signFailed();
                    }
                });
            }
        });
    }

    @Override
    public void onCreate() {

    }

    @Override
    public void destroy() {

    }
}
