package com.chat.laty.presenters;

import android.text.TextUtils;

import com.hjq.toast.Toaster;
import com.chat.laty.controllers.InviteController;
import com.chat.laty.entity.BaseBean;
import com.chat.laty.entity.InviteBindRecordsInfoModel;
import com.chat.laty.entity.InviteFriendInfoModel;
import com.chat.laty.entity.InviteInfoModel;
import com.chat.laty.entity.SimpleBaseBean;
import com.chat.laty.utils.FromJsonUtils;
import com.chat.laty.utils.LogUtil;
import com.chat.laty.utils.ResultData;
import com.chat.laty.utils.net.Common;
import com.chat.laty.utils.net.GsonUtils;
import com.chat.laty.utils.net.HttpUtils;
import com.chat.laty.utils.net.TextCallBack;

import java.util.HashMap;
import java.util.List;

import cn.xjc_soft.lib_utils.ThreadUtils;

/**
 * <AUTHOR>
 * @date 2023/11/28 16:46
 * @description:
 */
public class InvitePresenter1 implements InviteController.Presenter {

    private final InviteController.IInfoView infoIView;
    private InviteController.IBindSelfView bindView;
    private InviteController.IBindRecordsView recordsView;

    public InvitePresenter1(InviteController.IInfoView infoIView) {
        this.infoIView = infoIView;
    }

    public void addView(InviteController.IBindRecordsView recordsView) {
        this.recordsView = recordsView;
    }

    public void addView(InviteController.IBindSelfView bindView) {
        this.bindView = bindView;
    }

    public void getBindRecordsList() {
        HashMap<String, Object> params = new HashMap<>();
        params.put("pageNo",1);
        params.put("pageSize",50);
        HttpUtils.get(Common.APP_GET_INVITE_BING_RECORDS, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<List<InviteBindRecordsInfoModel>> data = FromJsonUtils.fromJson(text, InviteBindRecordsInfoModel.class);
                if (null != recordsView) {
                    ThreadUtils.runOnUiThread(() -> recordsView.getRecordsSucceed(data.getData()));
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());

                ThreadUtils.runOnUiThread(() -> {
                    String msg = "获取申请记录信息失败～";
                    if (!TextUtils.isEmpty(e.getMsg())) {
                        msg = e.getMsg();
                    }
                    Toaster.show(msg);
                    if (null != recordsView) {
                        recordsView.getRecordsFailed();
                    }
                });
            }
        });
    }

    public void getInviteInfo() {
        HashMap<String, Object> params = new HashMap<>();
        HttpUtils.get(Common.APP_GET_INVITE_INFO, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);

                ResultData<InviteInfoModel> data = FromJsonUtils.fromJson(text, InviteInfoModel.class);

                if (null != infoIView) {
                    ThreadUtils.runOnUiThread(() -> infoIView.getInfoSucceed(data.getData()));
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
                ThreadUtils.runOnUiThread(() -> {
                    String msg = "获取邀请信息失败～";
                    if (!TextUtils.isEmpty(e.getMsg())) {
                        msg = e.getMsg();
                    }
                    Toaster.show(msg);
                    if (null != infoIView) {
                        infoIView.getInfoFailed();
                    }
                });
            }
        });
    }

    public void getInviteFriends() {
        HashMap<String, Object> params = new HashMap<>();
        params.put("pageNo", 1);
        params.put("pageSize", 50);
        HttpUtils.get(Common.APP_GET_INVITE_FRIENDS, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);

                ResultData<List<InviteFriendInfoModel>> data = FromJsonUtils.fromJson(text, InviteFriendInfoModel.class);

                if (null != infoIView) {
                    ThreadUtils.runOnUiThread(() -> infoIView.getFriendsSucceed(data.getData()));
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
                ThreadUtils.runOnUiThread(() -> {
                    String msg = "获取邀请信息失败～";
                    if (!TextUtils.isEmpty(e.getMsg())) {
                        msg = e.getMsg();
                    }
                    Toaster.show(msg);
                    if (null != infoIView) {
                        infoIView.getFriendsFailed();
                    }
                });
            }
        });
    }

    public void getDownloadUrl() {
        HashMap<String, Object> params = new HashMap<>();
        HttpUtils.get(Common.APP_GET_INVITE_DOWN_URL, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);

                SimpleBaseBean bean = GsonUtils.JsonToBean(text, SimpleBaseBean.class);

                if (null != infoIView) {
                    ThreadUtils.runOnUiThread(() -> infoIView.getDownUrlSucceed(bean.getResult()));
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
                ThreadUtils.runOnUiThread(() -> {
                    String msg = "获取下载链接失败～";
                    if (!TextUtils.isEmpty(e.getMsg())) {
                        msg = e.getMsg();
                    }
                    Toaster.show(msg);
                    if (null != infoIView) {
                        infoIView.getInfoFailed();
                    }
                });
            }
        });
    }

    public void modify(String invitationCode) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("invitationCode", invitationCode);
        HttpUtils.getResult(Common.APP_GET_INVITE_MODIFY, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);

                BaseBean bean = GsonUtils.JsonToBean(text, BaseBean.class);

                ThreadUtils.runOnUiThread(() -> {
                    if (bean.getCode() == 200) {
                        Toaster.show("修改成功～");
                        if (null != infoIView) {
                            infoIView.modifySucceed();
                        }
                    } else {
                        Toaster.show(bean.getMessage());
                        if (null != infoIView) {
                            infoIView.modifyFailed();
                        }
                    }
                });
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
                ThreadUtils.runOnUiThread(() -> {

                    String msg = "修改失败～";
                    if (e.getMsg() != null) {
                        msg = e.getMsg();
                    }
                    if (!TextUtils.isEmpty(msg)) {
                        Toaster.show(msg);
                    }
                    if (null != infoIView) {
                        infoIView.modifyFailed();
                    }
                });
            }
        });
    }

    public void apply(String bindUserId, String remark, String fileUrl) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("bindUserId", bindUserId);
        params.put("remark", remark);
        params.put("fileUrl", fileUrl);
        HttpUtils.getResult(Common.APP_GET_INVITE_BING_SELF, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);

                BaseBean bean = GsonUtils.JsonToBean(text, BaseBean.class);

                ThreadUtils.runOnUiThread(() -> {
                    if (bean.getCode() == 200) {
                        Toaster.show("申请成功～");
                        if (null != bindView) {
                            bindView.bindSucceed();
                        }
                    } else {
                        Toaster.show(bean.getMessage());
                        if (null != bindView) {
                            bindView.bindFailed();
                        }
                    }
                });
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
                ThreadUtils.runOnUiThread(() -> {

                    String msg = "申请失败～";
                    if (e.getMsg() != null) {
                        msg = e.getMsg();
                    }
                    if (!TextUtils.isEmpty(msg)) {
                        Toaster.show(msg);
                    }
                    if (null != bindView) {
                        bindView.bindFailed();
                    }
                });
            }
        });
    }

    public void bindFriend(String parentCode) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("parentCode", parentCode);
        HttpUtils.get(Common.APP_GET_INVITE_BING_FRIENDS, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);

                BaseBean bean = GsonUtils.JsonToBean(text, BaseBean.class);

                ThreadUtils.runOnUiThread(() -> {
                    if (bean.getCode() == 200) {
                        Toaster.show("绑定成功～");
                        if (null != infoIView) {
                            infoIView.bindFriendSucceed();
                        }
                    } else {
                        Toaster.show(bean.getMessage());
                        if (null != infoIView) {
                            infoIView.bindFriendFailed();
                        }
                    }
                });
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
                ThreadUtils.runOnUiThread(() -> {

                    String msg = "绑定失败～";
                    if (e.getMsg() != null) {
                        msg = e.getMsg();
                    }
                    if (!TextUtils.isEmpty(msg)) {
                        Toaster.show(msg);
                    }
                    if (null != infoIView) {
                        infoIView.bindFriendFailed();
                    }
                });
            }
        });
    }

    @Override
    public void onCreate() {

    }

    @Override
    public void destroy() {

    }
}
