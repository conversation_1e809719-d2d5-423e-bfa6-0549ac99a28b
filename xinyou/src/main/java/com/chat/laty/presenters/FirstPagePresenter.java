package com.chat.laty.presenters;

import static cn.xjc_soft.lib_utils.ThreadUtils.runOnUiThread;

import com.hjq.toast.Toaster;
import com.chat.laty.controllers.FirstPageController;
import com.chat.laty.entity.RYTokenInfo;
import com.chat.laty.utils.FromJsonUtils;
import com.chat.laty.utils.LogUtil;
import com.chat.laty.utils.ResultData;
import com.chat.laty.utils.net.Common;
import com.chat.laty.utils.net.HttpUtils;
import com.chat.laty.utils.net.TextCallBack;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @date 2023/11/28 16:46
 * @description:
 */
public class FirstPagePresenter implements FirstPageController.Presenter {

    private FirstPageController.View mView;

    public FirstPagePresenter(FirstPageController.View mView) {
        this.mView = mView;
    }

    /**
     * 获取融云token
     */
    public void getRongCloudToken() {
        HttpUtils.get(Common.APP_GET_RONG_CLOUD_TOKEN, new HashMap<>(), new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<RYTokenInfo> mData = FromJsonUtils.fromJson(text, RYTokenInfo.class);
                if (null != mView) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            if (200 != mData.getCode()) {
                                Toaster.show(mData.getMessage());
                                mView.showRyToken(null);
                            } else {
                                mView.showRyToken(mData.getData());
                            }
                        }
                    });
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
//                mView.showLogin(null);
            }
        });
    }

    @Override
    public void onCreate() {

    }

    @Override
    public void destroy() {

    }
}
