package com.chat.laty.presenters;

import static cn.xjc_soft.lib_utils.ThreadUtils.runOnUiThread;

import com.hjq.toast.Toaster;
import com.chat.laty.controllers.FriendController;
import com.chat.laty.entity.BaseBean;
import com.chat.laty.entity.FriendInfo;
import com.chat.laty.utils.FromJsonUtils;
import com.chat.laty.utils.LogUtil;
import com.chat.laty.utils.ResultData;
import com.chat.laty.utils.net.Common;
import com.chat.laty.utils.net.GsonUtils;
import com.chat.laty.utils.net.HttpUtils;
import com.chat.laty.utils.net.TextCallBack;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/28 16:46
 * @description:
 */
public class FriendPresenter implements FriendController.Presenter {

    private FriendController.View mView;

    public FriendPresenter(FriendController.View mView) {
        this.mView = mView;
    }


    public void getFriendsByType(int friendType, int pageNo) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("pageType", friendType);
        params.put("pageNo", pageNo);
        params.put("PageSize", 20);
        HttpUtils.get(Common.APP_GET_FRIEND_BY_TYPE, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<List<FriendInfo>> mData = FromJsonUtils.fromJson(text, FriendInfo.class);
                if (200 == mData.getCode()) {
                    if (null != mView) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showFriends(mData.getData());
                            }
                        });
                    }
                }


            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
//                mView.showLogin(null);
            }
        });
    }


    public void followUser(String userId, boolean type) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("toUserId", userId);
        params.put("type", type ? "2" : "1");//关注类型(1-关注 2-取消关注)
        HttpUtils.get(Common.APP_FOLLOW_USER, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
//                ResultData<XYUserInfo> mData = FromJsonUtils.fromJson(text, XYUserInfo.class);
                BaseBean mData = GsonUtils.JsonToBean(text, BaseBean.class);
                if (200 != mData.getCode()) {
                    Toaster.show(mData.getMessage());
                    return;
                }
                if (null != mView) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mView.showFollwoCallback();
                        }
                    });
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
            }
        });
    }


    public void cancleFollowUser(String userId) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("toUserId", userId);
        params.put("type", "2");
        HttpUtils.get(Common.APP_FOLLOW_USER, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
//                ResultData<XYUserInfo> mData = FromJsonUtils.fromJson(text, XYUserInfo.class);
                BaseBean mData = GsonUtils.JsonToBean(text, BaseBean.class);
                if (200 != mData.getCode()) {
                    Toaster.show(mData.getMessage());
                    return;
                }
                if (null != mView) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mView.showCancleFollwoCallback();
                        }
                    });
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
            }
        });
    }

    @Override
    public void onCreate() {

    }

    @Override
    public void destroy() {

    }
}
