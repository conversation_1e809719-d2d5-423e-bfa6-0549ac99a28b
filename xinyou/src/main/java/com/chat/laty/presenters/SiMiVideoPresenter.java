package com.chat.laty.presenters;

import static cn.xjc_soft.lib_utils.ThreadUtils.runOnUiThread;

import com.hjq.toast.Toaster;
import com.luck.picture.lib.entity.LocalMedia;
import com.chat.laty.controllers.SiMiVideoController;
import com.chat.laty.entity.SiMiUploadBean1;
import com.chat.laty.entity.UploadFileInfo;
import com.chat.laty.utils.FromJsonUtils;
import com.chat.laty.utils.LogUtil;
import com.chat.laty.utils.ResultData;
import com.chat.laty.utils.net.Common;
import com.chat.laty.utils.net.GsonUtils;
import com.chat.laty.utils.net.HttpUtils;
import com.chat.laty.utils.net.TextCallBack;

import java.util.HashMap;
import java.util.List;


public class SiMiVideoPresenter implements SiMiVideoController.Presenter {

    private SiMiVideoController.View mView;

    public SiMiVideoPresenter(SiMiVideoController.View mView) {
        this.mView = mView;
    }

    public void getSimiPhoto() {
        HashMap<String, Object> params = new HashMap<>();
        HttpUtils.get(Common.GET_SIMI_VIDEO, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========simisimi--->" + text);
                ResultData<List<SiMiUploadBean1>> data = FromJsonUtils.fromJson(text, SiMiUploadBean1.class);
                if (null != data.getData()) {
                    mView.loadSiMiData(data.getData());
                }

            }
            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
            }
        });
    }

    public void delSimiPhoto(String id,int position) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("id", id);
        HttpUtils.get(Common.DEL_SIMI_VIDEO, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========simisimi--->" + text);
                ResultData<Object> data = FromJsonUtils.fromJson(text, Object.class);
                Toaster.show(data.getMessage());
                mView.delSiMiResult(position);
            }
            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
            }
        });
    }

    public void UploadInfo(String url) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("videoSort", "0");
        params.put("videoUrl", url);
        HttpUtils.getResult(Common.UPLOAD_SIMI_VIDEO, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========vipInfo--->" + text);
                ResultData<Object> data = FromJsonUtils.fromJson(text, Object.class);
                Toaster.show(data.getMessage());
//                if (null != data.getData()) {}
                mView.submitSiMiResult(url);
            }
            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
            }
        });
    }

    public void uploadFiles(List<LocalMedia> files) {
        HttpUtils.upLoadFiles(Common.APP_UPLOAD_FILE, "file", files, "userHead", new HttpUtils.OnResultCallBack() {
            @Override
            public void OnResultCallBack(String s) {
                LogUtil.e("响应结果", "==========" + s);
//                ResultData<List<String>> mData = FromJsonUtils.fromJson(s, String.class);
                UploadFileInfo uploadBean = GsonUtils.JsonToBean(s, UploadFileInfo.class);
//                ResultData<List<String>> mData = FromJsonUtils.fromJson(s, String.class);
                if (200 != uploadBean.getCode()) {
                    Toaster.show(uploadBean.getMessage());
                    if (null != mView) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showUploadResults(null);
                            }
                        });
                    }
                } else {
                    if (null != mView) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showUploadResults(uploadBean.getResult());
                            }
                        });
                    }
                }
            }

            @Override
            public void OnFailCallBack(String s) {
                LogUtil.e("响应结果", "==========出错了-->" + s);
            }
        });
    }


    @Override
    public void onCreate() {
    }
    @Override
    public void destroy() {
    }
}
