package com.chat.laty.presenters;

import android.text.TextUtils;

import com.hjq.toast.Toaster;
import com.chat.laty.controllers.CreditsController;
import com.chat.laty.entity.BaseBean;
import com.chat.laty.entity.CreditsRecordsModel;
import com.chat.laty.entity.CreditsStoreInfoModel;
import com.chat.laty.utils.FromJsonUtils;
import com.chat.laty.utils.LogUtil;
import com.chat.laty.utils.ResultData;
import com.chat.laty.utils.net.Common;
import com.chat.laty.utils.net.GsonUtils;
import com.chat.laty.utils.net.HttpUtils;
import com.chat.laty.utils.net.TextCallBack;

import java.util.HashMap;
import java.util.List;

import cn.xjc_soft.lib_utils.ThreadUtils;

/**
 * <AUTHOR>
 * @date 2023/11/28 16:46
 * @description:
 */
public class CreditsStorePresenter implements CreditsController.Presenter {

    int type = 0;
    private final CreditsController.ICreditsStoreView infoIView;
    private final CreditsController.ICreditsRecordsView recordsView;
    private final CreditsController.ICreditsStoreDetailView detailView;


    public void setType(int type) {
        this.type = type;
    }

    public CreditsStorePresenter(CreditsController.ICreditsStoreView infoIView,
                                 CreditsController.ICreditsRecordsView recordsView,
                                 CreditsController.ICreditsStoreDetailView detailView) {
        this.infoIView = infoIView;
        this.recordsView = recordsView;
        this.detailView = detailView;
    }


    public void getCreditsStoreInfo() {
        HashMap<String, Object> params = new HashMap<>();
        HttpUtils.get(Common.APP_GET_CREDITS_STORE_INFO, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<CreditsStoreInfoModel> data = FromJsonUtils.fromJson(text, CreditsStoreInfoModel.class);
                if (null != infoIView) {
                    ThreadUtils.runOnUiThread(() -> infoIView.getInfoSucceed(data.getData()));
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());

                ThreadUtils.runOnUiThread(() -> {
                    String msg = "获取商城信息失败～";
                    if (!TextUtils.isEmpty(e.getMsg())) {
                        msg = e.getMsg();
                    }
                    Toaster.show(msg);
                    if (null != infoIView) {
                        infoIView.getInfoFailed();
                    }
                });
            }
        });
    }

    public void getRecordsList() {
        HashMap<String, Object> params = new HashMap<>();
        HttpUtils.get(Common.APP_GET_CREDITS_RECORDS, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<List<CreditsRecordsModel>> data = FromJsonUtils.fromJson(text, CreditsRecordsModel.class);
                if (null != recordsView) {
                    ThreadUtils.runOnUiThread(() -> recordsView.getRecordsSucceed(data.getData()));
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());

                ThreadUtils.runOnUiThread(() -> {
                    String msg = "获取好友失败～";
                    if (!TextUtils.isEmpty(e.getMsg())) {
                        msg = e.getMsg();
                    }
                    Toaster.show(msg);
                    if (null != recordsView) {
                        recordsView.getRecordsFailed();
                    }
                });

            }
        });
    }

    public void exchange(String integralId) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("integralId", integralId);
        HttpUtils.get(Common.APP_GET_CREDITS_EXCHANAGE, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);

                BaseBean bean = GsonUtils.JsonToBean(text, BaseBean.class);

                ThreadUtils.runOnUiThread(() -> {
                    if (bean.getCode() == 200) {
                        Toaster.show("兑换成功～");
                        if (null != detailView) {
                            detailView.exchangeSucceed();
                        }
                    } else {
                        Toaster.show(bean.getMessage());
                        if (null != detailView) {
                            detailView.exchangeFailed();
                        }
                    }
                });
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
                ThreadUtils.runOnUiThread(() -> {
                    String msg = "兑换失败～";
                    if (!TextUtils.isEmpty(e.getMsg())) {
                        msg = e.getMsg();
                    }
                    Toaster.show(msg);
                    if (null != detailView) {
                        detailView.exchangeFailed();
                    }
                });
            }
        });
    }

    // oldcenter/getPayLink


    @Override
    public void onCreate() {

    }

    @Override
    public void destroy() {

    }
}
