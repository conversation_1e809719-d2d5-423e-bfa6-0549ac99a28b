package com.chat.laty.presenters;

import com.chat.laty.base.XYApplication;
import com.chat.laty.controllers.GeneralSettingController;
import com.chat.laty.entity.BaseBean;
import com.chat.laty.entity.GeneralSettingInfo;
import com.chat.laty.entity.SimiInfo;
import com.chat.laty.entity.VipInfoModel;
import com.chat.laty.entity.WebBeanInfo;
import com.chat.laty.utils.FromJsonUtils;
import com.chat.laty.utils.LogUtil;
import com.chat.laty.utils.ResultData;
import com.chat.laty.utils.net.Common;
import com.chat.laty.utils.net.GsonUtils;
import com.chat.laty.utils.net.HttpUtils;
import com.chat.laty.utils.net.TextCallBack;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import cn.xjc_soft.lib_utils.ThreadUtils;

import static cn.xjc_soft.lib_utils.ThreadUtils.runOnUiThread;

/**
 * <AUTHOR>
 * @date 2023/11/28 16:46
 * @description:
 */
public class GeneralSettingPresenter implements GeneralSettingController.Presenter {

    private GeneralSettingController.View mView;

    public GeneralSettingPresenter(GeneralSettingController.View mView) {
        this.mView = mView;
    }

    public void getSettings() {
        HttpUtils.get(Common.APP_GET_SETTINGS, new HashMap<>(), new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<GeneralSettingInfo> mData = FromJsonUtils.fromJson(text, GeneralSettingInfo.class);

                if (200 == mData.getCode()) {
                    if (null != mView) {
                        ThreadUtils.runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showGeneralSetting(mData.getData());
                            }
                        });
                    }
                }

            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
//                mView.showLogin(null);
            }
        });
    }

    public void getSimiInfo() {
        HttpUtils.get(Common.GET_SIMI, new HashMap<>(), new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<SimiInfo> mData = FromJsonUtils.fromJson(text, SimiInfo.class);

                if (200 == mData.getCode()) {
                    if (null != mView) {
                        ThreadUtils.runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.getSimiInfo(mData.getData());
                            }
                        });
                    }
                }

            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
            }
        });
    }

    public void logOffUser() {
        HttpUtils.get(Common.APP_SET_USER_LOG_OFF_USER, new HashMap<>(), new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);

                BaseBean baseBean = GsonUtils.JsonToBean(text, BaseBean.class);
                if (null != mView) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mView.showDeleteCallback(baseBean);
                        }
                    });
                }


            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
//                mView.showLogin(null);
            }
        });
    }

    public void updateSettings(Map<String, Object> params) {
        HttpUtils.getResult(Common.APP_UPDATE_SETTINGS, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
//                mView.showLogin(null);
            }
        });
    }

    public void updateSimiInfo(Map<String, Object> params) {
        HttpUtils.getResult(Common.SUBMIT_SIMI, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
            }
        });
    }

    public void getAccordByNum(int type) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("type", type);
        HttpUtils.get(Common.APP_GET_ACCORD_BY_TYPE, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<WebBeanInfo> mData = FromJsonUtils.fromJson(text, WebBeanInfo.class);

                if (200 == mData.getCode()) {
                    if (null != mView) {
                        ThreadUtils.runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showWebInfo(mData.getData());
                            }
                        });
                    }
                }

            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
//                mView.showLogin(null);
            }
        });
    }

    public void logOutApp() {
        HttpUtils.getResult(Common.APP_SET_USER_LOG_OUT, new HashMap<>(), new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                BaseBean uploadBean = GsonUtils.JsonToBean(text, BaseBean.class);
                if (200 == uploadBean.getCode()) {
                    if (null != mView) {
                        ThreadUtils.runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showLogoutCallback(uploadBean);
                            }
                        });
                    }
                }

            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
//                mView.showLogin(null);
            }
        });
    }

    public void getVipInfo() {
        HashMap<String, Object> params = new HashMap<>();
        HttpUtils.get(Common.APP_GET_VIP_INFO1, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========vipInfo--->" + text);
                ResultData<List<VipInfoModel.VipTypeModel>> data = FromJsonUtils.fromJson(text, VipInfoModel.VipTypeModel.class);
                if (null != data.getData()) {
                    XYApplication.vipTypeModelList.clear();
                    XYApplication.vipTypeModelList.addAll(data.getData());
                    mView.showDeleteCallback(new BaseBean());
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
            }
        });
    }


    @Override
    public void onCreate() {

    }

    @Override
    public void destroy() {

    }
}
