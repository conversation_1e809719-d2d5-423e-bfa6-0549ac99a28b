package com.chat.laty.presenters;

import static cn.xjc_soft.lib_utils.ThreadUtils.runOnUiThread;

import android.content.Context;
import android.text.TextUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.hjq.toast.Toaster;
import com.luck.picture.lib.entity.LocalMedia;
import com.chat.laty.activity.CallPhoneActivity;
import com.chat.laty.base.XYApplication;
import com.chat.laty.controllers.UserInfoController;
import com.chat.laty.entity.AddressInfo;
import com.chat.laty.entity.BalanceInfo;
import com.chat.laty.entity.BaseBean;
import com.chat.laty.entity.LoinEntity;
import com.chat.laty.entity.RYTokenInfo;
import com.chat.laty.entity.RyUserInfo;
import com.chat.laty.entity.UploadFileInfo;
import com.chat.laty.entity.UserFreeInfo;
import com.chat.laty.entity.VipInfoModel;
import com.chat.laty.entity.XYUserInfo;
import com.chat.laty.entity.XinYouCallInfo;
import com.chat.laty.im.message.XYCallVideoContent;
import com.chat.laty.im.message.XYCommentContent;
import com.chat.laty.im.message.XYGiftContent;
import com.chat.laty.im.message.XYGoldCoinsTextContent;
import com.chat.laty.im.message.XYMediaMessageContent;
import com.chat.laty.im.message.XYTextContent;
import com.chat.laty.utils.BgmPlayer;
import com.chat.laty.utils.FromJsonUtils;
import com.chat.laty.utils.JsonUtils;
import com.chat.laty.utils.LogUtil;
import com.chat.laty.utils.ResultData;
import com.chat.laty.utils.XYSPUtils;
import com.chat.laty.utils.net.Common;
import com.chat.laty.utils.net.GsonUtils;
import com.chat.laty.utils.net.HttpUtils;
import com.chat.laty.utils.net.TextCallBack;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import cn.rongcloud.callplus.api.RCCallPlusClient;
import cn.rongcloud.callplus.api.RCCallPlusConfig;
import cn.rongcloud.callplus.api.RCCallPlusResultCode;
import io.rong.imkit.RongIM;
import io.rong.imlib.IRongCallback;
import io.rong.imlib.RongIMClient;
import io.rong.imlib.model.Conversation;
import io.rong.imlib.model.Message;
import io.rong.imlib.model.MessageContent;


/**
 * <AUTHOR>
 * @date 2023/11/28 16:46
 * @description:
 */
public class UserInfoPresenter implements UserInfoController.Presenter {

    private UserInfoController.View mView;

    public UserInfoPresenter(UserInfoController.View mView) {
        this.mView = mView;
    }

    public void uploadFiles(List<LocalMedia> files, String bizPath) {
        HttpUtils.upLoadFiles(Common.APP_UPLOAD_FILE, "file", files, bizPath, new HttpUtils.OnResultCallBack() {
            @Override
            public void OnResultCallBack(String s) {
                LogUtil.e("响应结果", "==========" + s);
//                ResultData<List<String>> mData = FromJsonUtils.fromJson(s, String.class);
                UploadFileInfo uploadBean = GsonUtils.JsonToBean(s, UploadFileInfo.class);
                LogUtil.e("响应结果", "==========" + uploadBean.getResult().size());
                if (200 != uploadBean.getCode()) {
                    Toaster.show(uploadBean.getMessage());
                    if (null != mView) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showUploadResult(null);
                            }
                        });
                    }
                } else {
                    if (null != mView) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showUploadResult(uploadBean.getResult());
                            }
                        });
                    }
                }
            }

            @Override
            public void OnFailCallBack(String s) {
            }
        });
    }


    public void updateUserInfo(String username, boolean sex, String liveAddress, String birthday, String invitationCode) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("nickname", username);
        params.put("birthday", birthday);
        params.put("liveAddress", liveAddress);
        if (!TextUtils.isEmpty(invitationCode))
            params.put("invitationCode", invitationCode);
        if (!TextUtils.isEmpty(XYSPUtils.getString(Common.KEY_APP_USER_OPENID)))
            params.put("openid", XYSPUtils.getString(Common.KEY_APP_USER_OPENID));
        if (!TextUtils.isEmpty(XYSPUtils.getString(Common.KEY_APP_USER_PHONE)))
            params.put("phone", XYSPUtils.getString(Common.KEY_APP_USER_PHONE));

        params.put("sex", sex ? "1" : "2");

        LogUtil.i("updateUserInfo:", JSONObject.toJSONString(params));
        HttpUtils.getResult(Common.APP_REGISTER, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
//                BaseBean callbackBean = GsonUtils.JsonToBean(text, BaseBean.class);
                ResultData<LoinEntity> mData = FromJsonUtils.fromJson(text, LoinEntity.class);
                LogUtil.e("响应结果码", "==========" + mData.getCode());
                if (200 != mData.getCode()) {
                    Toaster.show(mData.getMessage());
                    if (null != mView) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.complementCallback(null);
                            }
                        });
                    }
                } else {
                    if (null != mView) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                XYSPUtils.delete(Common.KEY_APP_USER_OPENID);
                                XYSPUtils.put(Common.KEY_APP_TOKEN, mData.getData().getToken());
                                XYSPUtils.put(Common.KEY_APP_USER_PHONE, username);
                                XYSPUtils.put(Common.KEY_APP_USER_RY_ID, mData.getData().getUserInfo().getId());
                                XYSPUtils.put(Common.KEY_APP_USER_GENDER, mData.getData().getUserInfo().getSex());
                                XYApplication.getDaoInstant().getRyUserInfoDao().insertOrReplaceInTx(mData.getData().getUserInfo());
                                mView.complementCallback(mData.getData());
                            }
                        });
                    }
                }

            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
//                mView.showLogin(null);
            }
        });
    }

    public void getUserInfo() {
        HashMap<String, Object> params = new HashMap<>();
        params.put("userId", XYSPUtils.getString(Common.KEY_APP_USER_RY_ID));
        HttpUtils.get(Common.APP_GET_USER_BY_USERID, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                ResultData<RyUserInfo> mData = FromJsonUtils.fromJson(text, RyUserInfo.class);
                if (200 != mData.getCode()) {
                    Toaster.show(mData.getMessage());
                    if (null != mView) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showUserInfo(null);
                            }
                        });
                    }
                } else {
                    if (null != mView) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showUserInfo(mData.getData());
                            }
                        });
                    }
                }
                LogUtil.e("响应结果", "==========userResult-->" + text);

                if (null != mData.getData()) {
                    XYApplication.getDaoInstant().getRyUserInfoDao().insertOrReplaceInTx(mData.getData());
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
            }
        });
    }

    public void getUserInfo(String userId) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("userId", userId);
        HttpUtils.get(Common.APP_GET_USER_INFO, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<XYUserInfo> mData = FromJsonUtils.fromJson(text, XYUserInfo.class);
                if (200 != mData.getCode()) {
                    Toaster.show(mData.getMessage());
                }
                if (null != mView) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mView.showUserDetails(null == mData.getData() ? null : mData.getData());
                        }
                    });
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
            }
        });
    }

    public void getUserBalance() {
        HttpUtils.get(Common.APP_GET_USER_BALANCE, new HashMap<>(), new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<BalanceInfo> mData = FromJsonUtils.fromJson(text, BalanceInfo.class);
                if (200 == mData.getCode()) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            XYApplication.getDaoInstant().getBalanceInfoDao().deleteAll();
                            BalanceInfo info = mData.getData();
                            info.setUserId(XYSPUtils.getString(Common.KEY_APP_USER_RY_ID));
                            XYApplication.getDaoInstant().getBalanceInfoDao().insertOrReplace(info);
                        }
                    });
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
            }
        });
    }

    public void getUserFee(String userId) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("userId", userId);
        HttpUtils.get(Common.APP_GET_USER_FEE, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<UserFreeInfo> mData = FromJsonUtils.fromJson(text, UserFreeInfo.class);
                if (200 != mData.getCode()) {
                    Toaster.show(mData.getMessage());
                }
                if (null != mView) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mView.showUserFreeInfo(null == mData.getData() ? null : mData.getData());
                        }
                    });
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
            }
        });
    }


    public void addVisit(String userId) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("userId", userId);
        HttpUtils.get(Common.APP_GET_USER_VISITOR, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
            }
        });
    }


    public void getRongCloudToken() {
        HttpUtils.get(Common.APP_GET_RONG_CLOUD_TOKEN, new HashMap<>(), new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<RYTokenInfo> mData = FromJsonUtils.fromJson(text, RYTokenInfo.class);
                if (null != mView) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            if (200 != mData.getCode()) {
                                Toaster.show(mData.getMessage());
                                mView.showRyToken(null);
                            } else {
                                connectIM(mData.getData().getToken());
//                                mView.showRyToken(mData.getData());
                            }
                        }
                    });
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
//                mView.showLogin(null);
            }
        });
    }

    public void followUser(String userId, boolean type) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("toUserId", userId);
        params.put("type", type ? "2" : "1");//关注类型(1-关注 2-取消关注)
        HttpUtils.get(Common.APP_FOLLOW_USER, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
//                ResultData<XYUserInfo> mData = FromJsonUtils.fromJson(text, XYUserInfo.class);
                BaseBean mData = GsonUtils.JsonToBean(text, BaseBean.class);
                if (200 != mData.getCode()) {
                    Toaster.show(mData.getMessage());
                    return;
                }
                if (null != mView) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mView.showFollwoCallback();
                        }
                    });
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
            }
        });
    }

    public void getAddress(Context context) {
        new Thread(new Runnable() {
            @Override
            public void run() {
                String json = JsonUtils.loadJSONFromAsset(context, "city.json");//AddressInfo

                if (!TextUtils.isEmpty(json)) {
                    List<AddressInfo> addressInfos = JSON.parseArray(json, AddressInfo.class);
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mView.showAddress(addressInfos);
                        }
                    });
                }
            }
        }).start();
    }


    public void connectIM(String token) {
        ArrayList<Class<? extends MessageContent>> myMessages = new ArrayList<>();
        myMessages.add(XYTextContent.class);
        myMessages.add(XYGiftContent.class);
        myMessages.add(XYMediaMessageContent.class);
        myMessages.add(XYCallVideoContent.class);
        myMessages.add(XYGoldCoinsTextContent.class);
        myMessages.add(XYCommentContent.class);
        RongIMClient.registerMessageType(myMessages);
//        String targetId = "1725849550466932737";//1729341395424034818（176） 1729776846723137538（133）
        int timeLimit = 0;
        RongIM.connect(token, timeLimit, new RongIMClient.ConnectCallback() {
            @Override
            public void onDatabaseOpened(RongIMClient.DatabaseOpenStatus code) {
                LogUtil.e("响应结果", "==========本地数据库状态###" + code);
                if (RongIMClient.DatabaseOpenStatus.DATABASE_OPEN_SUCCESS.equals(code)) {
                    //本地数据库打开，跳转到会话列表页面
                } else {
                    //数据库打开失败，可以弹出 toast 提示。
                }
            }

            @Override
            public void onSuccess(String userId) {
                //连接成功，如果 onDatabaseOpened() 时没有页面跳转，也可在此时进行跳转。
                LogUtil.e("响应结果", "==========userId###" + userId);
                RCCallPlusResultCode resultCode = RCCallPlusClient.getInstance().init(RCCallPlusConfig.Builder.create().enableTinyStream(false).build());
                LogUtil.e("进入融云初始化", "进入融云初始化");
                LogUtil.e("响应结果", "==========resultCode:::" + resultCode);

                Toaster.show("登录成功");
                mView.showLoginImCallback();
            }

            @Override
            public void onError(RongIMClient.ConnectionErrorCode errorCode) {
                LogUtil.e("响应结果", "==========errorCode###" + errorCode.getValue());
                if (errorCode.equals(RongIMClient.ConnectionErrorCode.RC_CONN_TOKEN_EXPIRE)) {
                    //从 APP 服务请求新 token，获取到新 token 后重新 connect()
                    Toaster.show("从 APP 服务请求新 token，获取到新 token 后重新 connect()");
                } else if (errorCode.equals(RongIMClient.ConnectionErrorCode.RC_CONNECT_TIMEOUT)) {
                    //连接超时，弹出提示，可以引导用户等待网络正常的时候再次点击进行连接
                    Toaster.show("连接超时");
                } else {
                    //其它业务错误码，请根据相应的错误码作出对应处理。
                }
            }
        });
    }

    public void getVipInfo() {
        HashMap<String, Object> params = new HashMap<>();
        HttpUtils.get(Common.APP_GET_VIP_INFO, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========vipInfo--->" + text);
                ResultData<VipInfoModel> data = FromJsonUtils.fromJson(text, VipInfoModel.class);
                if (null != data.getData()) {
                    XYApplication.vipTypeModelList.clear();
                    XYApplication.vipTypeModelList.addAll(data.getData().getVipList());
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
            }
        });
    }


    public void getUserVideoStatus(String userId, String mUserId, int type, Context context) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("userId", userId);
        HttpUtils.get(Common.GET_USER_VIDEO_STATUS, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                BaseBean callbackBean = GsonUtils.JsonToBean(text, BaseBean.class);
                if (200 != callbackBean.getCode()) {
                    Toaster.show("忙线中 请稍后再试~");
                    sendVideoMsg(new XinYouCallInfo("已结束", type + "", "", userId));
                } else {
                    if (BgmPlayer.getInstance(context).isBluetoothHeadsetConnected()) {
                        BgmPlayer.getInstance(XYApplication.getAppApplicationContext()).changeToBluetoothSco();
                    } else {
                        BgmPlayer.getInstance(XYApplication.getAppApplicationContext()).changeToSpeaker();
                    }

                    BgmPlayer mBgmPlayer = BgmPlayer.getInstance(context);
                    mBgmPlayer.playRaw();
                    CallPhoneActivity.start(context, mUserId, 0, type);
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
            }
        });
    }

    private void sendVideoMsg(XinYouCallInfo callInfo) {
        Conversation.ConversationType conversationType = Conversation.ConversationType.PRIVATE;
        XYCallVideoContent messages = XYCallVideoContent.obtain(callInfo);
        Message message = Message.obtain(callInfo.getCallId(), conversationType, messages);
        message.setCanIncludeExpansion(true);
        RongIM.getInstance().sendMessage(message, null, null, new IRongCallback.ISendMediaMessageCallback() {
            @Override
            public void onProgress(Message message, int i) {

            }

            @Override
            public void onCanceled(Message message) {

            }

            @Override
            public void onAttached(Message message) {

            }

            @Override
            public void onSuccess(Message message) {

            }

            @Override
            public void onError(final Message message, final RongIMClient.ErrorCode errorCode) {

            }
        });
    }

    @Override
    public void onCreate() {

    }

    @Override
    public void destroy() {

    }
}
