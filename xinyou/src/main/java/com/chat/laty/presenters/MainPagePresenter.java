package com.chat.laty.presenters;

import static cn.xjc_soft.lib_utils.ThreadUtils.runOnUiThread;

import android.text.TextUtils;

import com.hjq.toast.Toaster;
import com.chat.laty.base.XYApplication;
import com.chat.laty.controllers.MainPageController;
import com.chat.laty.entity.BalanceInfo;
import com.chat.laty.entity.RYTokenInfo;
import com.chat.laty.im.message.XYCallVideoContent;
import com.chat.laty.im.message.XYCommentContent;
import com.chat.laty.im.message.XYGiftContent;
import com.chat.laty.im.message.XYGoldCoinsTextContent;
import com.chat.laty.im.message.XYMediaMessageContent;
import com.chat.laty.im.message.XYTextContent;
import com.chat.laty.utils.FromJsonUtils;
import com.chat.laty.utils.LogUtil;
import com.chat.laty.utils.ResultData;
import com.chat.laty.utils.XYSPUtils;
import com.chat.laty.utils.net.Common;
import com.chat.laty.utils.net.HttpUtils;
import com.chat.laty.utils.net.TextCallBack;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import cn.rongcloud.callplus.api.RCCallPlusClient;
import cn.rongcloud.callplus.api.RCCallPlusConfig;
import cn.rongcloud.callplus.api.RCCallPlusResultCode;
import io.rong.imkit.RongIM;
import io.rong.imlib.RongIMClient;
import io.rong.imlib.model.MessageContent;

/**
 * <AUTHOR>
 * @date 2023/11/28 16:46
 * @description:
 */
public class MainPagePresenter implements MainPageController.Presenter {

    private MainPageController.View mView;

    public MainPagePresenter(MainPageController.View mView) {
        this.mView = mView;
    }

    /**
     * 获取融云token
     */
    public void getRYToken() {
        HttpUtils.get(Common.APP_GET_RONG_CLOUD_TOKEN, new HashMap<>(), new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<RYTokenInfo> mData = FromJsonUtils.fromJson(text, RYTokenInfo.class);
                if (null != mData) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            if (200 != mData.getCode()) {
                                Toaster.show(mData.getMessage());
                                mView.showRyToken(null);
                            } else {
                                connectIM(mData.getData().getToken());
//                                mView.showRyToken(mData.getData());
                            }
                        }
                    });
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
//                mView.showLogin(null);
            }
        });
    }

    public void connectIM(String token) {
        ArrayList<Class<? extends MessageContent>> myMessages = new ArrayList<>();
        myMessages.add(XYTextContent.class);
        myMessages.add(XYGiftContent.class);
        myMessages.add(XYMediaMessageContent.class);
        myMessages.add(XYCallVideoContent.class);
        myMessages.add(XYGoldCoinsTextContent.class);
        myMessages.add(XYCommentContent.class);
        RongIMClient.registerMessageType(myMessages);
//        String targetId = "1725849550466932737";//1729341395424034818（176） 1729776846723137538（133）
        int timeLimit = 0;
        RongIM.connect(token, timeLimit, new RongIMClient.ConnectCallback() {
            @Override
            public void onDatabaseOpened(RongIMClient.DatabaseOpenStatus code) {
                LogUtil.e("响应结果", "==========本地数据库状态###" + code);
                if (RongIMClient.DatabaseOpenStatus.DATABASE_OPEN_SUCCESS.equals(code)) {
                    //本地数据库打开，跳转到会话列表页面
                } else {
                    //数据库打开失败，可以弹出 toast 提示。
                }
            }

            @Override
            public void onSuccess(String userId) {
                //连接成功，如果 onDatabaseOpened() 时没有页面跳转，也可在此时进行跳转。
//                InitOption initOption = new InitOption.Builder().build();
//                RongCoreClient.init(XYApplication.getAppApplicationContext(), XYApplication.APP_KEY, initOption);
//                RCCallPlusResultCode resultCode = RCCallPlusClient.getInstance().init(RCCallPlusConfig.Builder.create().build());
                LogUtil.e("响应结果", "==========userId###" + userId);
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        RCCallPlusResultCode resultCode = RCCallPlusClient.getInstance().init(RCCallPlusConfig.Builder.create().enableTinyStream(false).build());
                        LogUtil.e("进入融云初始化", "进入融云初始化");
                        LogUtil.e("响应结果", "==========resultCode###" + resultCode.getReason());
                    }
                });

//                BgmPlayer bgmPlayer = BgmPlayer.getInstance(XYApplication.getAppApplicationContext());
//                bgmPlayer.changeToSpeaker();

                //                RCCallPlusConfig config = RCCallPlusConfig.Builder.create().build();
//                RCCallPlusResultCode resultCode = RCCallPlusClient.getInstance().init(config);
//                LogUtil.e("响应结果", "==========resultCode:::" + resultCode);
            }

            @Override
            public void onError(RongIMClient.ConnectionErrorCode errorCode) {
                LogUtil.e("响应结果", "==========errorCode###" + errorCode.getValue());
                if (errorCode.equals(RongIMClient.ConnectionErrorCode.RC_CONN_TOKEN_EXPIRE)) {
                    //从 APP 服务请求新 token，获取到新 token 后重新 connect()
                    Toaster.show("从 APP 服务请求新 token，获取到新 token 后重新 connect()");
                } else if (errorCode.equals(RongIMClient.ConnectionErrorCode.RC_CONNECT_TIMEOUT)) {
                    //连接超时，弹出提示，可以引导用户等待网络正常的时候再次点击进行连接
                    Toaster.show("连接超时");
                } else {
                    //其它业务错误码，请根据相应的错误码作出对应处理。
                }
            }
        });
    }

    public void getUserBalance() {
        HttpUtils.get(Common.APP_GET_USER_BALANCE, new HashMap<>(), new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<BalanceInfo> mData = FromJsonUtils.fromJson(text, BalanceInfo.class);
                if (200 == mData.getCode()) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            XYApplication.getDaoInstant().getBalanceInfoDao().deleteAll();
                            BalanceInfo info = mData.getData();
                            info.setUserId(XYSPUtils.getString(Common.KEY_APP_USER_RY_ID));
                            XYApplication.getDaoInstant().getBalanceInfoDao().insertOrReplace(info);
                        }
                    });
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
            }
        });
    }

    @Override
    public void onCreate() {

    }

    @Override
    public void destroy() {

    }


    public void downloadBinding(String data){
        if(TextUtils.isEmpty(data)){
            LogUtil.i("downloadBinding", "参数不能为空");
            return;
        }

        Map<String, Object> params = new HashMap<>();
        params.put("param", data);
        HttpUtils.get(Common.DOWNLOAD_BINDING, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
            }
        });
    }
}
