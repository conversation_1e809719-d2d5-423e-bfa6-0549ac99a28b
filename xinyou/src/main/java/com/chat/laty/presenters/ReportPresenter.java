package com.chat.laty.presenters;

import com.hjq.toast.Toaster;
import com.luck.picture.lib.entity.LocalMedia;
import com.chat.laty.controllers.ReportController;
import com.chat.laty.entity.BaseBean;
import com.chat.laty.entity.UploadFileInfo;
import com.chat.laty.utils.LogUtil;
import com.chat.laty.utils.net.Common;
import com.chat.laty.utils.net.GsonUtils;
import com.chat.laty.utils.net.HttpUtils;
import com.chat.laty.utils.net.TextCallBack;

import java.util.HashMap;
import java.util.List;

import static cn.xjc_soft.lib_utils.ThreadUtils.runOnUiThread;

/**
 * <AUTHOR>
 * @date 2023/11/28 16:46
 * @description:
 */
public class ReportPresenter implements ReportController.Presenter {

    private ReportController.View mView;

    public ReportPresenter(ReportController.View mView) {
        this.mView = mView;
    }


    public void uploadFiles(List<LocalMedia> files, String bizPath) {
        HttpUtils.upLoadFiles(Common.APP_UPLOAD_FILE, "file", files, bizPath, new HttpUtils.OnResultCallBack() {
            @Override
            public void OnResultCallBack(String s) {
                LogUtil.e("响应结果", "==========" + s);
//                ResultData<List<String>> mData = FromJsonUtils.fromJson(s, String.class);
                UploadFileInfo uploadBean = GsonUtils.JsonToBean(s, UploadFileInfo.class);
//                ResultData<List<String>> mData = FromJsonUtils.fromJson(s, String.class);
                if (200 != uploadBean.getCode()) {
                    Toaster.show(uploadBean.getMessage());
                    if (null != mView) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showUploadResults(null);
                            }
                        });
                    }
                } else {
                    if (null != mView) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showUploadResults(uploadBean.getResult());
                            }
                        });
                    }
                }
            }

            @Override
            public void OnFailCallBack(String s) {
                LogUtil.e("响应结果", "==========出错了-->" + s);
            }
        });
    }

    public void addReport(HashMap<String, Object> params) {
        HttpUtils.getResult(Common.APP_ADD_USER_MESSAGE_REPORTS, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========userResult-->" + text);

                BaseBean callbackBean = GsonUtils.JsonToBean(text, BaseBean.class);
                if (null != mView) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mView.showReportCallback(callbackBean);
                        }
                    });
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
            }
        });
    }


    @Override
    public void onCreate() {

    }

    @Override
    public void destroy() {

    }
}
