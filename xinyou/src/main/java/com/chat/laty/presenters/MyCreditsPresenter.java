package com.chat.laty.presenters;

import android.text.TextUtils;

import com.hjq.toast.Toaster;
import com.chat.laty.controllers.CreditsController;
import com.chat.laty.entity.BaseBean;
import com.chat.laty.entity.CreditsFriendInfoModel;
import com.chat.laty.entity.MyCreditsDetailModel;
import com.chat.laty.entity.MyCreditsInfoModel;
import com.chat.laty.utils.FromJsonUtils;
import com.chat.laty.utils.LogUtil;
import com.chat.laty.utils.ResultData;
import com.chat.laty.utils.net.Common;
import com.chat.laty.utils.net.GsonUtils;
import com.chat.laty.utils.net.HttpUtils;
import com.chat.laty.utils.net.TextCallBack;

import java.util.HashMap;
import java.util.List;

import cn.xjc_soft.lib_utils.ThreadUtils;

/**
 * <AUTHOR>
 * @date 2023/11/28 16:46
 * @description:
 */
public class MyCreditsPresenter implements CreditsController.Presenter {

    int type = 0;
    private final CreditsController.IMyInfoView infoIView;

    private final CreditsController.IMyDetailView detailView;

    public void setType(int type) {
        this.type = type;
    }

    public MyCreditsPresenter(CreditsController.IMyInfoView infoIView, CreditsController.IMyDetailView detailView) {
        this.infoIView = infoIView;
        this.detailView = detailView;
    }

    public void getMyCreditsInfo() {
        HashMap<String, Object> params = new HashMap<>();
        HttpUtils.get(Common.APP_GET_CREDITS_INFO, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<MyCreditsInfoModel> data = FromJsonUtils.fromJson(text, MyCreditsInfoModel.class);
                if (null != infoIView) {
                    ThreadUtils.runOnUiThread(() -> infoIView.getInfoSucceed(data.getData()));
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());

                ThreadUtils.runOnUiThread(() -> {
                    String msg = "获取积分信息失败～";
                    if (!TextUtils.isEmpty(e.getMsg())) {
                        msg = e.getMsg();
                    }
                    Toaster.show(msg);
                    if (null != infoIView) {
                        infoIView.getInfoFailed();
                    }
                });
            }
        });
    }

    public void getFriendListV2(String keyword,int page,int pageSize,boolean refresh,boolean loadmore) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("keyword", keyword);
        params.put("pageNo", page);
        params.put("pageSize", pageSize);
        HttpUtils.get(Common.APP_GET_CREDITS_FRIEND_LIST, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<List<CreditsFriendInfoModel>> data = FromJsonUtils.fromJson(text, CreditsFriendInfoModel.class);
                if (null != infoIView) {
                    ThreadUtils.runOnUiThread(() -> infoIView.getFriendListSucceed(data.getData(),refresh,loadmore));
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());

                ThreadUtils.runOnUiThread(() -> {
                    String msg = "获取好友信息失败～";
                    if (!TextUtils.isEmpty(e.getMsg())) {
                        msg = e.getMsg();
                    }
                    Toaster.show(msg);
                    if (null != infoIView)  infoIView.getInfoFailed();
                });
            }
        });
    }

    public void getMyCreditsDetail(int pageNo, int pageSize, boolean load, boolean refresh, boolean loadmore) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("pageNo", pageNo);
        params.put("pageSize", pageSize);
        params.put("changeType", type);
        HttpUtils.getResult(Common.APP_GET_CREDITS_DETAIL, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);

                ResultData<List<MyCreditsDetailModel>> mData = FromJsonUtils.fromJson(text, MyCreditsDetailModel.class);

                if (null != detailView) {
                    ThreadUtils.runOnUiThread(() -> detailView.getDetailSucceed(mData.getData(), load, refresh, loadmore));
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());

                ThreadUtils.runOnUiThread(() -> {
                    String msg = "获取金币详细失败～";
                    if (!TextUtils.isEmpty(e.getMsg())) {
                        msg = e.getMsg();
                    }
                    Toaster.show(msg);
                    if (null != detailView) {
                        detailView.getDetailFailed(load, refresh, loadmore);
                    }
                });
            }
        });
    }


    public void give(String integralId, String toUserId) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("integralId", integralId);
        params.put("toUserId", toUserId);
        HttpUtils.get(Common.APP_GET_CREDITS_GIVE, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);

                BaseBean bean = GsonUtils.JsonToBean(text, BaseBean.class);

                ThreadUtils.runOnUiThread(() -> {
                    if (null != infoIView) {
                        if (bean.getCode() == 200) {
                            Toaster.show("赠送成功～");
                            infoIView.giveSucceed();
                        } else {
                            infoIView.giveFailed();
                            Toaster.show(bean.getMessage());
                        }
                    }
                });
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());

                ThreadUtils.runOnUiThread(() -> {
                    String msg = "赠送失败～";
                    if (!TextUtils.isEmpty(e.getMsg())) {
                        msg = e.getMsg();
                    }
                    Toaster.show(msg);
                    if (null != infoIView) {
                        infoIView.giveFailed();
                    }
                });
            }
        });
    }

    @Override
    public void onCreate() {

    }

    @Override
    public void destroy() {

    }
}
