package com.chat.laty.presenters;

import com.chat.laty.controllers.MessagePageController;
import com.chat.laty.entity.BannerInfo;
import com.chat.laty.entity.MessageUnReadInfo;
import com.chat.laty.entity.RyUserInfo;
import com.chat.laty.utils.FromJsonUtils;
import com.chat.laty.utils.LogUtil;
import com.chat.laty.utils.ResultData;
import com.chat.laty.utils.net.Common;
import com.chat.laty.utils.net.HttpUtils;
import com.chat.laty.utils.net.TextCallBack;
import com.hjq.toast.Toaster;

import java.util.HashMap;
import java.util.List;

import static cn.xjc_soft.lib_utils.ThreadUtils.runOnUiThread;

import cn.xjc_soft.lib_utils.ViewUtils;

/**
 * <AUTHOR>
 * @date 2023/11/28 16:46
 * @description:
 */
public class MessagePagePresenter implements MessagePageController.Presenter {

    private MessagePageController.View mView;

    public MessagePagePresenter(MessagePageController.View mView) {
        this.mView = mView;
    }

    /**
     * 获取融云token
     */
    public void getMsgCount() {
        HttpUtils.get(Common.APP_GET_USER_MESSAGE_NUM, new HashMap<>(), new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<MessageUnReadInfo> mData = FromJsonUtils.fromJson(text, MessageUnReadInfo.class);
                if (null != mView) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            if (200 == mData.getCode()) {
                                mView.showMsgCount(mData.getData());
                            }
                        }
                    });
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
//                mView.showLogin(null);
            }
        });
    }

    @Override
    public void onCreate() {

    }

    @Override
    public void destroy() {

    }


    public void getUserInfo(String userId) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("userId", userId);
        HttpUtils.get(Common.APP_GET_CHAT_RY_USERINFO, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========userResult-->" + text);
                ResultData<RyUserInfo> mData = FromJsonUtils.fromJson(text, RyUserInfo.class);
                if (null != mView) {
                    ViewUtils.runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mView.showUserInfo(mData.getData());
                        }
                    });
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
            }
        });
    }

    public void getBanners() {
        HttpUtils.get(Common.APP_GET_FIRST_PAGE_BANNERS, new HashMap<>(), new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========banner-->" + text);
                ResultData<List<BannerInfo>> mData = FromJsonUtils.fromJson(text, BannerInfo.class);
                if (200 != mData.getCode()) {
                    Toaster.show(mData.getMessage());
                    if (null != mView) {
                        ViewUtils.runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showBanners(null);
                            }
                        });
                    }
                } else {
                    if (null != mView) {
                        ViewUtils.runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showBanners(mData.getData());

                            }
                        });
                    }
                }

            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
//                mView.showLogin(null);
            }
        });
    }
}
