package com.chat.laty.presenters;

import com.hjq.toast.Toaster;
import com.chat.laty.controllers.SystemMsgController;
import com.chat.laty.entity.ReportMessageInfo;
import com.chat.laty.entity.SystemMessageInfo;
import com.chat.laty.utils.FromJsonUtils;
import com.chat.laty.utils.LogUtil;
import com.chat.laty.utils.ResultData;
import com.chat.laty.utils.net.Common;
import com.chat.laty.utils.net.HttpUtils;
import com.chat.laty.utils.net.TextCallBack;

import java.util.HashMap;
import java.util.List;

import static cn.xjc_soft.lib_utils.ThreadUtils.runOnUiThread;

/**
 * <AUTHOR>
 * @date 2023/11/28 16:46
 * @description:
 */
public class SystemMsgPresenter implements SystemMsgController.Presenter {

    private SystemMsgController.View mView;

    public SystemMsgPresenter(SystemMsgController.View mView) {
        this.mView = mView;
    }

    /**
     * 系统消息
     */
    public void getSystemByType(int pageNo) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("pageNo",pageNo);
        params.put("PageSize",20);
        HttpUtils.get(Common.APP_GET_USER_MESSAGE_SYS, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<List<SystemMessageInfo>> mData = FromJsonUtils.fromJson(text, SystemMessageInfo.class);
                if (null != mView) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            if (200 != mData.getCode()) {
                                Toaster.show(mData.getMessage());
                                mView.showMessages(null);
                            } else {
                                mView.showMessages(mData.getData());
                            }
                        }
                    });
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
//                mView.showLogin(null);
            }
        });
    }


    public void getLikeByType(int pageNo) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("pageNo",pageNo);
        params.put("PageSize",20);
        HttpUtils.get(Common.APP_GET_USER_MESSAGE_LIKES, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<List<SystemMessageInfo>> mData = FromJsonUtils.fromJson(text, SystemMessageInfo.class);
                if (null != mView) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            if (200 != mData.getCode()) {
                                Toaster.show(mData.getMessage());
                                mView.showLikeMessages(null);
                            } else {
                                mView.showLikeMessages(mData.getData());
                            }
                        }
                    });
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
//                mView.showLogin(null);
            }
        });
    }


    public void getReportByType(int pageNo) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("pageNo",pageNo);
        params.put("PageSize",20);
        HttpUtils.get(Common.APP_GET_USER_MESSAGE_REPORTS, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<List<ReportMessageInfo>> mData = FromJsonUtils.fromJson(text, ReportMessageInfo.class);
                if (null != mView) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            if (200 != mData.getCode()) {
                                Toaster.show(mData.getMessage());
                                mView.showReportMessages(null);
                            } else {
                                mView.showReportMessages(mData.getData());
                            }
                        }
                    });
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
//                mView.showLogin(null);
            }
        });
    }


    @Override
    public void onCreate() {

    }

    @Override
    public void destroy() {

    }
}
