package com.chat.laty.presenters;

import com.hjq.toast.Toaster;
import com.chat.laty.controllers.VisitorController;
import com.chat.laty.entity.VisitorUserInfo;
import com.chat.laty.utils.FromJsonUtils;
import com.chat.laty.utils.LogUtil;
import com.chat.laty.utils.ResultData;
import com.chat.laty.utils.net.Common;
import com.chat.laty.utils.net.HttpUtils;
import com.chat.laty.utils.net.TextCallBack;

import java.util.HashMap;
import java.util.List;

import static cn.xjc_soft.lib_utils.ThreadUtils.runOnUiThread;

/**
 * <AUTHOR>
 * @date 2023/11/28 16:46
 * @description:
 */
public class VisitorPresenter implements VisitorController.Presenter {

    private VisitorController.View mView;

    public VisitorPresenter(VisitorController.View mView) {
        this.mView = mView;
    }

    /**
     * 获取融云token
     */
    public void getUserVisitorList(int pageNum) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("pageNo", pageNum);
        params.put("pageSize", 20);
        HttpUtils.get(Common.APP_GET_USER_VISITOR_LIST, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<List<VisitorUserInfo>> mData = FromJsonUtils.fromJson(text, VisitorUserInfo.class);
                if (200 != mData.getCode()) {
                    Toaster.show(mData.getMessage());
                }
                if (null != mView) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mView.showVisitorList(mData.getData());
                        }
                    });
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
//                mView.showLogin(null);
            }
        });
    }

    @Override
    public void onCreate() {

    }

    @Override
    public void destroy() {

    }
}
