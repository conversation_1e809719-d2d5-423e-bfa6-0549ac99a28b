package com.chat.laty.presenters;

import static cn.xjc_soft.lib_utils.ThreadUtils.runOnUiThread;

import com.chat.laty.controllers.AuthenticationController;
import com.chat.laty.entity.UserCenterInfo;
import com.hjq.toast.Toaster;
import com.luck.picture.lib.entity.LocalMedia;
import com.chat.laty.entity.BaseBean;
import com.chat.laty.entity.BaseEntity;
import com.chat.laty.entity.UploadImgInfo;
import com.chat.laty.utils.FromJsonUtils;
import com.chat.laty.utils.LogUtil;
import com.chat.laty.utils.ResultData;
import com.chat.laty.utils.net.Common;
import com.chat.laty.utils.net.GsonUtils;
import com.chat.laty.utils.net.HttpUtils;
import com.chat.laty.utils.net.TextCallBack;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/12 16:46
 * @description:
 */
public class AuthenticationPresenter implements AuthenticationController.Presenter {

    private AuthenticationController.View mView;

    public AuthenticationPresenter(AuthenticationController.View mView) {
        this.mView = mView;
    }

    /**
     * 实名认证
     */
    public void getUserVerify() {
        HttpUtils.get(Common.APP_POST_AUTHENTICATION_GET_USER_VERIFY, new HashMap<>(), new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<BaseEntity> mData = FromJsonUtils.fromJson(text, BaseEntity.class);
                if (null != mView) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mView.showUserVerifyStatus(mData.getData());
                        }
                    });
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
//                mView.showLogin(null);
            }
        });
    }

    public void submitRealName(String identifyNum, String userName) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("identifyNum", identifyNum);
        params.put("userName", userName);
        HttpUtils.get(Common.APP_POST_AUTHENTICATION_REAL_NAME, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                BaseBean base = GsonUtils.JsonToBean(text, BaseBean.class);
//                ResultData<RYTokenInfo> mData = FromJsonUtils.fromJson(text, RYTokenInfo.class);
                if (null != mView) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mView.showRealNameCallback(base);
                        }
                    });
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
//                mView.showLogin(null);
            }
        });
    }


    public void submitRealPerson(String imgUrl) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("imgUrl", imgUrl);
        HttpUtils.get(Common.APP_POST_AUTHENTICATION_REAL_PERSON, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                BaseBean base = GsonUtils.JsonToBean(text, BaseBean.class);
//                ResultData<RYTokenInfo> mData = FromJsonUtils.fromJson(text, RYTokenInfo.class);
                if (null != mView) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mView.showRealPersonCallback(base);
                        }
                    });

                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
//                mView.showLogin(null);
            }
        });
    }

    public void uploadFiles(List<LocalMedia> files, String bizPath) {
        HttpUtils.upLoadFiles(Common.APP_UPLOAD_FILE_TUM, "file", files, bizPath, new HttpUtils.OnResultCallBack() {
            @Override
            public void OnResultCallBack(String s) {
                LogUtil.e("响应结果", "==========" + s);
                ResultData<List<UploadImgInfo>> mData = FromJsonUtils.fromJson(s, UploadImgInfo.class);
//                UploadFileInfo uploadBean = GsonUtils.JsonToBean(s, UploadFileInfo.class);
//                LogUtil.e("响应结果", "==========" + uploadBean.getResult().size());
                if (200 != mData.getCode()) {
                    Toaster.show(mData.getMessage());
                    if (null != mView) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showUploadResult(null);
                            }
                        });
                    }
                } else {
                    if (null != mView) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showUploadResult(mData.getData());
                            }
                        });
                    }
                }
            }

            @Override
            public void OnFailCallBack(String s) {
            }
        });
    }

    public void getUserCenterInfo() {
        HttpUtils.get(Common.APP_USER_CENTER_INFO, new HashMap<>(), new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========userInfo::" + text);
                ResultData<UserCenterInfo> mData = FromJsonUtils.fromJson(text, UserCenterInfo.class);
                if (200 == mData.getCode()) {
                    if (null != mView) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                LogUtil.e("响应结果", "==========" + mData.getData().toString());
                                mView.showUserCenterDetails(mData.getData());
                            }
                        });
                    }
                }

            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
//                mView.showLogin(null);
            }
        });
    }

    @Override
    public void onCreate() {

    }

    @Override
    public void destroy() {

    }
}
