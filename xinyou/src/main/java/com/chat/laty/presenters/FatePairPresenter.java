package com.chat.laty.presenters;

import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.chat.laty.controllers.FatePairController;
import com.chat.laty.entity.FatePairingInfo;
import com.chat.laty.entity.WebBeanInfo;
import com.chat.laty.utils.FromJsonUtils;
import com.chat.laty.utils.LogUtil;
import com.chat.laty.utils.ResultData;
import com.chat.laty.utils.XYSPUtils;
import com.chat.laty.utils.net.Common;
import com.chat.laty.utils.net.GsonUtils;
import com.chat.laty.utils.net.HttpUtils;
import com.chat.laty.utils.net.TextCallBack;

import java.util.HashMap;

import cn.xjc_soft.lib_utils.ThreadUtils;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.WebSocket;
import okhttp3.WebSocketListener;
import okio.ByteString;

import static cn.xjc_soft.lib_utils.ThreadUtils.runOnUiThread;

/**
 * <AUTHOR>
 * @date 2023/11/28 16:46
 * @description:
 */
public class FatePairPresenter implements FatePairController.Presenter {

    private FatePairController.View mView;

    //开始连接
    WebSocket mWebsocket;


    public FatePairPresenter(FatePairController.View mView) {
        this.mView = mView;
    }

    public void startPair(String userId) {

        LogUtil.e("接口链接", "WebSocket==========userId-->" + userId);
        String url = Common.mFinalWebsoketAdd + userId;
        String head = XYSPUtils.getString(Common.KEY_APP_TOKEN);
        OkHttpClient client = HttpUtils.getClient();
        Request.Builder build = new Request.Builder();
        if (!TextUtils.isEmpty(head))
            build.addHeader("Sec-WebSocket-Protocol", head);
        build.url(url);

        mWebsocket = client.newWebSocket(build.build(), new WebSocketListener() {
            @Override
            public void onClosed(@NonNull WebSocket webSocket, int code, @NonNull String reason) {
                super.onClosed(webSocket, code, reason);
                LogUtil.e("接口链接", "WebSocket==========onClosed");
            }

            @Override
            public void onClosing(@NonNull WebSocket webSocket, int code, @NonNull String reason) {
                super.onClosing(webSocket, code, reason);
                LogUtil.e("接口链接", "WebSocket==========onClosing");
            }

            @Override
            public void onFailure(@NonNull WebSocket webSocket, @NonNull Throwable t, @Nullable Response response) {
                super.onFailure(webSocket, t, response);
                LogUtil.e("接口链接", "WebSocket==========onFailure");
            }

            @Override
            public void onMessage(@NonNull WebSocket webSocket, @NonNull String text) {
                super.onMessage(webSocket, text);
                LogUtil.e("接口链接", "WebSocket==========onMessage-->" + text);

                FatePairingInfo base = GsonUtils.JsonToBean(text, FatePairingInfo.class);

                if (null != mView) {
                    runOnUiThread(() -> {
                        mView.onFatePairMessageCallback(base, text);
                    });
                }
            }

            @Override
            public void onMessage(@NonNull WebSocket webSocket, @NonNull ByteString bytes) {
                super.onMessage(webSocket, bytes);
                LogUtil.e("接口链接", "WebSocket==========onMessage--->bytes");
            }

            @Override
            public void onOpen(@NonNull WebSocket webSocket, @NonNull Response response) {
                super.onOpen(webSocket, response);
                LogUtil.e("接口链接", "WebSocket==========onOpen");
                sedMsgWeb();
            }
        });
    }

    public void getAccordByNum(int type) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("type", type);
        HttpUtils.get(Common.APP_GET_ACCORD_BY_TYPE, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<WebBeanInfo> mData = FromJsonUtils.fromJson(text, WebBeanInfo.class);

                if (200 == mData.getCode()) {
                    if (null != mView) {
                        ThreadUtils.runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showWebInfo(mData.getData());
                            }
                        });
                    }
                }

            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
//                mView.showLogin(null);
            }
        });
    }

    public void sedMsgWeb() {
        if (mWebsocket != null)
            mWebsocket.send("{\"type\":\"1\",\"remark\":\"缘分配对\"}");
    }

    public void stopWebSocket() {
        if (mWebsocket != null) {
            mWebsocket.close(1000, "Goodbye, WebSocket!");
        }
    }

    @Override
    public void onCreate() {

    }

    @Override
    public void destroy() {
        if (mWebsocket != null) {
            mWebsocket.cancel();
            mWebsocket = null;
        }

    }
}
