package com.chat.laty.presenters;

import static cn.xjc_soft.lib_utils.ThreadUtils.runOnUiThread;

import android.content.Context;
import android.text.TextUtils;

import com.luck.picture.lib.entity.LocalMedia;
import com.chat.laty.controllers.DynamicController;
import com.chat.laty.entity.CostBean;
import com.chat.laty.entity.UploadFileInfo;
import com.chat.laty.entity.UploadImgInfo;
import com.chat.laty.utils.FromJsonUtils;
import com.chat.laty.utils.LogUtil;
import com.chat.laty.utils.ResultData;
import com.chat.laty.utils.net.Common;
import com.chat.laty.utils.net.GsonUtils;
import com.chat.laty.utils.net.HttpUtils;
import com.chat.laty.utils.net.TextCallBack;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/28 16:46
 * @description:
 */
public class DynamicPresenter implements DynamicController.Presenter {

    private DynamicController.UploadView uploadView;
    private DynamicController.ReleaseView releaseView;

    public DynamicPresenter(DynamicController.UploadView uploadView, DynamicController.ReleaseView releaseView) {
        this.uploadView = uploadView;
        this.releaseView = releaseView;
    }

    public void release(String text, String urls, int type, String isMents, Context context) {

        boolean result = new CommunityPresenter(null, null).isNotRzUser(context);
        if (result) {
            if (releaseView != null) releaseView.releaseFailed("");
            return;
        }

        HashMap<String, Object> params = new HashMap<>();
        params.put("textContent", text);
        params.put("fileUrl", urls);
        params.put("type", type);
        params.put("isMents", isMents);

        HttpUtils.getResult(Common.APP_POST_RELEASE, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<CostBean> mData = FromJsonUtils.fromJson(text, CostBean.class);

                runOnUiThread(() -> {
                    if (200 == mData.getCode()) {
                        if (null != releaseView) releaseView.releaseSucceed();
                    } else {
                        String msg = "发布失败，请重试~";
                        if (!TextUtils.isEmpty(mData.getMessage())) {
                            msg = mData.getMessage();
                        }
                        if (releaseView != null) releaseView.releaseFailed(msg);
                    }
                });

            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());

                runOnUiThread(() -> {
                    String msg = "发布失败，请重试~";
                    if (e.getMsg() != null) {
                        msg = e.getMsg();
                    }
                    if (releaseView != null) releaseView.releaseFailed(msg);
                });
            }
        });
    }

    public void uploadFiles(LocalMedia file) {
        List<LocalMedia> files = new ArrayList<>();
        files.add(file);

        if (file.getMimeType().contains("image")) {
            HttpUtils.upLoadFiles(Common.APP_UPLOAD_FILE_TUM, "file", files, "dynamic", new HttpUtils.OnResultCallBack() {
                @Override
                public void OnResultCallBack(String s) {
                    ResultData<List<UploadImgInfo>> mData = FromJsonUtils.fromJson(s, UploadImgInfo.class);
                    if (200 == mData.getCode()) {
                        if (null != uploadView) {
                            runOnUiThread(() -> uploadView.uploadSucceed(file, mData.getData()));
                        }
                    }
                }

                @Override
                public void OnFailCallBack(String s) {
                    uploadView.uploadFailed(file);
                }
            });
        } else {

            HttpUtils.upLoadFiles(Common.APP_UPLOAD_FILE_V2, "file", files, "dynamic", new HttpUtils.OnResultCallBack() {
                @Override
                public void OnResultCallBack(String s) {
                    LogUtil.e("响应结果", "==========" + s);
//                ResultData<List<String>> mData = FromJsonUtils.fromJson(s, String.class);
                    UploadFileInfo uploadBean = GsonUtils.JsonToBean(s, UploadFileInfo.class);
                    LogUtil.e("响应结果", "==========" + uploadBean.getResult().size());
                    if (200 == uploadBean.getCode()) {
                        if (null != uploadView) {
                            runOnUiThread(() -> {
                                List<UploadImgInfo> list = new ArrayList<>();
                                for (String item : uploadBean.getResult()) {
                                    UploadImgInfo info = new UploadImgInfo();
                                    info.setImgUrl(item);
                                    list.add(info);
                                }
                                uploadView.uploadSucceed(file, list);
                            });
                        }
                    }

                }

                @Override
                public void OnFailCallBack(String s) {
                    runOnUiThread(() -> uploadView.uploadFailed(file));
                }
            });
        }
    }

    @Override
    public void onCreate() {

    }

    @Override
    public void destroy() {

    }
}
