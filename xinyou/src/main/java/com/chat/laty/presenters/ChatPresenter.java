package com.chat.laty.presenters;

import com.hjq.permissions.OnPermissionCallback;
import com.hjq.permissions.Permission;
import com.hjq.permissions.XXPermissions;
import com.hjq.toast.Toaster;
import com.chat.laty.activity.CallPhoneActivity;
import com.chat.laty.base.XYApplication;
import com.chat.laty.controllers.ChatController;
import com.chat.laty.entity.BalanceInfo;
import com.chat.laty.entity.BaseBean;
import com.chat.laty.entity.CommonPhrasesInfo;
import com.chat.laty.entity.GiftInfo;
import com.chat.laty.entity.RyUserInfo;
import com.chat.laty.entity.UserCenterInfo;
import com.chat.laty.entity.UserFreeInfo;
import com.chat.laty.entity.XYUserInfo;
import com.chat.laty.entity.XinYouCallInfo;
import com.chat.laty.im.message.XYCallVideoContent;
import com.chat.laty.utils.BgmPlayer;
import com.chat.laty.utils.FromJsonUtils;
import com.chat.laty.utils.LogUtil;
import com.chat.laty.utils.PermissionInterceptor;
import com.chat.laty.utils.ResultData;
import com.chat.laty.utils.XYSPUtils;
import com.chat.laty.utils.net.Common;
import com.chat.laty.utils.net.GsonUtils;
import com.chat.laty.utils.net.HttpUtils;
import com.chat.laty.utils.net.TextCallBack;

import java.util.HashMap;
import java.util.List;

import cn.xjc_soft.lib_utils.ViewUtils;
import io.rong.imkit.IMCenter;
import io.rong.imkit.RongIM;
import io.rong.imlib.IRongCallback;
import io.rong.imlib.RongIMClient;
import io.rong.imlib.model.Conversation;
import io.rong.imlib.model.Message;

import static cn.xjc_soft.lib_utils.ThreadUtils.runOnUiThread;

import android.content.Context;

/**
 * <AUTHOR>
 * @date 2023/11/28 16:46
 * @description:
 */
public class ChatPresenter implements ChatController.Presenter {

    private ChatController.View mView;

    public ChatPresenter(ChatController.View mView) {
        this.mView = mView;
    }


    public void getRongYunUserInfo(String userId) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("userId", userId);
        HttpUtils.get(Common.APP_GET_CHAT_RY_USERINFO, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========userResult-->" + text);
                ResultData<RyUserInfo> mData = FromJsonUtils.fromJson(text, RyUserInfo.class);
//                if (null != mData.getData()) {
//                    XYApplication.getDaoInstant().getRyUserInfoDao().insertOrReplaceInTx(mData.getData());
//                }
//                ResultData<AppVersionInfo> mData = FromJsonUtils.fromJson(text, AppVersionInfo.class);
                if (null != mView) {
                    ViewUtils.runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mView.showUserInfo(mData.getData());
                        }
                    });
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
            }
        });
    }

    public void getCommonPhrasesList() {
        HttpUtils.get(Common.APP_GET_COMMON_PHRASES, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========userResult-->" + text);
//                CommonPhrasesInfo callbackBean = GsonUtils.JsonToBean(text, CommonPhrasesInfo.class);
                ResultData<List<CommonPhrasesInfo>> mData = FromJsonUtils.fromJson(text, CommonPhrasesInfo.class);
                if (null != mView) {
                    ViewUtils.runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mView.showCommonPhrases(mData.getData());
                        }
                    });
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
            }
        });
    }

    public void getUserCenterInfo() {
        HttpUtils.get(Common.APP_USER_CENTER_INFO, new HashMap<>(), new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========userInfo::" + text);
                ResultData<UserCenterInfo> mData = FromJsonUtils.fromJson(text, UserCenterInfo.class);
                if (200 == mData.getCode()) {
                    if (null != mView) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                LogUtil.e("响应结果", "==========" + mData.getData().toString());
                            }
                        });
                    }
                }

            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
//                mView.showLogin(null);
            }
        });
    }

    public void deleteCommonPhrasesList(String id) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("id", id);
        HttpUtils.get(Common.APP_DELETE_COMMON_PHRASES, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========userResult-->" + text);

                BaseBean baseBean = GsonUtils.JsonToBean(text, BaseBean.class);
                if (null != mView) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mView.showDeleteCallback(baseBean);
                        }
                    });
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
            }
        });
    }


    public void addCommonPhrases(String content) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("commonPhrases", content);
        HttpUtils.get(Common.APP_ADD_COMMON_PHRASES, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========userResult-->" + text);

                BaseBean baseBean = GsonUtils.JsonToBean(text, BaseBean.class);
                if (null != mView) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mView.showAddCallback(baseBean);
                        }
                    });
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
            }
        });
    }

    public void getUserInfo(String userId) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("userId", userId);
        HttpUtils.get(Common.APP_GET_USER_INFO, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========他人信息：" + text);
                ResultData<XYUserInfo> mData = FromJsonUtils.fromJson(text, XYUserInfo.class);
                if (200 != mData.getCode()) {
                    Toaster.show(mData.getMessage());
//                    deleteErrorUser(userId);
                }
                if (null != mView) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mView.showUserDetails(null == mData.getData() ? null : mData.getData());
                        }
                    });
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
            }
        });
    }

    public void getUserBalance() {
        HttpUtils.get(Common.APP_GET_USER_BALANCE, new HashMap<>(), new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========用户余额--->" + text);
                ResultData<BalanceInfo> mData = FromJsonUtils.fromJson(text, BalanceInfo.class);
                if (200 == mData.getCode()) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            XYApplication.getDaoInstant().getBalanceInfoDao().deleteAll();
                            BalanceInfo info = mData.getData();
                            info.setUserId(XYSPUtils.getString(Common.KEY_APP_USER_RY_ID));
                            XYApplication.getDaoInstant().getBalanceInfoDao().insertOrReplace(info);
                            mView.showUserBalance(null == mData.getData() ? null : mData.getData());
                        }
                    });
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
            }
        });
    }


    public void getGift() {
        HttpUtils.get(Common.APP_GET_GIFT_LIST, new HashMap<>(), new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========userResult-->" + text);
                ResultData<List<GiftInfo>> mData = FromJsonUtils.fromJson(text, GiftInfo.class);
                if (null != mData.getData()) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mView.showGiftResult(mData.getData());
                        }
                    });

                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
            }
        });
    }

    public void updateUserNickName(String userName, String userId) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("notesUserId", userId);
        params.put("notes", userName);
        HttpUtils.getResult(Common.APP_SET_USER_NICK_NAME, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========userResult-->" + text);
                BaseBean callbackBean = GsonUtils.JsonToBean(text, BaseBean.class);
                if (null != callbackBean) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mView.showSaveResult(callbackBean);
                        }
                    });
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
            }
        });
    }

    public void getUserFee(String userId) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("userId", userId);
        HttpUtils.get(Common.APP_GET_USER_FEE, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<UserFreeInfo> mData = FromJsonUtils.fromJson(text, UserFreeInfo.class);
                if (200 != mData.getCode()) {
                    Toaster.show(mData.getMessage());
                }
                if (null != mView) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mView.showUserFreeInfo(null == mData.getData() ? null : mData.getData());
                        }
                    });
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
            }
        });
    }


    /**
     * 用户通话中判断
     * @param userId
     * @param mUserId
     * @param type
     * @param context
     */
    public void getUserVideoStatus(String userId, String mUserId, int type, Context context) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("userId", userId);
        HttpUtils.get(Common.GET_USER_VIDEO_STATUS, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                BaseBean callbackBean = GsonUtils.JsonToBean(text, BaseBean.class);
                if (200 != callbackBean.getCode()) {
                    Toaster.show("忙线中 请稍后再试~");
                    sendVideoMsg(new XinYouCallInfo("已结束", type + "", "", userId));
                } else {
                    XXPermissions.with(context).permission(Permission.RECORD_AUDIO).permission(Permission.CAMERA).interceptor(new PermissionInterceptor()).request(new OnPermissionCallback() {
                        @Override
                        public void onGranted(List<String> permissions, boolean all) {
                            if (!all) {
                                return;
                            }
                            BgmPlayer mBgmPlayer = BgmPlayer.getInstance(context);
                            mBgmPlayer.playRaw();
                            CallPhoneActivity.start(context, mUserId, 0, type);
                        }
                    });
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
            }
        });
    }

    private void sendVideoMsg(XinYouCallInfo callInfo) {
        Conversation.ConversationType conversationType = Conversation.ConversationType.PRIVATE;
        XYCallVideoContent messages = XYCallVideoContent.obtain(callInfo);
        Message message = Message.obtain(callInfo.getCallId(), conversationType, messages);
        message.setCanIncludeExpansion(true);
        RongIM.getInstance().sendMessage(message, null, null, new IRongCallback.ISendMediaMessageCallback() {
            @Override
            public void onProgress(Message message, int i) {

            }

            @Override
            public void onCanceled(Message message) {

            }

            @Override
            public void onAttached(Message message) {

            }

            @Override
            public void onSuccess(Message message) {

            }

            @Override
            public void onError(final Message message, final RongIMClient.ErrorCode errorCode) {

            }
        });
    }


    private void deleteErrorUser(String targetId) {
        Conversation.ConversationType conversationType = Conversation.ConversationType.PRIVATE;

        IMCenter.getInstance().removeConversation(conversationType, targetId, new RongIMClient.ResultCallback<Boolean>() {
            @Override
            public void onSuccess(Boolean aBoolean) {

            }

            @Override
            public void onError(RongIMClient.ErrorCode e) {

            }
        });
    }


    @Override
    public void onCreate() {

    }

    @Override
    public void destroy() {

    }
}
