package com.chat.laty.presenters;

import static cn.xjc_soft.lib_utils.ThreadUtils.runOnUiThread;

import android.os.CountDownTimer;

import com.hjq.toast.Toaster;
import com.chat.laty.base.XYApplication;
import com.chat.laty.contants.Constant;
import com.chat.laty.controllers.LoginController;
import com.chat.laty.entity.BaseBean;
import com.chat.laty.entity.LoginEntity;
import com.chat.laty.entity.LoinEntity;
import com.chat.laty.entity.RYTokenInfo;
import com.chat.laty.entity.WXLoginInfo;
import com.chat.laty.entity.WebBeanInfo;
import com.chat.laty.im.message.XYCallVideoContent;
import com.chat.laty.im.message.XYCommentContent;
import com.chat.laty.im.message.XYGiftContent;
import com.chat.laty.im.message.XYGoldCoinsTextContent;
import com.chat.laty.im.message.XYMediaMessageContent;
import com.chat.laty.im.message.XYTextContent;
import com.chat.laty.utils.BgmPlayer;
import com.chat.laty.utils.FromJsonUtils;
import com.chat.laty.utils.LogUtil;
import com.chat.laty.utils.ResultData;
import com.chat.laty.utils.XYSPUtils;
import com.chat.laty.utils.net.Common;
import com.chat.laty.utils.net.GsonUtils;
import com.chat.laty.utils.net.HttpUtils;
import com.chat.laty.utils.net.TextCallBack;

import java.util.ArrayList;
import java.util.HashMap;

import cn.rongcloud.callplus.api.RCCallPlusClient;
import cn.rongcloud.callplus.api.RCCallPlusConfig;
import cn.rongcloud.callplus.api.RCCallPlusResultCode;
import cn.xjc_soft.lib_utils.ThreadUtils;
import io.rong.imkit.RongIM;
import io.rong.imlib.RongIMClient;
import io.rong.imlib.model.InitOption;
import io.rong.imlib.model.MessageContent;

/**
 * <AUTHOR>
 * @date 2022/8/16 14:01
 * @description:登录 presenter
 */
public class LoginPresenter implements LoginController.Presenter {

    private LoginController.View mView;

    CountDownTimer countDownTimer;

    public LoginPresenter(LoginController.View mView) {
        this.mView = mView;
        initDatas();
    }

    /**
     * 登录
     *
     * @param username
     * @param pwd
     */
    public void login(String username, String pwd) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("phone", username);
        params.put("password", pwd);
        HttpUtils.getResult(Common.APP_LOGIN, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<LoinEntity> mData = FromJsonUtils.fromJson(text, LoinEntity.class);

                LogUtil.e("响应结果码", "==========" + mData.getCode());
                if (200 != mData.getCode()) {
                    Toaster.show(mData.getMessage());
                    if (null != mView) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showLogin(null);
                            }
                        });
                    }
                } else {
                    if (null != mView) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                XYSPUtils.put(Common.KEY_APP_TOKEN, mData.getData().getToken());
                                XYSPUtils.put(Common.KEY_APP_USER_PHONE, username);
                                XYSPUtils.put(Common.KEY_APP_USER_GENDER, mData.getData().getUserInfo().getSex());
//                                XYApplication.getDaoInstant().getUserInfoDao().insertOrReplace(mData.getData().getUserInfo());
                                mView.showLogin(mData.getData());
                            }
                        });
                    }
                }

            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
//                mView.showLogin(null);
            }
        });
    }

    public void loginBySms(String username, String sms) {
        if (null != countDownTimer)
            countDownTimer.cancel();
        HashMap<String, Object> params = new HashMap<>();
        params.put("phone", username);
        params.put("code", sms);
        HttpUtils.getResult(Common.APP_LOGIN_SMS, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<LoginEntity> mData = FromJsonUtils.fromJson(text, LoginEntity.class);
//
//                LogUtil.e("响应结果码", "==========" + mData.getCode());
                if (200 != mData.getCode()) {
                    Toaster.show(mData.getMessage());
                    if (null != mView) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showLoginCallBack(null);
                            }
                        });
                    }
                } else {
                    if (null != mView) {
                        XYSPUtils.put(Common.KEY_APP_USER_PHONE, username);
                        if (0 == mData.getData().getIsNewUser()) {
                            XYSPUtils.put(Common.KEY_APP_TOKEN, mData.getData().getToken());
                            XYSPUtils.put(Common.KEY_APP_USER_RY_ID, mData.getData().getUserInfo().getId());
                            XYSPUtils.put(Common.KEY_APP_USER_GENDER, mData.getData().getUserInfo().getSex());
                            XYApplication.getDaoInstant().clear();
                            XYApplication.getDaoInstant().getRyUserInfoDao().insertOrReplace(mData.getData().getUserInfo());
                        }
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
//                                RyUserInfo userInfo2 = XYApplication.getCurrentUserInfo();
//                                if (!TextUtils.isEmpty(mData.getData().getUserInfo().getIsName()))
//                                    userInfo2.setIsName(mData.getData().getUserInfo().getIsName());
//                                userInfo2.setSex(mData.getData().getUserInfo().getSex());
//                                userInfo2.setIsReal(mData.getData().getUserInfo().getIsReal());
//                                userInfo2.setIsPhone(mData.getData().getUserInfo().getIsPhone());
//                                XYApplication.getDaoInstant().getRyUserInfoDao().insertOrReplace(mData.getData().getUserInfo());
                                mView.showLoginCallBack(mData.getData());
                            }
                        });
                    }
                }

            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
//                mView.showLogin(null);
            }
        });
    }


    public void getAccordByNum(int type) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("type", type);
        HttpUtils.get(Common.APP_GET_ACCORD_BY_TYPE, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<WebBeanInfo> mData = FromJsonUtils.fromJson(text, WebBeanInfo.class);

                if (200 == mData.getCode()) {
                    if (null != mView) {
                        ThreadUtils.runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showWebInfo(mData.getData());
                            }
                        });
                    }
                }

            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
//                mView.showLogin(null);
            }
        });
    }

    /**
     * 注册
     *
     * @param phoneNumber
     * @param pwd
     * @param vCode
     * @param invitationQr
     */

    public void register(String phoneNumber, String pwd, String vCode, String invitationQr) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("phone", phoneNumber);
        params.put("password", pwd);
        params.put("verifyCode", vCode);
        params.put("invitationQr", invitationQr);
        params.put("avatar", "https://img1.baidu.com/it/u=3262732418,4135260730&fm=253&fmt=auto&app=138&f=JPEG?w=339&h=500");
        params.put("nickname", "测试001");
        params.put("sex", "1");
        HttpUtils.getResult(Common.APP_REGISTER, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果--->", "==========" + text);
//                BaseBean baseBean = GsonUtils.JsonToBean(text, BaseBean.class);
//                if (null != mView) {
//                    runOnUiThread(new Runnable() {
//                        @Override
//                        public void run() {
//                            mView.showRegister(baseBean);
//                        }
//                    });
//                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
            }
        });
    }


    public void getVcode(String phoneNumber) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("mobile", phoneNumber);
        HttpUtils.get(Common.APP_GET_VCODE, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                BaseBean baseBean = GsonUtils.JsonToBean(text, BaseBean.class);

                if (null != baseBean) {
                    Toaster.show(baseBean.getMessage());
                    if (200 == baseBean.getCode()) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                countDown();
                            }
                        });
                    }
                }

            }

            //
            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
            }
        });
    }

    public void getRongCloudToken() {
        HttpUtils.get(Common.APP_GET_RONG_CLOUD_TOKEN, new HashMap<>(), new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<RYTokenInfo> mData = FromJsonUtils.fromJson(text, RYTokenInfo.class);
                if (null != mView) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            if (200 != mData.getCode()) {
                                Toaster.show(mData.getMessage());
                                mView.showRyToken(null);
                            } else {
                                XYSPUtils.put(Constant.KEY_USER_RONGYUN_TOKEN, mData.getData().getToken());
                                connectIM(mData.getData().getToken());
                            }
                        }
                    });
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
//                mView.showLogin(null);
            }
        });
    }

    public void weiXinLogin(String code) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("code", code);
        HttpUtils.get(Common.APP_WEIXIN_LOGIN, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<WXLoginInfo> mData = FromJsonUtils.fromJson(text, WXLoginInfo.class);

                if (200 != mData.getCode()) {
                    Toaster.show(mData.getMessage());
                    if (null != mView) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showWeiXinLogin(null);
                            }
                        });
                    }
                } else {
                    if (0 == mData.getData().getIsNewUser()) {
                        XYSPUtils.put(Common.KEY_APP_TOKEN, mData.getData().getToken());
                    }
                    if (null != mView) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
//                                XYSPUtils.put(Constant.KEY_USER_RONGYUN_TOKEN, mData.getData().getToken());
//                                getRongCloudToken();
                                mView.showWeiXinLogin(mData.getData());
                            }
                        });
                    }
                }
//                if (null != mView)
//                {
//                    runOnUiThread(new Runnable() {
//                        @Override
//                        public void run() {
////                            if (200 != mData.getCode()) {
////                                Toaster.show(mData.getMessage());
////                                mView.showRyToken(null);
////                            } else {
////                                mView.showRyToken(mData.getData());
////                            }
//                        }
//                    });
//                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
//                mView.showLogin(null);
            }
        });
    }

    public void connectIM(String token) {
        ArrayList<Class<? extends MessageContent>> myMessages = new ArrayList<>();
        myMessages.add(XYTextContent.class);
        myMessages.add(XYGiftContent.class);
        myMessages.add(XYMediaMessageContent.class);
        myMessages.add(XYCallVideoContent.class);
        myMessages.add(XYGoldCoinsTextContent.class);
        myMessages.add(XYCommentContent.class);
        RongIMClient.registerMessageType(myMessages);
//        String targetId = "1725849550466932737";//1729341395424034818（176） 1729776846723137538（133）
        int timeLimit = 0;
        LogUtil.e("响应结果", "==========登录融云###token--》" + token);
        RongIM.connect(token, timeLimit, new RongIMClient.ConnectCallback() {
            @Override
            public void onDatabaseOpened(RongIMClient.DatabaseOpenStatus code) {
                LogUtil.e("响应结果", "==========本地数据库状态###" + code);
                if (RongIMClient.DatabaseOpenStatus.DATABASE_OPEN_SUCCESS.equals(code)) {
                    //本地数据库打开，跳转到会话列表页面
                } else {
                    //数据库打开失败，可以弹出 toast 提示。
                }
            }

            @Override
            public void onSuccess(String userId) {
                //连接成功，如果 onDatabaseOpened() 时没有页面跳转，也可在此时进行跳转。
                InitOption initOption = new InitOption.Builder().build();
//                RongCoreClient.init(XYApplication.getAppApplicationContext(), XYApplication.APP_KEY, initOption);

                LogUtil.e("响应结果", "==========userId###" + userId);
//                RCCallPlusResultCode resultCode = RCCallPlusClient.getInstance().init(RCCallPlusConfig.Builder.create().build());
//                LogUtil.e("响应结果", "==========resultCode:::" + resultCode);
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        RCCallPlusResultCode resultCode = RCCallPlusClient.getInstance().init(RCCallPlusConfig.Builder.create().enableTinyStream(false).build());
                        LogUtil.e("进入融云初始化", "进入融云初始化");
                        LogUtil.e("响应结果", "==========resultCode###" + resultCode.getReason());
                    }
                });
                BgmPlayer bgmPlayer = BgmPlayer.getInstance(XYApplication.getAppApplicationContext());
                bgmPlayer.changeToSpeaker();
                Toaster.show("登录成功");
                mView.showLoginImCallback();
//                getVipInfo();
            }

            @Override
            public void onError(RongIMClient.ConnectionErrorCode errorCode) {
                LogUtil.e("响应结果", "==========errorCode###" + errorCode.getValue());
                if (errorCode.equals(RongIMClient.ConnectionErrorCode.RC_CONN_TOKEN_EXPIRE)) {
                    //从 APP 服务请求新 token，获取到新 token 后重新 connect()
                    Toaster.show("从 APP 服务请求新 token，获取到新 token 后重新 connect()");
                } else if (errorCode.equals(RongIMClient.ConnectionErrorCode.RC_CONNECT_TIMEOUT)) {
                    //连接超时，弹出提示，可以引导用户等待网络正常的时候再次点击进行连接
                    Toaster.show("连接超时");
                } else {
                    //其它业务错误码，请根据相应的错误码作出对应处理。
                }
            }

        });
    }

//    public void getVipInfo() {
//        HashMap<String, Object> params = new HashMap<>();
//        HttpUtils.get(Common.APP_GET_VIP_INFO, params, new TextCallBack() {
//            @Override
//            protected void onSuccess(String text) {
//                LogUtil.e("响应结果", "==========vipInfo--->" + text);
//                ResultData<VipInfoModel> data = FromJsonUtils.fromJson(text, VipInfoModel.class);
//                if (null != data.getData()) {
//                    XYApplication.vipTypeModelList.clear();
//                    XYApplication.vipTypeModelList.addAll(data.getData().getVipList());
//                    mView.loadVipData();
//                }
//            }
//
//            @Override
//            protected void onFailure(ResponseException e) {
//                LogUtil.e("响应结果", "==========" + e.toString());
//            }
//        });
//    }


    private void countDown() {
        countDownTimer = new CountDownTimer(60 * 1000, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                if (null != mView) {
                    mView.showCountDown(millisUntilFinished / 1000);
                }
            }

            @Override
            public void onFinish() {
                if (null != mView) {
                    mView.showCountDownFinish();
                }
            }
        };
        countDownTimer.start();
    }

    private void initDatas() {
    }

    @Override
    public void onCreate() {

    }

    @Override
    public void destroy() {
        if (null != countDownTimer)
            countDownTimer.cancel();
    }
}
