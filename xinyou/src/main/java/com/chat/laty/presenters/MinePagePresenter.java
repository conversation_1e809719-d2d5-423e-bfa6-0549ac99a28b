package com.chat.laty.presenters;

import static cn.xjc_soft.lib_utils.ThreadUtils.runOnUiThread;

import com.chat.laty.activity.WebViewActivity;
import com.chat.laty.entity.KefuUrlInfo;
import com.hjq.toast.Toaster;
import com.chat.laty.base.XYApplication;
import com.chat.laty.controllers.MinePageController;
import com.chat.laty.entity.BalanceInfo;
import com.chat.laty.entity.RYTokenInfo;
import com.chat.laty.entity.UserCenterInfo;
import com.chat.laty.entity.ViolationEntity;
import com.chat.laty.entity.WebBeanInfo;
import com.chat.laty.entity.WeiChatPayInfo;
import com.chat.laty.utils.FromJsonUtils;
import com.chat.laty.utils.LogUtil;
import com.chat.laty.utils.ResultData;
import com.chat.laty.utils.XYSPUtils;
import com.chat.laty.utils.net.Common;
import com.chat.laty.utils.net.GsonUtils;
import com.chat.laty.utils.net.HttpUtils;
import com.chat.laty.utils.net.TextCallBack;

import java.util.HashMap;

import cn.xjc_soft.lib_utils.ThreadUtils;
import cn.xjc_soft.lib_utils.ViewUtils;

/**
 * <AUTHOR>
 * @date 2023/11/28 16:46
 * @description:
 */
public class MinePagePresenter implements MinePageController.Presenter {

    private MinePageController.View mView;

    public MinePagePresenter(MinePageController.View mView) {
        this.mView = mView;
    }


    public void getPayParm() {
        HttpUtils.getResult(Common.APP_GET_GET_PAY_URL, new HashMap<>(), new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<RYTokenInfo> mData = FromJsonUtils.fromJson(text, RYTokenInfo.class);

//                LogUtil.e("响应结果码", "==========" + mData.getCode());
                if (200 != mData.getCode()) {
                    Toaster.show(mData.getMessage());
                    if (null != mView) {
                        ViewUtils.runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showPayParm(null);

                            }
                        });
                    }
                } else {
                    if (null != mView) {
                        ViewUtils.runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showPayParm(mData.getData());

                            }
                        });
                    }
                }

            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
//                mView.showLogin(null);
            }
        });
    }

    public void getWXPayParam() {
        HttpUtils.getResult(Common.APP_GET_GET_WX_PAY_URL, new HashMap<>(), new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<WeiChatPayInfo> mData = FromJsonUtils.fromJson(text, WeiChatPayInfo.class);
                if (200 != mData.getCode()) {
                    Toaster.show(mData.getMessage());
                    if (null != mView) {
                        ViewUtils.runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showAXPayParm(null);

                            }
                        });
                    }
                } else {
                    if (null != mView) {
                        ViewUtils.runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showAXPayParm(mData.getData());

                            }
                        });
                    }
                }

            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
//                mView.showLogin(null);
            }
        });
    }

    public void getUserCenterInfo() {
        HttpUtils.get(Common.APP_USER_CENTER_INFO, new HashMap<>(), new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========userInfo::" + text);
                ResultData<UserCenterInfo> mData = FromJsonUtils.fromJson(text, UserCenterInfo.class);
                if (200 == mData.getCode()) {
                    if (null != mView) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                LogUtil.e("响应结果", "==========" + mData.getData().toString());
                                mView.showUserCenterDetails(mData.getData());
                            }
                        });
                    }
                }

            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
//                mView.showLogin(null);
            }
        });
    }

    public void getViolationList() {
        HttpUtils.get(Common.APP_USER_VIOLATION_LIST, new HashMap<>(), new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========getViolationList::" + text);
//                ResultData<List<String>> mData = FromJsonUtils.fromJson(text, String.class);
                ViolationEntity base = GsonUtils.JsonToBean(text, ViolationEntity.class);
                if (200 == base.getCode()) {
                    if (null != mView) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showViolationList(base.getResult());
                            }
                        });
                    }
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
//                mView.showLogin(null);
            }
        });
    }


    public void getUserBalance() {
        HttpUtils.get(Common.APP_GET_USER_BALANCE, new HashMap<>(), new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<BalanceInfo> mData = FromJsonUtils.fromJson(text, BalanceInfo.class);
                if (200 == mData.getCode()) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            XYApplication.getDaoInstant().getBalanceInfoDao().deleteAll();
                            BalanceInfo info = mData.getData();
                            info.setUserId(XYSPUtils.getString(Common.KEY_APP_USER_RY_ID));
                            XYApplication.getDaoInstant().getBalanceInfoDao().insertOrReplace(info);
                            mView.showUserBalance(info);
                        }
                    });
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
            }
        });
    }

    public void getAccordByNum(int type) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("type", type);
        HttpUtils.get(Common.APP_GET_ACCORD_BY_TYPE, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<WebBeanInfo> mData = FromJsonUtils.fromJson(text, WebBeanInfo.class);

                if (200 == mData.getCode()) {
                    if (null != mView) {
                        ThreadUtils.runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showWebInfo(mData.getData());
                            }
                        });
                    }
                }

            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
//                mView.showLogin(null);
            }
        });
    }

    public void getKefuUrl(String userId, String nickName) {
        HttpUtils.get(Common.APP_GET_KEFU_URL, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<KefuUrlInfo> mData = FromJsonUtils.fromJson(text, KefuUrlInfo.class);

                if (200 == mData.getCode()) {
                    if (null != mView) {
                        ThreadUtils.runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                WebViewActivity.startActivity(mView.context(), "联系客服", mData.getData().getKefuUrl() + "&userId=" + userId + "&userName=" + nickName);
                            }
                        });
                    }
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
            }
        });

    }

    @Override
    public void onCreate() {

    }

    @Override
    public void destroy() {

    }
}
