package com.chat.laty.presenters;

import static cn.xjc_soft.lib_utils.ThreadUtils.runOnUiThread;

import com.hjq.toast.Toaster;
import com.chat.laty.controllers.videoTelecastController;
import com.chat.laty.entity.SwTokenInfo;
import com.chat.laty.utils.FromJsonUtils;
import com.chat.laty.utils.LogUtil;
import com.chat.laty.utils.ResultData;
import com.chat.laty.utils.net.Common;
import com.chat.laty.utils.net.HttpUtils;
import com.chat.laty.utils.net.TextCallBack;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @date 2023/11/28 16:46
 * @description:
 */
public class VideoTelecastPresenter implements videoTelecastController.Presenter {

    private videoTelecastController.View mView;

    public VideoTelecastPresenter(videoTelecastController.View mView) {
        this.mView = mView;
    }

    public void getSwToken() {
        HttpUtils.get(Common.APP_GET_SW_TOKEN, new HashMap<>(), new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<SwTokenInfo> mData = FromJsonUtils.fromJson(text, SwTokenInfo.class);
                LogUtil.e("响应结果码", "==========" + mData.getCode());
                if (null != mView) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            if (200 != mData.getCode()) {
                                Toaster.show(mData.getMessage());
                                mView.showSwToken(null);
                            } else {
                                mView.showSwToken(mData.getData());
                            }
                        }
                    });
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
//                mView.showLogin(null);
            }
        });
    }

    public void getSystemConfigMap() {
        HttpUtils.get(Common.APP_GET_AGORA_CONFIG_MAP, new HashMap<>(), new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<SwTokenInfo> mData = FromJsonUtils.fromJson(text, SwTokenInfo.class);
                LogUtil.e("响应结果码", "==========" + mData.getCode());
                if (null != mView) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            if (200 != mData.getCode()) {
                                Toaster.show(mData.getMessage());
                                mView.showConfigMap(null);
                            } else {
                                mView.showConfigMap(mData.getData());
                            }
                        }
                    });
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
//                mView.showLogin(null);
            }
        });
    }

    @Override
    public void onCreate() {

    }

    @Override
    public void destroy() {

    }
}
