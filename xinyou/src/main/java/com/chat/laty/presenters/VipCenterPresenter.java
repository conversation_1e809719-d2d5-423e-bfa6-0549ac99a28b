package com.chat.laty.presenters;

import android.text.TextUtils;

import com.hjq.toast.Toaster;
import com.chat.laty.controllers.VipCenterController;
import com.chat.laty.entity.VipInfoModel;
import com.chat.laty.entity.VipDetailModel;
import com.chat.laty.entity.WeiChatPayInfo;
import com.chat.laty.utils.FromJsonUtils;
import com.chat.laty.utils.LogUtil;
import com.chat.laty.utils.ResultData;
import com.chat.laty.utils.net.Common;
import com.chat.laty.utils.net.HttpUtils;
import com.chat.laty.utils.net.TextCallBack;

import java.util.HashMap;
import java.util.List;

import cn.xjc_soft.lib_utils.ThreadUtils;

/**
 * <AUTHOR>
 * @date 2023/11/28 16:46
 * @description:
 */
public class VipCenterPresenter implements VipCenterController.Presenter {

    private final VipCenterController.IInfoView infoIView;

    private final VipCenterController.IDetailView recordsView;

    public VipCenterPresenter(VipCenterController.IInfoView infoIView, VipCenterController.IDetailView recordsView) {
        this.infoIView = infoIView;
        this.recordsView = recordsView;
    }

    public void getVipInfo() {
        HashMap<String, Object> params = new HashMap<>();
        HttpUtils.get(Common.APP_GET_VIP_INFO, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========vipInfo--->" + text);
                ResultData<VipInfoModel> data = FromJsonUtils.fromJson(text, VipInfoModel.class);
                if (null != infoIView && null != data.getData()) {
                    ThreadUtils.runOnUiThread(() -> infoIView.getInfoSucceed(data.getData()));
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
                if (null != infoIView) {
                    ThreadUtils.runOnUiThread(() -> {
                        String msg = "获取Vip充值信息失败～";
                        if (!TextUtils.isEmpty(e.getMsg())) {
                            msg = e.getMsg();
                        }
                        Toaster.show(msg);
                        infoIView.getInfoFailed();
                    });
                }
            }
        });
    }

    public void getVipRecords() {
        HashMap<String, Object> params = new HashMap<>();
        HttpUtils.get(Common.APP_GET_VIP_RECORDS, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);

                ResultData<List<VipDetailModel>> data = FromJsonUtils.fromJson(text, VipDetailModel.class);

                if (null != recordsView) {
                    ThreadUtils.runOnUiThread(() -> recordsView.getDetailSucceed(data.getData()));
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
                if (null != recordsView) {
                    ThreadUtils.runOnUiThread(() -> {
                        String msg = "获取开通记录失败～";
                        if (!TextUtils.isEmpty(e.getMsg())) {
                            msg = e.getMsg();
                        }
                        Toaster.show(msg);
                        recordsView.getDetailFailed();
                    });
                }
            }
        });
    }

    public void buyVip(String payCode, String vipId) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("payCode", payCode);
        params.put("vipId", vipId);
        HttpUtils.getResult(Common.APP_POST_BUY_VIP, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);

                ResultData<WeiChatPayInfo> mData = FromJsonUtils.fromJson(text, WeiChatPayInfo.class);

                if (null != infoIView) {
                    ThreadUtils.runOnUiThread(() -> infoIView.buySucceed(mData.getData()));
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
                if (null != recordsView) {
                    ThreadUtils.runOnUiThread(() -> {
                        String msg = "获取开通记录失败～";
                        if (!TextUtils.isEmpty(e.getMsg())) {
                            msg = e.getMsg();
                        }
                        Toaster.show(msg);
                        recordsView.getDetailFailed();
                    });
                }
            }
        });
    }

    @Override
    public void onCreate() {

    }

    @Override
    public void destroy() {

    }
}
