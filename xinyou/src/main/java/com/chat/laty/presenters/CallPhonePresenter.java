package com.chat.laty.presenters;

import static cn.xjc_soft.lib_utils.ThreadUtils.runOnUiThread;

import android.os.CountDownTimer;

import com.chat.laty.base.XYApplication;
import com.chat.laty.controllers.CallPhoneController;
import com.chat.laty.entity.BalanceInfo;
import com.chat.laty.entity.BaseBean;
import com.chat.laty.entity.GiftInfo;
import com.chat.laty.entity.RyUserInfo;
import com.chat.laty.entity.VideoTipsInfo;
import com.chat.laty.utils.FromJsonUtils;
import com.chat.laty.utils.LogUtil;
import com.chat.laty.utils.ResultData;
import com.chat.laty.utils.XYSPUtils;
import com.chat.laty.utils.net.Common;
import com.chat.laty.utils.net.GsonUtils;
import com.chat.laty.utils.net.HttpUtils;
import com.chat.laty.utils.net.TextCallBack;

import java.util.HashMap;
import java.util.List;

import cn.xjc_soft.lib_utils.ViewUtils;

/**
 * <AUTHOR>
 * @date 2023/11/28 16:46
 * @description:
 */
public class CallPhonePresenter implements CallPhoneController.Presenter {

    CountDownTimer countDownTimer;
    CountDownTimer balanceTimer;

    private CallPhoneController.View mView;


    public void countDown() {
        LogUtil.e("响应结果", "==========countDown开始");
        if (null == countDownTimer)
            countDownTimer = new CountDownTimer(20 * 1000, 1000) {
                @Override
                public void onTick(long millisUntilFinished) {
                    if (null != mView) {
                        LogUtil.e("响应结果", "==========" + millisUntilFinished);
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showCountDown(millisUntilFinished / 1000);
                            }
                        });

                    }
                }

                @Override
                public void onFinish() {
                    if (null != mView) {
                        LogUtil.e("响应结果", "==========");
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showCountDownFinish();
                            }
                        });
                    }
                }
            };
        countDownTimer.start();
    }

    public void balanceCountDown(long remainTime) {
        if (balanceTimer == null) {
            balanceTimer = new CountDownTimer(remainTime, 1000) {
                @Override
                public void onTick(long millisUntilFinished) {
                    if (null != mView) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showBalanceCountDown(millisUntilFinished / 1000);
                            }
                        });
                    }
                }

                @Override
                public void onFinish() {
                    if (null != mView) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showBalanceCountDownFinish();
                            }
                        });

                    }
                }
            };
            balanceTimer.start();
        }
    }


    public void getUserBalance() {
        HttpUtils.get(Common.APP_GET_USER_BALANCE, new HashMap<>(), new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<BalanceInfo> mData = FromJsonUtils.fromJson(text, BalanceInfo.class);
                if (200 == mData.getCode()) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            XYApplication.getDaoInstant().getBalanceInfoDao().deleteAll();
                            BalanceInfo info = mData.getData();
                            info.setUserId(XYSPUtils.getString(Common.KEY_APP_USER_RY_ID));
                            XYApplication.getDaoInstant().getBalanceInfoDao().insertOrReplace(info);
                            mView.showUserBalance(null == mData.getData() ? null : mData.getData());
                        }
                    });
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
            }
        });
    }

    public void submitRoom(String userId,String toUserId,String roomId) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("userId", userId);
        params.put("toUserId", toUserId);
        params.put("roomId", roomId);
        HttpUtils.getResult(Common.APP_GET_CHAT_SUBMIT_ROOMINFO, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========userResult-->" + text);
//                ResultData<RyUserInfo> mData = FromJsonUtils.fromJson(text, RyUserInfo.class);
//                if (null != mView) {
//                    ViewUtils.runOnUiThread(new Runnable() {
//                        @Override
//                        public void run() {
//                            mView.showUserInfo(mData.getData());
//                        }
//                    });
//                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
            }
        });
    }

    public void setRedisUserKey() {
        HashMap<String, Object> params = new HashMap<>();
        params.put("userId", XYSPUtils.getString(Common.KEY_APP_USER_RY_ID));
        HttpUtils.get(Common.APP_VIDEO_SET_REDIS_USER_KEY, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========RedisUserResult-->" + text);
                BaseBean callbackBean = GsonUtils.JsonToBean(text, BaseBean.class);
                if (null != mView) {
                    ViewUtils.runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mView.showRedisUserKey(callbackBean);
                        }
                    });
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
            }
        });
    }

    public void getVideoMsg() {
        HttpUtils.get(Common.APP_VIDEO_MSG_TIPS, new HashMap<>(), new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<VideoTipsInfo> mData = FromJsonUtils.fromJson(text, VideoTipsInfo.class);
                if (200 == mData.getCode()) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mView.showVideoTips(null == mData.getData() ? null : mData.getData());
                        }
                    });
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
            }
        });
    }


    public void getGift() {
        HttpUtils.get(Common.APP_GET_GIFT_LIST, new HashMap<>(), new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========userResult-->" + text);
                ResultData<List<GiftInfo>> mData = FromJsonUtils.fromJson(text, GiftInfo.class);
                if (null != mData.getData()) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mView.showGiftResult(mData.getData());
                        }
                    });

                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
            }
        });
    }

    public void getUserInfo(String userId) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("userId", userId);
        HttpUtils.get(Common.APP_GET_CHAT_RY_USERINFO, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========userResult-->" + text);
                ResultData<RyUserInfo> mData = FromJsonUtils.fromJson(text, RyUserInfo.class);
                if (null != mView) {
                    ViewUtils.runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mView.showUserInfo(mData.getData());
                        }
                    });
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
            }
        });
    }


    public CallPhonePresenter(CallPhoneController.View mView) {
        this.mView = mView;
    }

    @Override
    public void onCreate() {
    }

    @Override
    public void destroy() {
        if (null != countDownTimer) {
            countDownTimer.cancel();
            countDownTimer = null;
        }

        if (null != balanceTimer) {
            balanceTimer.cancel();
            balanceTimer = null;
        }

    }
}
