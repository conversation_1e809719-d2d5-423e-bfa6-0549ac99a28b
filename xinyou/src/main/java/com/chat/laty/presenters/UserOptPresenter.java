package com.chat.laty.presenters;

import static cn.xjc_soft.lib_utils.ThreadUtils.runOnUiThread;

import android.content.Context;
import android.text.TextUtils;

import com.alibaba.fastjson.JSON;
import com.hjq.toast.Toaster;
import com.luck.picture.lib.entity.LocalMedia;
import com.chat.laty.controllers.UserOptController;
import com.chat.laty.entity.AddressInfo;
import com.chat.laty.entity.BaseBean;
import com.chat.laty.entity.LabelInfo;
import com.chat.laty.entity.PickerViewBean;
import com.chat.laty.entity.UploadFileInfo;
import com.chat.laty.entity.UploadImgInfo;
import com.chat.laty.utils.FromJsonUtils;
import com.chat.laty.utils.JsonUtils;
import com.chat.laty.utils.LogUtil;
import com.chat.laty.utils.ResultData;
import com.chat.laty.utils.net.Common;
import com.chat.laty.utils.net.GsonUtils;
import com.chat.laty.utils.net.HttpUtils;
import com.chat.laty.utils.net.TextCallBack;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2023/11/28 16:46
 * @description:
 */
public class UserOptPresenter implements UserOptController.Presenter {

    private UserOptController.View mView;

    public UserOptPresenter(UserOptController.View mView) {
        this.mView = mView;
    }

    public void uploadFiles(List<LocalMedia> files, String bizPath) {
        HttpUtils.upLoadFiles(Common.APP_UPLOAD_FILE, "file", files, bizPath, new HttpUtils.OnResultCallBack() {
            @Override
            public void OnResultCallBack(String s) {
                LogUtil.e("响应结果", "==========" + s);
//                ResultData<List<String>> mData = FromJsonUtils.fromJson(s, String.class);
//                UploadFileInfo uploadBean = GsonUtils.JsonToBean(s, UploadFileInfo.class);
                ResultData<List<UploadImgInfo>> mData = FromJsonUtils.fromJson(s, UploadImgInfo.class);
                if (200 != mData.getCode()) {
                    Toaster.show(mData.getMessage());
                    if (null != mView) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showUploadResult(null);
                            }
                        });
                    }
                } else {
                    if (null != mView) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showUploadResult(mData.getData());
                            }
                        });
                    }
                }
            }

            @Override
            public void OnFailCallBack(String s) {
                LogUtil.e("响应结果", "==========出错了-->" + s);
            }
        });
    }

    public void uploadFiles(List<LocalMedia> files) {
        HttpUtils.upLoadFiles(Common.APP_UPLOAD_FILE, "file", files, "userHead", new HttpUtils.OnResultCallBack() {
            @Override
            public void OnResultCallBack(String s) {
                LogUtil.e("响应结果", "==========" + s);
//                ResultData<List<String>> mData = FromJsonUtils.fromJson(s, String.class);
                UploadFileInfo uploadBean = GsonUtils.JsonToBean(s, UploadFileInfo.class);
//                ResultData<List<String>> mData = FromJsonUtils.fromJson(s, String.class);
                if (200 != uploadBean.getCode()) {
                    Toaster.show(uploadBean.getMessage());
                    if (null != mView) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showUploadResults(null);
                            }
                        });
                    }
                } else {
                    if (null != mView) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showUploadResults(uploadBean.getResult());
                            }
                        });
                    }
                }
            }

            @Override
            public void OnFailCallBack(String s) {
                LogUtil.e("响应结果", "==========出错了-->" + s);
            }
        });
    }




    public void uploadHeadImgs(String file) {
        HttpUtils.upLoadFiles(Common.APP_UPLOAD_FILE, "file", file, "userHead", new HttpUtils.OnResultCallBack() {
            @Override
            public void OnResultCallBack(String s) {
                LogUtil.e("响应结果", "==========" + s);
//                ResultData<List<String>> mData = FromJsonUtils.fromJson(s, String.class);
                UploadFileInfo uploadBean = GsonUtils.JsonToBean(s, UploadFileInfo.class);
//                ResultData<List<String>> mData = FromJsonUtils.fromJson(s, String.class);
                if (200 != uploadBean.getCode()) {
                    Toaster.show(uploadBean.getMessage());
                    if (null != mView) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showUploadResults(null);
                            }
                        });
                    }
                } else {
                    if (null != mView) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showUploadResults(uploadBean.getResult());
                            }
                        });
                    }
                }
            }

            @Override
            public void OnFailCallBack(String s) {
                LogUtil.e("响应结果", "==========出错了-->" + s);
            }
        });
    }

    public void updateUserInfo(Map<String, Object> params) {
        HttpUtils.getResult(Common.APP_UPDATE_USER_INFO, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                BaseBean callbackBean = GsonUtils.JsonToBean(text, BaseBean.class);
                if (200 != callbackBean.getCode()) {
                    Toaster.show(callbackBean.getMessage());
                }
                if (null != mView) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mView.updateCallback(callbackBean);
                        }
                    });
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
//                mView.showLogin(null);
            }
        });
    }

    public void getLabelList(int type) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("type", type);
        HttpUtils.get(Common.APP_GET_LABEL_LIST, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<LabelInfo> mData = FromJsonUtils.fromJson(text, LabelInfo.class);
                LogUtil.e("响应结果码", "==========" + mData.getCode());
                if (200 == mData.getCode()) {
                    if (null != mView) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showLabels(type, mData.getData());
                            }
                        });
                    }
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
//                mView.showLogin(null);
            }
        });
    }

    public void getEditInfo() {
        HttpUtils.get(Common.APP_EDIT_USER_INFO, new HashMap<>(), new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<PickerViewBean> mData = FromJsonUtils.fromJson(text, PickerViewBean.class);
                if (200 == mData.getCode()) {
                    if (null != mView) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {

                                mView.showDetails(mData.getData());
                            }
                        });
                    }
                }

            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
//                mView.showLogin(null);
            }
        });
    }

    public void getAddress(Context context) {
        new Thread(new Runnable() {
            @Override
            public void run() {
                String json = JsonUtils.loadJSONFromAsset(context, "city.json");//AddressInfo

                if (!TextUtils.isEmpty(json)) {
                    List<AddressInfo> addressInfos = JSON.parseArray(json, AddressInfo.class);
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mView.showAddress(addressInfos);
                        }
                    });
                }
            }
        }).start();
    }


    public void uploadAudio(String filePath, String bizPath) {
        HttpUtils.upLoadFiles(Common.APP_UPLOAD_FILE, "file", filePath, bizPath, new HttpUtils.OnResultCallBack() {
            @Override
            public void OnResultCallBack(String s) {
                LogUtil.e("响应结果", "==========" + s);
//                ResultData<List<String>> mData = FromJsonUtils.fromJson(s, String.class);
                UploadFileInfo uploadBean = GsonUtils.JsonToBean(s, UploadFileInfo.class);
                LogUtil.e("响应结果", "==========" + uploadBean.getResult().size());
                if (200 != uploadBean.getCode()) {
                    Toaster.show(uploadBean.getMessage());
                } else {
                    if (null != mView) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showUploadAudioResult(uploadBean.getResult());
                            }
                        });
                    }
                }
            }

            @Override
            public void OnFailCallBack(String s) {
                LogUtil.e("响应结果", "==========错误：：" + s);
            }
        });
    }

    @Override
    public void onCreate() {

    }

    @Override
    public void destroy() {

    }
}
