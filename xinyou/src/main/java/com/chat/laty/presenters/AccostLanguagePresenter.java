package com.chat.laty.presenters;

import android.os.CountDownTimer;
import android.text.TextUtils;

import com.hjq.toast.Toaster;
import com.luck.picture.lib.entity.LocalMedia;
import com.chat.laty.controllers.AccostLanguageController;
import com.chat.laty.entity.AccostLanguageInfo;
import com.chat.laty.entity.AccostManLanguageInfo;
import com.chat.laty.entity.BaseBean;
import com.chat.laty.entity.UploadFileInfo;
import com.chat.laty.entity.UploadImgInfo;
import com.chat.laty.utils.FromJsonUtils;
import com.chat.laty.utils.LogUtil;
import com.chat.laty.utils.ResultData;
import com.chat.laty.utils.net.Common;
import com.chat.laty.utils.net.GsonUtils;
import com.chat.laty.utils.net.HttpUtils;
import com.chat.laty.utils.net.TextCallBack;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static cn.xjc_soft.lib_utils.ThreadUtils.runOnUiThread;

/**
 * <AUTHOR>
 * @date 2023/11/28 16:46
 * @description:
 */
public class AccostLanguagePresenter implements AccostLanguageController.Presenter {

    CountDownTimer countDownTimer;

    private AccostLanguageController.View mView;

    public AccostLanguagePresenter(AccostLanguageController.View mView) {
        this.mView = mView;
    }

    public void getAccostWomanList() {
        HttpUtils.get(Common.APP_GET_ACCOST_WOMAN_LIST, new HashMap<>(), new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<List<AccostLanguageInfo>> mData = FromJsonUtils.fromJson(text, AccostLanguageInfo.class);

                if (200 == mData.getCode()) {
                    if (null != mView) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showAccostWomanList(mData.getData());
                            }
                        });
                    }
                }

            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
//                mView.showLogin(null);
            }
        });
    }

    public void getAccostManList() {
        HttpUtils.get(Common.APP_GET_ACCOST_MAN_LIST, new HashMap<>(), new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<AccostManLanguageInfo> mData = FromJsonUtils.fromJson(text, AccostManLanguageInfo.class);
                if (200 == mData.getCode()) {
                    if (null != mView) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showAccostManList(mData.getData());
                            }
                        });
                    }
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
//                mView.showLogin(null);
            }
        });
    }

    public void deleteAccost(String accostId) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("accostId", accostId);
        HttpUtils.get(Common.APP_DELETE_ACCOST_WOMAN_BYID, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                BaseBean callbackBean = GsonUtils.JsonToBean(text, BaseBean.class);
                if (200 != callbackBean.getCode()) {
                    Toaster.show(callbackBean.getMessage());
                }
                if (null != mView) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mView.showDeleteCallback(callbackBean.getCode());
                        }
                    });
                }

            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
//                mView.showLogin(null);
            }
        });
    }

    public void setDefaultAccost(String accostId) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("accostId", accostId);
        HttpUtils.get(Common.APP_DEFAULT_ACCOST_WOMAN_BYID, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                BaseBean callbackBean = GsonUtils.JsonToBean(text, BaseBean.class);
                if (200 != callbackBean.getCode()) {
                    Toaster.show(callbackBean.getMessage());
                }
                if (null != mView) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mView.showDefaultCallback(callbackBean.getCode());
                        }
                    });
                }

            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
//                mView.showLogin(null);
            }
        });
    }

    public void updateAccostWoman(AccostLanguageInfo info, String name, String content, UploadImgInfo imgInfo, String audioPath, int travel) {
        HashMap<String, Object> params = new HashMap<>();
        if (null != info)
            params.put("id", info.getId());
        if (null != imgInfo) {
            params.put("tempImg", imgInfo.getImgUrl());
            params.put("tempImgThum", imgInfo.getTumhImgUrl());
        }

        params.put("tempName", name);
        if (!TextUtils.isEmpty(content))
            params.put("tempText", content);
        if (!TextUtils.isEmpty(audioPath)) {
            params.put("voiceUrl", audioPath);
            params.put("voiceTimeLength", travel);
        }

        HttpUtils.getResult(Common.APP_ADD_OR_UPDATE_ACCOST_WOMAN, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
//                ResultData<RYTokenInfo> mData = FromJsonUtils.fromJson(text, RYTokenInfo.class);
                BaseBean callbackBean = GsonUtils.JsonToBean(text, BaseBean.class);
                if (200 != callbackBean.getCode()) {
                    Toaster.show(callbackBean.getMessage());
                }
                if (null != mView) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mView.showupdateCallback(callbackBean.getCode());
                        }
                    });
                }

            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
//                mView.showLogin(null);
            }
        });
    }


    public void uploadFiles(List<LocalMedia> files, String bizPath) {
        HttpUtils.upLoadFiles(Common.APP_UPLOAD_IMG, "file", files, bizPath, new HttpUtils.OnResultCallBack() {
            @Override
            public void OnResultCallBack(String s) {
                LogUtil.e("响应结果", "==========" + s);
                ResultData<List<UploadImgInfo>> mData = FromJsonUtils.fromJson(s, UploadImgInfo.class);
//                LogUtil.e("响应结果", "==========" + uploadBean.getResult().size());
                if (200 == mData.getCode()) {
                    if (null != mView) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showUploadImgResult(mData.getData());
                            }
                        });
                    }
                }
            }

            @Override
            public void OnFailCallBack(String s) {
            }
        });
    }

    public void uploadAudio(String filePath, String bizPath) {
        HttpUtils.upLoadFiles(Common.APP_UPLOAD_FILE, "file", filePath, bizPath, new HttpUtils.OnResultCallBack() {
            @Override
            public void OnResultCallBack(String s) {
                LogUtil.e("响应结果", "==========" + s);
//                ResultData<List<String>> mData = FromJsonUtils.fromJson(s, String.class);
                UploadFileInfo uploadBean = GsonUtils.JsonToBean(s, UploadFileInfo.class);
                LogUtil.e("响应结果", "==========" + uploadBean.getResult().size());
                if (200 != uploadBean.getCode()) {
                    Toaster.show(uploadBean.getMessage());
                    if (null != mView) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showUploadAudioResult(null);
                            }
                        });
                    }
                } else {
                    if (null != mView) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showUploadAudioResult(uploadBean.getResult());
                            }
                        });
                    }
                }
            }

            @Override
            public void OnFailCallBack(String s) {
                LogUtil.e("响应结果", "==========错误：：" + s);
            }
        });
    }

    public void updateSettings(Map<String, Object> params) {
        HttpUtils.getResult(Common.APP_UPDATE_SETTINGS, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);

                BaseBean base = GsonUtils.JsonToBean(text, BaseBean.class);
//                ResultData<RYTokenInfo> mData = FromJsonUtils.fromJson(text, RYTokenInfo.class);
                if (null != mView) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mView.showUpdateCallback(base);
                        }
                    });
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
//                mView.showLogin(null);
            }
        });
    }


    public void startRecord() {
        countDown();
    }

    public void stopRecord() {
        if (null != countDownTimer)
            countDownTimer.cancel();
    }

    private void countDown() {
        countDownTimer = new CountDownTimer(8 * 1000, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                if (null != mView) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mView.showCountDownTravel((int) (8 - (millisUntilFinished / 1000)));
                        }
                    });
                }
            }

            @Override
            public void onFinish() {
                if (null != mView) {
                    mView.showCountDownFinish();
                }
            }
        };
        countDownTimer.start();
    }

    @Override
    public void onCreate() {

    }

    @Override
    public void destroy() {
        if (null != countDownTimer)
            countDownTimer.cancel();
    }
}
