package com.chat.laty.presenters;

import android.text.TextUtils;

import com.hjq.toast.Toaster;
import com.chat.laty.controllers.MyGroupController;
import com.chat.laty.entity.BaseBean;
import com.chat.laty.entity.MyGroupInfoModel;
import com.chat.laty.entity.MyGroupListInfoModel;
import com.chat.laty.utils.FromJsonUtils;
import com.chat.laty.utils.LogUtil;
import com.chat.laty.utils.ResultData;
import com.chat.laty.utils.net.Common;
import com.chat.laty.utils.net.GsonUtils;
import com.chat.laty.utils.net.HttpUtils;
import com.chat.laty.utils.net.TextCallBack;

import java.util.HashMap;
import java.util.List;

import cn.xjc_soft.lib_utils.ThreadUtils;

/**
 * <AUTHOR>
 * @date 2023/11/28 16:46
 * @description:
 */
public class MyGroupPresenter implements MyGroupController.Presenter {

    int type = 0;
    private final MyGroupController.IInfoView infoIView;
    private final MyGroupController.IGroupListView listView;
    private MyGroupController.ISetLevelView setLevelView;


    public void setType(int type) {
        this.type = type;
    }

    public MyGroupPresenter(MyGroupController.IInfoView infoIView, MyGroupController.IGroupListView listView) {
        this.infoIView = infoIView;
        this.listView = listView;
    }

    public void setSetLevelView(MyGroupController.ISetLevelView setLevelView) {
        this.setLevelView = setLevelView;
    }


    public void getMyGroupInfo(String date) {
        HashMap<String, Object> params = new HashMap<>();
        if (date != null) {
            params.put("date", date);
        }
        HttpUtils.get(Common.APP_GET_GROUP_INFO, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<MyGroupInfoModel> data = FromJsonUtils.fromJson(text, MyGroupInfoModel.class);

                if (null != infoIView) {
                    ThreadUtils.runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            infoIView.getInfoSucceed(data.getData(), date != null, date);
                        }
                    });
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());

                ThreadUtils.runOnUiThread(() -> {
                    String msg = "获取商城信息失败～";
                    if (!TextUtils.isEmpty(e.getMsg())) {
                        msg = e.getMsg();
                    }
                    Toaster.show(msg);
                    if (null != infoIView) {
                        infoIView.getInfoFailed(date != null);
                    }
                });
            }
        });
    }

    public void search(String keyword, int type, String userId) {
        getMyGroupList(keyword, type, userId, 1, 20, true, false, false);
    }

    public void getMyGroupList(String keyword, int type, String userId, int pageNo, int pageSize, boolean load, boolean refresh, boolean loadmore) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("keyword", keyword);
        params.put("type", type);
        params.put("userId", userId);
        params.put("pageNo", pageNo);
        params.put("pageSize", pageSize);
        HttpUtils.getResult(Common.APP_GET_GROUP_LIST, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<List<MyGroupListInfoModel>> data = FromJsonUtils.fromJson(text, MyGroupListInfoModel.class);
                if (null != listView) {
                    ThreadUtils.runOnUiThread(() -> listView.getGroupListSucceed(data.getData(), load, refresh, loadmore));
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());

                ThreadUtils.runOnUiThread(() -> {
                    String msg = "获取团队信息～";
                    if (!TextUtils.isEmpty(e.getMsg())) {
                        msg = e.getMsg();
                    }
                    Toaster.show(msg);
                    if (null != listView) {
                        listView.getGroupListFailed(load, refresh, loadmore);
                    }
                });

            }
        });
    }

    public void getMyGroupList(String keyword, int type, String userId, String date, int pageNo, int pageSize, boolean load, boolean refresh, boolean loadmore) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("keyword", keyword);
        params.put("type", type);
        params.put("userId", userId);
        params.put("date", date);
        params.put("pageNo", pageNo);
        params.put("pageSize", pageSize);
        HttpUtils.getResult(Common.APP_GET_GROUP_LIST, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<List<MyGroupListInfoModel>> data = FromJsonUtils.fromJson(text, MyGroupListInfoModel.class);
                if (null != listView) {
                    ThreadUtils.runOnUiThread(() -> listView.getGroupListSucceed(data.getData(), load, refresh, loadmore));
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());

                ThreadUtils.runOnUiThread(() -> {
                    String msg = "获取团队信息～";
                    if (!TextUtils.isEmpty(e.getMsg())) {
                        msg = e.getMsg();
                    }
                    Toaster.show(msg);
                    if (null != listView) {
                        listView.getGroupListFailed(load, refresh, loadmore);
                    }
                });

            }
        });
    }

    public void setLevelInfo(String userId, String level, int index) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("dbnLevel", level);
        params.put("userId", userId);

        HttpUtils.get(Common.APP_GET_GROUP_SET_LEVEL, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                BaseBean callbackBean = GsonUtils.JsonToBean(text, BaseBean.class);
                if (200 != callbackBean.getCode()) {
                    Toaster.show(callbackBean.getMessage());
                    if (null != setLevelView) {
                        ThreadUtils.runOnUiThread(() -> setLevelView.setLevelFailed());
                    }
                } else {
                    if (null != setLevelView) {
                        int l = Integer.parseInt(level);
                        ThreadUtils.runOnUiThread(() -> setLevelView.setLevelSucceed(index, l));
                    }
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());

                if (null != setLevelView)
                    ThreadUtils.runOnUiThread(() -> setLevelView.setLevelFailed());
            }
        });
    }


    @Override
    public void onCreate() {

    }

    @Override
    public void destroy() {

    }
}
