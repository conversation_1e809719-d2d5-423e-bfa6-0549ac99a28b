package com.chat.laty.presenters;

import android.app.Activity;
import android.content.Context;
import android.graphics.Bitmap;

import com.alipay.sdk.app.PayTask;
import com.tencent.mm.opensdk.modelmsg.SendAuth;
import com.tencent.mm.opensdk.modelmsg.SendMessageToWX;
import com.tencent.mm.opensdk.modelmsg.WXImageObject;
import com.tencent.mm.opensdk.modelmsg.WXMediaMessage;
import com.tencent.mm.opensdk.modelpay.PayReq;
import com.tencent.mm.opensdk.openapi.IWXAPI;
import com.tencent.mm.opensdk.openapi.WXAPIFactory;
import com.chat.laty.entity.PayResultEntity;
import com.chat.laty.entity.WeiChatPayInfo;
import com.chat.laty.utils.LogUtil;
import com.chat.laty.utils.net.Common;

import org.greenrobot.eventbus.EventBus;

import java.io.ByteArrayOutputStream;
import java.util.Map;


public class PayManager {
    /**
     * 微信支付
     * VY66kRKiraW8oCSk
     *
     * @param context
     * @param weiChatPayInfo
     */
    public static void weiChatPay(Context context, WeiChatPayInfo weiChatPayInfo) {
        IWXAPI api = WXAPIFactory.createWXAPI(context, weiChatPayInfo.getAppid());
        boolean c = api.registerApp(weiChatPayInfo.getAppid());
        if (!c)
            api.registerApp(weiChatPayInfo.getAppid());
        PayReq request = new PayReq();
        request.appId = weiChatPayInfo.getAppid();
        request.nonceStr = weiChatPayInfo.getNonceStr();
        request.partnerId = weiChatPayInfo.getPartnerId();
        request.prepayId = weiChatPayInfo.getPrepayId();
//        request.packageValue = weiChatPayInfo.getPackageX();
        request.packageValue = "Sign=WXPay";
        request.timeStamp = weiChatPayInfo.getTimestamp();
        request.sign = weiChatPayInfo.getSign();
        if (request.checkArgs())
            api.sendReq(request);
    }

    public static void alipayPay(Context context, String info) {
        Runnable payRunnable = () -> {
            PayTask alipay = new PayTask((Activity) context);
            Map<String, String> value = alipay.payV2(info, true);
            LogUtil.e("支付结果", "==========  " + value.toString());
            int code = -1;
            if ("9000".equals(value.get("resultStatus"))) {
                code = 0;
            }
            PayResultEntity result = new PayResultEntity(code);
            EventBus.getDefault().post(result);
        };
        // 必须异步调用
        Thread payThread = new Thread(payRunnable);
        payThread.start();
    }

    public static void goToMiniApp(Context context, String path) {
//        IWXAPI api = WXAPIFactory.createWXAPI(context, Constant.APP_ID);
//        boolean c = api.registerApp(Constant.APP_ID);
//        if (!c)
//            api.registerApp(Constant.APP_ID);
//        WXLaunchMiniProgram.Req req = new WXLaunchMiniProgram.Req();
//        req.userName = "gh_d2af5ef623fb"; // 填小程序原始id
//        req.path = path;                  //拉起小程序页面的可带参路径，不填默认拉起小程序首页
////        req.path="page/index?key1=xxx&key2=yyy";
//        req.miniprogramType = WXLaunchMiniProgram.Req.MINIPTOGRAM_TYPE_RELEASE;// 可选打开 开发版，体验版和正式版
//        if (req.checkArgs())
//            api.sendReq(req);
    }

    public static void wxLogin(Context context) {
        IWXAPI api = WXAPIFactory.createWXAPI(context, Common.APP_ID);
        boolean c = api.registerApp(Common.APP_ID);
        if (!c)
            api.registerApp(Common.APP_ID);
        //先判断用户手机是否安装了微信客户端
        //向微信开放平台发起请求
        // 发送授权登录信息，来获取code
        SendAuth.Req req = new SendAuth.Req();
        // 应用的作用域，获取个人信息
        req.scope = "snsapi_userinfo";
        req.state = "app_wechat";
        api.sendReq(req);
    }


    /**
     *
     * @param context
     * @param bmp
     * @param type SendMessageToWX.Req.WXSceneSession ,SendMessageToWX.Req.WXSceneTimeline
     */
    public static void wxShare(Context context,Bitmap bmp ,int type) {
        IWXAPI api = WXAPIFactory.createWXAPI(context, Common.APP_ID);
        boolean c = api.registerApp(Common.APP_ID);
        if (!c)
            api.registerApp(Common.APP_ID);

//        Bitmap bmp = BitmapFactory.decodeResource(context.getResources(), R.drawable.send_img);

//初始化 WXImageObject 和 WXMediaMessage 对象
        WXImageObject imgObj = new WXImageObject(bmp);
        WXMediaMessage msg = new WXMediaMessage();
        msg.mediaObject = imgObj;

//设置缩略图
        Bitmap thumbBmp = Bitmap.createScaledBitmap(bmp, 50, 50, true);
        bmp.recycle();
        msg.thumbData = convertToBytes(thumbBmp);

//构造一个Req
        SendMessageToWX.Req req = new SendMessageToWX.Req();
        req.transaction = "xy_invite_wx_" + type;
        req.message = msg;
        req.scene = type;
        req.userOpenId = Common.APP_ID;
        api.sendReq(req);
    }


    public static byte[] convertToBytes(Bitmap bitmap) {
        ByteArrayOutputStream stream = new ByteArrayOutputStream();
        bitmap.compress(Bitmap.CompressFormat.PNG, 100, stream); // 这里选择了PNG格式进行压缩，也可以根据需要选择其他格式
        return stream.toByteArray();
    }
}
