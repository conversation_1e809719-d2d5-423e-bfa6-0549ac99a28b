package com.chat.laty.presenters;

import android.text.TextUtils;

import com.hjq.toast.Toaster;
import com.chat.laty.controllers.GoldCoinController;
import com.chat.laty.controllers.VipCenterController;
import com.chat.laty.entity.GoldCoinDetailModel;
import com.chat.laty.entity.GoldCoinInfoModel;
import com.chat.laty.entity.WeiChatPayInfo;
import com.chat.laty.utils.FromJsonUtils;
import com.chat.laty.utils.LogUtil;
import com.chat.laty.utils.ResultData;
import com.chat.laty.utils.net.Common;
import com.chat.laty.utils.net.HttpUtils;
import com.chat.laty.utils.net.TextCallBack;

import java.util.HashMap;
import java.util.List;

import cn.xjc_soft.lib_utils.ThreadUtils;

/**
 * <AUTHOR>
 * @date 2023/11/28 16:46
 * @description:
 */
public class GoldCoinPresenter implements VipCenterController.Presenter {

    int type = 0;
    private final GoldCoinController.IInfoView infoIView;

    private final GoldCoinController.IDetailView detailView;

    public void setType(int type) {
        this.type = type;
    }

    public GoldCoinPresenter(GoldCoinController.IInfoView infoIView, GoldCoinController.IDetailView detailView) {
        this.infoIView = infoIView;
        this.detailView = detailView;
    }

    public void getGoldCoinInfo() {
        HashMap<String, Object> params = new HashMap<>();
        HttpUtils.get(Common.APP_GET_GOLD_COIN_INFO, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<GoldCoinInfoModel> data = FromJsonUtils.fromJson(text, GoldCoinInfoModel.class);
                if (null != infoIView) {
                    ThreadUtils.runOnUiThread(() -> infoIView.getInfoSucceed(data.getData()));
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
                if (null != infoIView) {
                    ThreadUtils.runOnUiThread(() -> {
                        String msg = "获取金币信息失败～";
                        if (!TextUtils.isEmpty(e.getMsg())) {
                            msg = e.getMsg();
                        }
                        Toaster.show(msg);
                        infoIView.getInfoFailed();
                    });
                }
            }
        });
    }

    public void getGoldCoinDetail(int pageNo, int pageSize, boolean load, boolean refresh, boolean loadmore) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("pageNo", pageNo);
        params.put("pageSize", pageSize);
        params.put("type", type);
        HttpUtils.getResult(Common.APP_GET_GOLD_COIN_DETAIL, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);

                ResultData<List<GoldCoinDetailModel>> mData = FromJsonUtils.fromJson(text, GoldCoinDetailModel.class);

                if (null != detailView) {
                    ThreadUtils.runOnUiThread(() -> detailView.getDetailSucceed(mData.getData(), load, refresh, loadmore));
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
                if (null != detailView) {
                    ThreadUtils.runOnUiThread(() -> {
                        String msg = "获取金币详细失败～";
                        if (!TextUtils.isEmpty(e.getMsg())) {
                            msg = e.getMsg();
                        }
                        Toaster.show(msg);
                        detailView.getDetailFailed(load, refresh, loadmore);
                    });
                }
            }
        });
    }


    public void buyGoldCoin(String payCode, String goldId) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("payCode", payCode);
        params.put("goldId", goldId);
        HttpUtils.getResult(Common.APP_POST_BUY_GOLD_COIN, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);

                ResultData<WeiChatPayInfo> mData = FromJsonUtils.fromJson(text, WeiChatPayInfo.class);

                if (null != infoIView) {
                    ThreadUtils.runOnUiThread(() -> infoIView.buySucceed(mData.getData()));
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
                if (null != infoIView) {
                    ThreadUtils.runOnUiThread(() -> {
                        String msg = "获取开通记录失败～";
                        if (!TextUtils.isEmpty(e.getMsg())) {
                            msg = e.getMsg();
                        }
                        Toaster.show(msg);
                        infoIView.buyFailed();
                    });
                }
            }
        });
    }

    // oldcenter/getPayLink


    @Override
    public void onCreate() {

    }

    @Override
    public void destroy() {

    }
}
