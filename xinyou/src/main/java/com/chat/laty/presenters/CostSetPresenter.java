package com.chat.laty.presenters;

import static cn.xjc_soft.lib_utils.ThreadUtils.runOnUiThread;

import com.chat.laty.controllers.CostSetController;
import com.chat.laty.entity.BaseBean;
import com.chat.laty.entity.CostBean;
import com.chat.laty.entity.WebBeanInfo;
import com.chat.laty.utils.FromJsonUtils;
import com.chat.laty.utils.LogUtil;
import com.chat.laty.utils.ResultData;
import com.chat.laty.utils.net.Common;
import com.chat.laty.utils.net.GsonUtils;
import com.chat.laty.utils.net.HttpUtils;
import com.chat.laty.utils.net.TextCallBack;

import java.util.HashMap;
import java.util.Map;

import cn.xjc_soft.lib_utils.ThreadUtils;

/**
 * <AUTHOR>
 * @date 2023/11/28 16:46
 * @description:
 */
public class CostSetPresenter implements CostSetController.Presenter {

    private CostSetController.View mView;

    public CostSetPresenter(CostSetController.View mView) {
        this.mView = mView;
    }

    /**
     * 获取融云token
     */
    public void getUserFeeSettings() {
        HttpUtils.get(Common.APP_GET_CHAT_COST_SET, new HashMap<>(), new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<CostBean> mData = FromJsonUtils.fromJson(text, CostBean.class);
                if (200 == mData.getCode()) {
                    if (null != mView) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showCostCallback(mData.getData());
                            }
                        });
                    }
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
//                mView.showLogin(null);
            }
        });
    }


    public void updateSettings(Map<String, Object> params) {
        HttpUtils.getResult(Common.APP_UPDATE_SETTINGS, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                BaseBean baseBean = GsonUtils.JsonToBean(text, BaseBean.class);
                if (null != mView) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mView.showUpdateCallback(baseBean);
                        }
                    });
                }
            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
//                mView.showLogin(null);
            }
        });
    }

    public void getAccordByNum(int type) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("type", type);
        HttpUtils.get(Common.APP_GET_ACCORD_BY_TYPE, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                LogUtil.e("响应结果", "==========" + text);
                ResultData<WebBeanInfo> mData = FromJsonUtils.fromJson(text, WebBeanInfo.class);

                if (200 == mData.getCode()) {
                    if (null != mView) {
                        ThreadUtils.runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mView.showWebInfo(mData.getData());
                            }
                        });
                    }
                }

            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
//                mView.showLogin(null);
            }
        });
    }

    @Override
    public void onCreate() {

    }

    @Override
    public void destroy() {

    }
}
