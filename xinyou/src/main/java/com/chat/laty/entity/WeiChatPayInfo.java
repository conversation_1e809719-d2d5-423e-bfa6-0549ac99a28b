package com.chat.laty.entity;


import com.google.gson.annotations.SerializedName;


public class WeiChatPayInfo {

    @SerializedName("package")
    private String packageX;

    @SerializedName("appid")
    private String appid;

    @SerializedName("partnerId")
    private String partnerId;

    @SerializedName("prepayId")
    private String prepayId;

    @SerializedName("packageVal")
    private String packageVal;

    @SerializedName("nonceStr")
    private String nonceStr;

    @SerializedName("timestamp")
    private String timestamp;

    @SerializedName("sign")
    private String sign;

    @SerializedName("payUrl")
    private String payUrl;

    public String getPackageX() {
        return packageX;
    }

    public void setPackageX(String packageX) {
        this.packageX = packageX;
    }

    public String getPayUrl() {
        return payUrl;
    }

    public void setPayUrl(String payUrl) {
        this.payUrl = payUrl;
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(String partnerId) {
        this.partnerId = partnerId;
    }

    public String getPrepayId() {
        return prepayId;
    }

    public void setPrepayId(String prepayId) {
        this.prepayId = prepayId;
    }

    public String getPackageVal() {
        return packageVal;
    }

    public void setPackageVal(String packageVal) {
        this.packageVal = packageVal;
    }

    public String getNonceStr() {
        return nonceStr;
    }

    public void setNonceStr(String nonceStr) {
        this.nonceStr = nonceStr;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }
}
