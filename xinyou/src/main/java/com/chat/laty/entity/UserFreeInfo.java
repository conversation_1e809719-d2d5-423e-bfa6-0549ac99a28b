package com.chat.laty.entity;

import com.google.gson.annotations.SerializedName;

/**
 * <AUTHOR>
 * @date 2024/3/12 22:15
 * @description:
 */
public class UserFreeInfo {

    @SerializedName("textFee")
    private String textFee;
    @SerializedName("voiceFee")
    private String voiceFee;
    @SerializedName("videoFee")
    private String videoFee;

    public String getTextFee() {
        return textFee;
    }

    public void setTextFee(String textFee) {
        this.textFee = textFee;
    }

    public String getVoiceFee() {
        return voiceFee;
    }

    public void setVoiceFee(String voiceFee) {
        this.voiceFee = voiceFee;
    }

    public String getVideoFee() {
        return videoFee;
    }

    public void setVideoFee(String videoFee) {
        this.videoFee = videoFee;
    }
}
