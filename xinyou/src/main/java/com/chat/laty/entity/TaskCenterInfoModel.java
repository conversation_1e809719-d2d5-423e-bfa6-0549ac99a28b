package com.chat.laty.entity;

import java.util.List;
import java.util.Map;

public class TaskCenterInfoModel {

    private String integralBalance;
    private List<TaskInfoModel> everydayTask;
    private List<TaskInfoModel> newcomerRewards;
    private Map<String, Object> weeklySignVo;

    public String getIntegralBalance() {
        return integralBalance;
    }

    public void setIntegralBalance(String integralBalance) {
        this.integralBalance = integralBalance;
    }

    public List<TaskInfoModel> getEverydayTask() {
        return everydayTask;
    }

    public void setEverydayTask(List<TaskInfoModel> everydayTask) {
        this.everydayTask = everydayTask;
    }

    public List<TaskInfoModel> getNewcomerRewards() {
        return newcomerRewards;
    }

    public void setNewcomerRewards(List<TaskInfoModel> newcomerRewards) {
        this.newcomerRewards = newcomerRewards;
    }

    public Map<String, Object> getWeeklySignVo() {
        return weeklySignVo;
    }

    public void setWeeklySignVo(Map<String, Object> weeklySignVo) {
        this.weeklySignVo = weeklySignVo;
    }

    public static class TaskInfoModel {
        private String taskName;
        private String reachCondition;
        private int addIntegral;
        private String isComplete;

        public boolean selected;

        public String getTaskName() {
            return taskName;
        }

        public void setTaskName(String taskName) {
            this.taskName = taskName;
        }

        public String getReachCondition() {
            return reachCondition;
        }

        public void setReachCondition(String reachCondition) {
            this.reachCondition = reachCondition;
        }

        public int getAddIntegral() {
            return addIntegral;
        }

        public void setAddIntegral(int addIntegral) {
            this.addIntegral = addIntegral;
        }

        public String getIsComplete() {
            return isComplete;
        }

        public void setIsComplete(String isComplete) {
            this.isComplete = isComplete;
        }
    }
}
