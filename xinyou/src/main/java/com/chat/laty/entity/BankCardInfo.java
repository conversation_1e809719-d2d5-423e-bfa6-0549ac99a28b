package com.chat.laty.entity;

import com.google.gson.annotations.SerializedName;

/**
 * <AUTHOR>
 * @date 2024/1/16 22:19
 * @description:
 */
public class BankCardInfo {

    @SerializedName("id")
    private String id;
    @SerializedName("bankName")
    private String bankName;
    @SerializedName("bankNumber")
    private String bankNumber;
    @SerializedName("userName")
    private String userName;

    public boolean selected;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getBankNumber() {
        return bankNumber;
    }

    public void setBankNumber(String bankNumber) {
        this.bankNumber = bankNumber;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }
}
