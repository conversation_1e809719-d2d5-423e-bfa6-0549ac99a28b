package com.chat.laty.entity;

import com.google.gson.annotations.SerializedName;

/**
 * <AUTHOR>
 * @date 2023/12/25 23:15
 * @description:
 */
public class UploadImgInfo {

    @SerializedName("tumhImgUrl")
    private String tumhImgUrl;
    @SerializedName("imgUrl")
    private String imgUrl;

    private int imgAddress;

    public int getImgAddress() {
        return imgAddress;
    }

    public void setImgAddress(int imgAddress) {
        this.imgAddress = imgAddress;
    }

    public String getTumhImgUrl() {
        return tumhImgUrl;
    }

    public void setTumhImgUrl(String tumhImgUrl) {
        this.tumhImgUrl = tumhImgUrl;
    }

    public String getImgUrl() {
        return imgUrl;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }
}
