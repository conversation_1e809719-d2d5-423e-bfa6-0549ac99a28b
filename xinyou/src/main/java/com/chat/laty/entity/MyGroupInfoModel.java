package com.chat.laty.entity;

import android.os.Parcel;
import android.os.Parcelable;

import androidx.annotation.NonNull;

public class MyGroupInfoModel implements Parcelable {
    private String avatar;
    private String nickname;
    private String userId;
    private int dbnLevel;  // (0-普通用户 10-会员 20-超级会员 30-运营商 40-合伙人)
    private int totalDirectNum;
    private float totalRechargeNum;
    private float totalIndirectRechargeNum;
    private float totalSocializeNum;
    private float totalIndirectSocialNum;

    private float alltotalRechargeNum;
    private float alltotalIndirectRechargeNum;
    private float alltotalSocializeNum;
    private float alltotalIndirectSocialNum;

    private String dividedAmountNum;
    private int totalTeamNum;

    private String teamRechargeTotalAmount;
    private String theTotalAmountRechargedByTheTeamToday;

    public MyGroupInfoModel() {
    }

    protected MyGroupInfoModel(Parcel in) {
        avatar = in.readString();
        nickname = in.readString();
        userId = in.readString();
        dbnLevel = in.readInt();
        totalDirectNum = in.readInt();
        totalRechargeNum = in.readFloat();
        totalIndirectRechargeNum = in.readFloat();
        totalSocializeNum = in.readFloat();
        totalIndirectSocialNum = in.readFloat();

        alltotalRechargeNum = in.readFloat();
        alltotalIndirectRechargeNum = in.readFloat();
        alltotalSocializeNum = in.readFloat();
        alltotalIndirectSocialNum = in.readFloat();

        totalTeamNum = in.readInt();
        dividedAmountNum = in.readString();
        teamRechargeTotalAmount = in.readString();
        theTotalAmountRechargedByTheTeamToday = in.readString();
    }

    public static final Creator<MyGroupInfoModel> CREATOR = new Creator<MyGroupInfoModel>() {
        @Override
        public MyGroupInfoModel createFromParcel(Parcel in) {
            return new MyGroupInfoModel(in);
        }

        @Override
        public MyGroupInfoModel[] newArray(int size) {
            return new MyGroupInfoModel[size];
        }
    };

    public float getAlltotalRechargeNum() {
        return alltotalRechargeNum;
    }

    public void setAlltotalRechargeNum(float alltotalRechargeNum) {
        this.alltotalRechargeNum = alltotalRechargeNum;
    }

    public float getAlltotalIndirectRechargeNum() {
        return alltotalIndirectRechargeNum;
    }

    public void setAlltotalIndirectRechargeNum(float alltotalIndirectRechargeNum) {
        this.alltotalIndirectRechargeNum = alltotalIndirectRechargeNum;
    }

    public float getAlltotalSocializeNum() {
        return alltotalSocializeNum;
    }

    public void setAlltotalSocializeNum(float alltotalSocializeNum) {
        this.alltotalSocializeNum = alltotalSocializeNum;
    }

    public float getAlltotalIndirectSocialNum() {
        return alltotalIndirectSocialNum;
    }

    public void setAlltotalIndirectSocialNum(float alltotalIndirectSocialNum) {
        this.alltotalIndirectSocialNum = alltotalIndirectSocialNum;
    }

    public void setTheTotalAmountRechargedByTheTeamToday(String theTotalAmountRechargedByTheTeamToday) {
        this.theTotalAmountRechargedByTheTeamToday = theTotalAmountRechargedByTheTeamToday;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public int getDbnLevel() {
        return dbnLevel;
    }

    public void setDbnLevel(int dbnLevel) {
        this.dbnLevel = dbnLevel;
    }

    public int getTotalDirectNum() {
        return totalDirectNum;
    }

    public float getTotalIndirectRechargeNum() {
        return totalIndirectRechargeNum;
    }

    public void setTotalIndirectRechargeNum(float totalIndirectRechargeNum) {
        this.totalIndirectRechargeNum = totalIndirectRechargeNum;
    }

    public float getTotalIndirectSocialNum() {
        return totalIndirectSocialNum;
    }

    public void setTotalIndirectSocialNum(float totalIndirectSocialNum) {
        this.totalIndirectSocialNum = totalIndirectSocialNum;
    }

    public void setTotalDirectNum(int totalDirectNum) {
        this.totalDirectNum = totalDirectNum;
    }

    public float getTotalRechargeNum() {
        return totalRechargeNum;
    }

    public void setTotalRechargeNum(float totalRechargeNum) {
        this.totalRechargeNum = totalRechargeNum;
    }

    public float getTotalSocializeNum() {
        return totalSocializeNum;
    }

    public void setTotalSocializeNum(float totalSocializeNum) {
        this.totalSocializeNum = totalSocializeNum;
    }

    public int getTotalTeamNum() {
        return totalTeamNum;
    }

    public void setTotalTeamNum(int totalTeamNum) {
        this.totalTeamNum = totalTeamNum;
    }

    public String getDividedAmountNum() {
        return dividedAmountNum;
    }

    public void setDividedAmountNum(String dividedAmountNum) {
        this.dividedAmountNum = dividedAmountNum;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public String getTeamRechargeTotalAmount() {
        return teamRechargeTotalAmount;
    }

    public void setTeamRechargeTotalAmount(String teamRechargeTotalAmount) {
        teamRechargeTotalAmount = teamRechargeTotalAmount;
    }

    public String getTheTotalAmountRechargedByTheTeamToday() {
        return theTotalAmountRechargedByTheTeamToday;
    }

    public void settheTotalAmountRechargedByTheTeamToday(String theTotalAmountRechargedByTheTeamToday) {
        theTotalAmountRechargedByTheTeamToday = theTotalAmountRechargedByTheTeamToday;
    }

    @Override
    public void writeToParcel(@NonNull Parcel parcel, int i) {
        parcel.writeString(avatar);
        parcel.writeString(nickname);
        parcel.writeString(userId);
        parcel.writeInt(dbnLevel);
        parcel.writeInt(totalDirectNum);
        parcel.writeFloat(totalRechargeNum);
        parcel.writeFloat(totalIndirectRechargeNum);
        parcel.writeFloat(totalSocializeNum);
        parcel.writeFloat(totalIndirectSocialNum);

        parcel.writeFloat(alltotalRechargeNum);
        parcel.writeFloat(alltotalIndirectRechargeNum);
        parcel.writeFloat(alltotalSocializeNum);
        parcel.writeFloat(alltotalIndirectSocialNum);

        parcel.writeInt(totalTeamNum);
        parcel.writeString(dividedAmountNum);
        parcel.writeString(teamRechargeTotalAmount);
        parcel.writeString(theTotalAmountRechargedByTheTeamToday);

    }


}

