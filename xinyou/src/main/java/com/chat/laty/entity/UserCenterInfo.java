package com.chat.laty.entity;

import com.google.gson.annotations.SerializedName;

/**
 * <AUTHOR>
 * @date 2024/1/6 16:42
 * @description:
 */
public class UserCenterInfo {

    @SerializedName("avatar")
    private String avatar;
    @SerializedName("integralBalance")
    private String integralBalance;
    @SerializedName("diamondBalance")
    private String diamondBalance;
    @SerializedName("goldNum")
    private String goldNum;
    @SerializedName("isName")
    private String isName;
    @SerializedName("isPhone")
    private String isPhone;
    @SerializedName("isReal")
    private String isReal;
    @SerializedName("myLoveNum")
    private String myLoveNum;
    @SerializedName("newSeeMeNum")
    private String newSeeMeNum;
    @SerializedName("nickname")
    private String nickname;
    @SerializedName("sex")
    private String sex;
    @SerializedName("userId")
    private String userId;
    @SerializedName("vipLevel")
    private int vipLevel;
    @SerializedName("whoSeeMeNum")
    private String whoSeeMeNum;

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getGoldNum() {
        return goldNum;
    }

    public void setGoldNum(String goldNum) {
        this.goldNum = goldNum;
    }

    public String getIntegralBalance() {
        return integralBalance;
    }

    public void setIntegralBalance(String integralBalance) {
        this.integralBalance = integralBalance;
    }

    public String getDiamondBalance() {
        return diamondBalance;
    }

    public void setDiamondBalance(String diamondBalance) {
        this.diamondBalance = diamondBalance;
    }

    public String getIsName() {
        return isName;
    }

    public void setIsName(String isName) {
        this.isName = isName;
    }

    public String getIsPhone() {
        return isPhone;
    }

    public void setIsPhone(String isPhone) {
        this.isPhone = isPhone;
    }

    public String getIsReal() {
        return isReal;
    }

    public void setIsReal(String isReal) {
        this.isReal = isReal;
    }

    public String getMyLoveNum() {
        return myLoveNum;
    }

    public void setMyLoveNum(String myLoveNum) {
        this.myLoveNum = myLoveNum;
    }

    public String getNewSeeMeNum() {
        return newSeeMeNum;
    }

    public void setNewSeeMeNum(String newSeeMeNum) {
        this.newSeeMeNum = newSeeMeNum;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public int getVipLevel() {
        return vipLevel;
    }

    public void setVipLevel(int vipLevel) {
        this.vipLevel = vipLevel;
    }

    public String getWhoSeeMeNum() {
        return whoSeeMeNum;
    }

    public void setWhoSeeMeNum(String whoSeeMeNum) {
        this.whoSeeMeNum = whoSeeMeNum;
    }

    @Override
    public String toString() {
        return "UserCenterInfo{" +
                "avatar='" + avatar + '\'' +
                ", goldNum=" + goldNum +
                ", isName='" + isName + '\'' +
                ", isPhone='" + isPhone + '\'' +
                ", isReal='" + isReal + '\'' +
                ", myLoveNum='" + myLoveNum + '\'' +
                ", newSeeMeNum='" + newSeeMeNum + '\'' +
                ", nickname='" + nickname + '\'' +
                ", sex='" + sex + '\'' +
                ", userId='" + userId + '\'' +
                ", vipLevel='" + vipLevel + '\'' +
                ", whoSeeMeNum='" + whoSeeMeNum + '\'' +
                '}';
    }
}
