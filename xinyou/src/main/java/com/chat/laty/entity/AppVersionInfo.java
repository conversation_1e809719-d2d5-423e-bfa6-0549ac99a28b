package com.chat.laty.entity;

import com.google.gson.annotations.SerializedName;

/**
 * <AUTHOR>
 * @date 2023/9/20 11:13
 * @description:
 */
public class AppVersionInfo {

    @SerializedName("appType")
    private Integer appType;
    @SerializedName("downloadUrl")
    private String appUrl;
    @SerializedName("createTime")
    private String createTime;
    @SerializedName("forceUpdate")
    private Boolean forceUpdate;
    @SerializedName("modifyContent")
    private String updateContent;
    @SerializedName("versionCode")
    private Integer versionCode;
    @SerializedName("apkSize")
    private Integer apkSize;

    @SerializedName("updateStatus")
    private Integer updateStatus;
    @SerializedName("versionId")
    private String versionId;
    @SerializedName("versionName")
    private String versionName;
    @SerializedName("versionNumber")
    private String versionNumber;

    public Integer getUpdateStatus() {
        return updateStatus;
    }

    public void setUpdateStatus(Integer updateStatus) {
        this.updateStatus = updateStatus;
    }

    public Integer getApkSize() {
        return apkSize;
    }

    public void setApkSize(Integer apkSize) {
        this.apkSize = apkSize;
    }

    public String getVersionName() {
        return versionName;
    }

    public void setVersionName(String versionName) {
        this.versionName = versionName;
    }

    public Integer getAppType() {
        return appType;
    }

    public void setAppType(Integer appType) {
        this.appType = appType;
    }

    public String getAppUrl() {
        return appUrl;
    }

    public void setAppUrl(String appUrl) {
        this.appUrl = appUrl;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public Boolean getForceUpdate() {
        return forceUpdate;
    }

    public void setForceUpdate(Boolean forceUpdate) {
        this.forceUpdate = forceUpdate;
    }

    public String getUpdateContent() {
        return updateContent;
    }

    public void setUpdateContent(String updateContent) {
        this.updateContent = updateContent;
    }

    public Integer getVersionCode() {
        return versionCode;
    }

    public void setVersionCode(Integer versionCode) {
        this.versionCode = versionCode;
    }

    public String getVersionId() {
        return versionId;
    }

    public void setVersionId(String versionId) {
        this.versionId = versionId;
    }

    public String getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber;
    }
}
