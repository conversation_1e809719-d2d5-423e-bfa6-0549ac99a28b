package com.chat.laty.entity;

import java.util.ArrayList;
import java.util.List;

public class VipInfoModel {

    private String userId;
    private String avatar;
    private String nickname;
    private String vipEndTime;
    private String vipLevel;  // (0-不是会员 1-铜牌 2-银牌 3-金牌 4-铂金)
    private String vipName;

    private List<VipTypeModel> vipList;
    private List<PayTypeModel> payTypeList;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getVipEndTime() {
        return vipEndTime;
    }

    public void setVipEndTime(String vipEndTime) {
        this.vipEndTime = vipEndTime;
    }

    public String getVipLevel() {
        return vipLevel;
    }

    public void setVipLevel(String vipLevel) {
        this.vipLevel = vipLevel;
    }

    public String getVipName() {
        return vipName;
    }

    public void setVipName(String vipName) {
        this.vipName = vipName;
    }

    public List<VipTypeModel> getVipList() {
        return vipList;
    }

    public void setVipList(List<VipTypeModel> vipList) {
        this.vipList = vipList;
    }

    public List<PayTypeModel> getPayTypeList() {
        if (payTypeList == null) {
            payTypeList = new ArrayList<>();
        }
        return payTypeList;
    }

    public void setPayTypeList(List<PayTypeModel> payTypeList) {
        this.payTypeList = payTypeList;
    }

    public void addPayTypeList(List<PayTypeModel> payTypeList) {
        this.payTypeList.addAll(payTypeList);
    }

    public static class VipTypeModel {
        private String id;
        private String isOpen; // 是否开通
        private String isLogo; // 是否有标识
        private String isVisitor; // 是否有查看访客权限
        private int isPrivateAlbum;
        private int isPrivateVideo;
        private int isCallPermission;
        private int vipType;
        private float price;
        private String giveTextNum;
        private String giveVideoNum;
        private String giveVoiceNum;
        private String timeLength;
        private String videoDiscount;
        private String vipName;
        private String exclusiveBorder;
        private String exclusiveIdentifier;
        private String exclusiveNameColor;
        private List<VipGridModel> powerList;

        public List<VipGridModel> getPowerList() {
            return powerList;
        }

        public void setPowerList(List<VipGridModel> powerList) {
            this.powerList = powerList;
        }

        public int getIsPrivateVideo() {
            return isPrivateVideo;
        }

        public void setIsPrivateVideo(int isPrivateVideo) {
            this.isPrivateVideo = isPrivateVideo;
        }

        public int getIsPrivateAlbum() {
            return isPrivateAlbum;
        }

        public void setIsPrivateAlbum(int isPrivateAlbum) {
            this.isPrivateAlbum = isPrivateAlbum;
        }

        public int getIsCallPermission() {
            return isCallPermission;
        }

        public void setIsCallPermission(int isCallPermission) {
            this.isCallPermission = isCallPermission;
        }

        public int getVipType() {
            return vipType;
        }

        public void setVipType(int vipType) {
            this.vipType = vipType;
        }

        public String getGiveVideoNum() {
            return giveVideoNum;
        }

        public void setGiveVideoNum(String giveVideoNum) {
            this.giveVideoNum = giveVideoNum;
        }

        public String getGiveVoiceNum() {
            return giveVoiceNum;
        }

        public void setGiveVoiceNum(String giveVoiceNum) {
            this.giveVoiceNum = giveVoiceNum;
        }

        boolean selected;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getIsOpen() {
            return isOpen;
        }

        public void setIsOpen(String isOpen) {
            this.isOpen = isOpen;
        }

        public String getIsLogo() {
            return isLogo;
        }

        public void setIsLogo(String isLogo) {
            this.isLogo = isLogo;
        }

        public float getPrice() {
            return price;
        }

        public void setPrice(float price) {
            this.price = price;
        }

        public int getGiveTextNum() {
            int value = 0;
            try {
                value = Integer.parseInt(giveTextNum);
            } catch (Exception e) {
                e.printStackTrace();
            }
            return value;
        }

        public String getIsVisitor() {
            return isVisitor;
        }

        public void setIsVisitor(String isVisitor) {
            this.isVisitor = isVisitor;
        }

        public void setGiveTextNum(String giveTextNum) {
            this.giveTextNum = giveTextNum;
        }

        public int getTimeLength() {
            int value = 0;
            try {
                value = Integer.parseInt(timeLength);
            } catch (Exception e) {
                e.printStackTrace();
            }
            return value;
        }

        public void setTimeLength(String timeLength) {
            this.timeLength = timeLength;
        }

        public float getVideoDiscount() {
            float value = 0;
            try {
                value = Float.parseFloat(videoDiscount);
            } catch (Exception e) {
                e.printStackTrace();
            }
            return value;
        }

        public void setVideoDiscount(String videoDiscount) {
            this.videoDiscount = videoDiscount;
        }

        public String getVipName() {
            return vipName;
        }

        public void setVipName(String vipName) {
            this.vipName = vipName;
        }

        public String getExclusiveBorder() {
            return exclusiveBorder;
        }

        public void setExclusiveBorder(String exclusiveBorder) {
            this.exclusiveBorder = exclusiveBorder;
        }

        public String getExclusiveIdentifier() {
            return exclusiveIdentifier;
        }

        public void setExclusiveIdentifier(String exclusiveIdentifier) {
            this.exclusiveIdentifier = exclusiveIdentifier;
        }

        public String getExclusiveNameColor() {
            return exclusiveNameColor;
        }

        public void setExclusiveNameColor(String exclusiveNameColor) {
            this.exclusiveNameColor = exclusiveNameColor;
        }
    }

}
