package com.chat.laty.entity;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * <AUTHOR>
 * @date 2021/6/23.
 * description：炬联图片信息
 */
public class DiaplayPicInfo implements Parcelable {
    private String picUrl;

    public DiaplayPicInfo(String picUrl) {
        this.picUrl = picUrl;
    }

    public String getPicUrl() {
        return picUrl;
    }

    public void setPicUrl(String picUrl) {
        this.picUrl = picUrl;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.picUrl);
    }

    protected DiaplayPicInfo(Parcel in) {
        this.picUrl = in.readString();
    }

    public static final Creator<DiaplayPicInfo> CREATOR = new Creator<DiaplayPicInfo>() {
        @Override
        public DiaplayPicInfo createFromParcel(Parcel source) {
            return new DiaplayPicInfo(source);
        }

        @Override
        public DiaplayPicInfo[] newArray(int size) {
            return new DiaplayPicInfo[size];
        }
    };
}
