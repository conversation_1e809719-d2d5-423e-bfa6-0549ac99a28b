package com.chat.laty.entity;

import com.google.gson.annotations.SerializedName;

/**
 * <AUTHOR>
 * @date 2023/12/20 21:42
 * @description:
 */
public class XYUserInfo {

    @SerializedName("userId")
    private String userId;
    @SerializedName("avatar")
    private String avatar;
    @SerializedName("nickname")
    private String nickname;
    @SerializedName("sex")
    private String sex;
    @SerializedName("birthday")
    private String birthday;
    @SerializedName("liveAddress")
    private String liveAddress;
    @SerializedName("liveAddressCode")
    private String liveAddressCode;
    @SerializedName("isReal")
    private String isReal;
    @SerializedName("isName")
    private String isName;
    @SerializedName("isPhone")
    private String isPhone;
    @SerializedName("audioUrl")
    private String audioUrl;
    @SerializedName("audioLength")
    private String audioLength;
    @SerializedName("makeFriendsDetail")
    private String makeFriendsDetail;
    @SerializedName("height")
    private String height;
    @SerializedName("weight")
    private String weight;
    @SerializedName("educationName")
    private String educationName;
    @SerializedName("educationCode")
    private String educationCode;
    @SerializedName("careerName")
    private String careerName;
    @SerializedName("careerCode")
    private String careerCode;
    @SerializedName("incomeName")
    private String incomeName;
    @SerializedName("incomeCode")
    private String incomeCode;
    @SerializedName("impressionLabels")
    private String impressionLabels;
    @SerializedName("loveFriendLabels")
    private String loveFriendLabels;
    @SerializedName("emotionStatus")
    private String emotionStatus;
    @SerializedName("emotionStatusCode")
    private String emotionStatusCode;
    @SerializedName("liveStatus")
    private String liveStatus;

    @SerializedName("onlineStatus")
    private String onlineStatus;
    @SerializedName("liveStatusCode")
    private String liveStatusCode;
    @SerializedName("photoList")
    private String photoList;
    @SerializedName("trendsFiles")
    private String trendsFiles;
    @SerializedName("trendsType")
    private String trendsType;
    @SerializedName("coverUrl")
    private String coverUrl;

    @SerializedName("isFollow")
    private String isFollow;
    @SerializedName("mindNum")
    private String mindNum;
    @SerializedName("sysMindNum")
    private String sysMindNum;
    @SerializedName("goldNum")
    private String goldNum;
    @SerializedName("vipLevel")
    private int vipLevel;
    @SerializedName("voiceDisturb")
    private String voiceDisturb;
    @SerializedName("videoDisturb")
    private String videoDisturb;
    @SerializedName("freeVoiceNum")
    private String freeVoiceNum;
    @SerializedName("freeVideoNum")
    private String freeVideoNum;
    @SerializedName("notes")
    private String notes;

    @SerializedName("videoGearNum")
    private String videoGearNum;
    @SerializedName("privateAlbums")
    private String privateAlbums;
    @SerializedName("privateVideos")
    private String privateVideos;

    public String getPrivateVideos() {
        return privateVideos;
    }

    public void setPrivateVideos(String privateVideos) {
        this.privateVideos = privateVideos;
    }

    public String getPrivateAlbums() {
        return privateAlbums;
    }

    public void setPrivateAlbums(String privateAlbums) {
        this.privateAlbums = privateAlbums;
    }

    public String getVideoGearNum() {
        return videoGearNum;
    }

    public void setVideoGearNum(String videoGearNum) {
        this.videoGearNum = videoGearNum;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public String getFreeVoiceNum() {
        return freeVoiceNum;
    }

    public void setFreeVoiceNum(String freeVoiceNum) {
        this.freeVoiceNum = freeVoiceNum;
    }

    public String getFreeVideoNum() {
        return freeVideoNum;
    }

    public void setFreeVideoNum(String freeVideoNum) {
        this.freeVideoNum = freeVideoNum;
    }

    public String getVoiceDisturb() {
        return voiceDisturb;
    }

    public void setVoiceDisturb(String voiceDisturb) {
        this.voiceDisturb = voiceDisturb;
    }

    public String getVideoDisturb() {
        return videoDisturb;
    }

    public void setVideoDisturb(String videoDisturb) {
        this.videoDisturb = videoDisturb;
    }

    public int getVipLevel() {
        return vipLevel;
    }

    public void setVipLevel(int vipLevel) {
        this.vipLevel = vipLevel;
    }

    public String getTrendsType() {
        return trendsType;
    }

    public void setTrendsType(String trendsType) {
        this.trendsType = trendsType;
    }

    public String getTrendsFiles() {
        return trendsFiles;
    }

    public void setTrendsFiles(String trendsFiles) {
        this.trendsFiles = trendsFiles;
    }

    public String getSysMindNum() {
        return sysMindNum;
    }

    public void setSysMindNum(String sysMindNum) {
        this.sysMindNum = sysMindNum;
    }

    public String getGoldNum() {
        return goldNum;
    }

    public void setGoldNum(String goldNum) {
        this.goldNum = goldNum;
    }

    public String getMindNum() {
        return mindNum;
    }

    public void setMindNum(String mindNum) {
        this.mindNum = mindNum;
    }

    public String getIsFollow() {
        return isFollow;
    }

    public void setIsFollow(String isFollow) {
        this.isFollow = isFollow;
    }

    public String getPhotoList() {
        return photoList;
    }

    public void setPhotoList(String photoList) {
        this.photoList = photoList;
    }

    public String getCoverUrl() {
        return coverUrl;
    }

    public void setCoverUrl(String coverUrl) {
        this.coverUrl = coverUrl;
    }

    @SerializedName("age")
    private String age;

    public String getOnlineStatus() {
        return onlineStatus;
    }

    public void setOnlineStatus(String onlineStatus) {
        this.onlineStatus = onlineStatus;
    }

    public static class PhotoInfo {
        @SerializedName("id")
        private String id;
        @SerializedName("imageUrl")
        private String imageUrl;
        @SerializedName("isCover")
        private String isCover;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getImageUrl() {
            return imageUrl;
        }

        public void setImageUrl(String imageUrl) {
            this.imageUrl = imageUrl;
        }

        public String getIsCover() {
            return isCover;
        }

        public void setIsCover(String isCover) {
            this.isCover = isCover;
        }
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public String getLiveAddress() {
        return liveAddress;
    }

    public void setLiveAddress(String liveAddress) {
        this.liveAddress = liveAddress;
    }

    public String getLiveAddressCode() {
        return liveAddressCode;
    }

    public void setLiveAddressCode(String liveAddressCode) {
        this.liveAddressCode = liveAddressCode;
    }

    public String getIsReal() {
        return isReal;
    }

    public void setIsReal(String isReal) {
        this.isReal = isReal;
    }

    public String getIsName() {
        return isName;
    }

    public void setIsName(String isName) {
        this.isName = isName;
    }

    public String getIsPhone() {
        return isPhone;
    }

    public void setIsPhone(String isPhone) {
        this.isPhone = isPhone;
    }

    public String getAudioUrl() {
        return audioUrl;
    }

    public void setAudioUrl(String audioUrl) {
        this.audioUrl = audioUrl;
    }

    public String getAudioLength() {
        return audioLength;
    }

    public void setAudioLength(String audioLength) {
        this.audioLength = audioLength;
    }

    public String getMakeFriendsDetail() {
        return makeFriendsDetail;
    }

    public void setMakeFriendsDetail(String makeFriendsDetail) {
        this.makeFriendsDetail = makeFriendsDetail;
    }

    public String getHeight() {
        return height;
    }

    public void setHeight(String height) {
        this.height = height;
    }

    public String getWeight() {
        return weight;
    }

    public void setWeight(String weight) {
        this.weight = weight;
    }

    public String getEducationName() {
        return educationName;
    }

    public void setEducationName(String educationName) {
        this.educationName = educationName;
    }

    public String getEducationCode() {
        return educationCode;
    }

    public void setEducationCode(String educationCode) {
        this.educationCode = educationCode;
    }

    public String getCareerName() {
        return careerName;
    }

    public void setCareerName(String careerName) {
        this.careerName = careerName;
    }

    public String getCareerCode() {
        return careerCode;
    }

    public void setCareerCode(String careerCode) {
        this.careerCode = careerCode;
    }

    public String getIncomeName() {
        return incomeName;
    }

    public void setIncomeName(String incomeName) {
        this.incomeName = incomeName;
    }

    public String getIncomeCode() {
        return incomeCode;
    }

    public void setIncomeCode(String incomeCode) {
        this.incomeCode = incomeCode;
    }

    public String getImpressionLabels() {
        return impressionLabels;
    }

    public void setImpressionLabels(String impressionLabels) {
        this.impressionLabels = impressionLabels;
    }

    public String getLoveFriendLabels() {
        return loveFriendLabels;
    }

    public void setLoveFriendLabels(String loveFriendLabels) {
        this.loveFriendLabels = loveFriendLabels;
    }

    public String getEmotionStatus() {
        return emotionStatus;
    }

    public void setEmotionStatus(String emotionStatus) {
        this.emotionStatus = emotionStatus;
    }

    public String getEmotionStatusCode() {
        return emotionStatusCode;
    }

    public void setEmotionStatusCode(String emotionStatusCode) {
        this.emotionStatusCode = emotionStatusCode;
    }

    public String getLiveStatus() {
        return liveStatus;
    }

    public void setLiveStatus(String liveStatus) {
        this.liveStatus = liveStatus;
    }

    public String getLiveStatusCode() {
        return liveStatusCode;
    }

    public void setLiveStatusCode(String liveStatusCode) {
        this.liveStatusCode = liveStatusCode;
    }

    public String getAge() {
        return age;
    }

    public void setAge(String age) {
        this.age = age;
    }
}
