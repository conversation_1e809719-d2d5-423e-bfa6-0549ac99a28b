package com.chat.laty.entity;

import com.google.gson.annotations.SerializedName;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/16 22:13
 * @description:
 */
public class RewardsEntity {

    @SerializedName("diamondBalance")
    private String diamondBalance;
    @SerializedName("todayRewards")
    private String todayRewards;
    @SerializedName("aliPayName")
    private String aliPayName;
    @SerializedName("aliPayAccount")
    private String aliPayAccount;
    @SerializedName("wxPayName")
    private String wxPayName;
    @SerializedName("wxPayAccount")
    private String wxPayAccount;
    @SerializedName("withdrawalSettingList")
    private List<WithdrawalInfo> withdrawalSettings;
    @SerializedName("bankCardList")
    private List<BankCardInfo> bankCardList;
    @SerializedName("withdrawalPrompt")
    private String withdrawalPrompt;

    public List<BankCardInfo> getBankCardList() {
        return bankCardList;
    }

    public void setBankCardList(List<BankCardInfo> bankCardList) {
        this.bankCardList = bankCardList;
    }

    public String getDiamondBalance() {
        return diamondBalance;
    }

    public void setDiamondBalance(String diamondBalance) {
        this.diamondBalance = diamondBalance;
    }

    public String getTodayRewards() {
        return todayRewards;
    }

    public void setTodayRewards(String todayRewards) {
        this.todayRewards = todayRewards;
    }

    public String getAliPayName() {
        return aliPayName;
    }

    public void setAliPayName(String aliPayName) {
        this.aliPayName = aliPayName;
    }

    public String getAliPayAccount() {
        return aliPayAccount;
    }

    public void setAliPayAccount(String aliPayAccount) {
        this.aliPayAccount = aliPayAccount;
    }

    public String getWxPayName() {
        return wxPayName;
    }

    public void setWxPayName(String wxPayName) {
        this.wxPayName = wxPayName;
    }

    public String getWxPayAccount() {
        return wxPayAccount;
    }

    public void setWxPayAccount(String wxPayAccount) {
        this.wxPayAccount = wxPayAccount;
    }

    public List<WithdrawalInfo> getWithdrawalSettings() {
        return withdrawalSettings;
    }

    public void setWithdrawalSettings(List<WithdrawalInfo> withdrawalSettings) {
        this.withdrawalSettings = withdrawalSettings;
    }

    public String getWithdrawalPrompt() {
        return withdrawalPrompt;
    }

    public void setWithdrawalPrompt(String withdrawalPrompt) {
        this.withdrawalPrompt = withdrawalPrompt;
    }
}
