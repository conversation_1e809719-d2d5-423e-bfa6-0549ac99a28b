package com.chat.laty.entity;

import com.google.gson.annotations.SerializedName;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/26 20:11
 * @description:
 */
public class PickerViewBean {

    @SerializedName("userInfo")
    private EditUserInfo userInfo;
    @SerializedName("ynList")
    private List<LabelInfo> ynList;
    @SerializedName("educationList")
    private List<LabelInfo> educationList;
    @SerializedName("careerList")
    private List<LabelInfo> careerList;
    @SerializedName("yearIncomeList")
    private List<LabelInfo> yearIncomeList;
    @SerializedName("emotionStatusList")
    private List<LabelInfo> emotionStatusList;
    @SerializedName("liveStatusList")
    private List<LabelInfo> liveStatusList;
    @SerializedName("impressionList")
    private LabelInfo impressionList;
    @SerializedName("loveFriendList")
    private LabelInfo loveFriendList;


    public void setUserInfo(EditUserInfo userInfo) {
        this.userInfo = userInfo;
    }

    public List<LabelInfo> getYnList() {
        return ynList;
    }

    public void setYnList(List<LabelInfo> ynList) {
        this.ynList = ynList;
    }

    public List<LabelInfo> getEducationList() {
        return educationList;
    }

    public void setEducationList(List<LabelInfo> educationList) {
        this.educationList = educationList;
    }

    public List<LabelInfo> getCareerList() {
        return careerList;
    }

    public void setCareerList(List<LabelInfo> careerList) {
        this.careerList = careerList;
    }

    public List<LabelInfo> getYearIncomeList() {
        return yearIncomeList;
    }

    public void setYearIncomeList(List<LabelInfo> yearIncomeList) {
        this.yearIncomeList = yearIncomeList;
    }

    public List<LabelInfo> getEmotionStatusList() {
        return emotionStatusList;
    }

    public void setEmotionStatusList(List<LabelInfo> emotionStatusList) {
        this.emotionStatusList = emotionStatusList;
    }

    public List<LabelInfo> getLiveStatusList() {
        return liveStatusList;
    }

    public void setLiveStatusList(List<LabelInfo> liveStatusList) {
        this.liveStatusList = liveStatusList;
    }

    public LabelInfo getImpressionList() {
        return impressionList;
    }

    public void setImpressionList(LabelInfo impressionList) {
        this.impressionList = impressionList;
    }

    public LabelInfo getLoveFriendList() {
        return loveFriendList;
    }

    public void setLoveFriendList(LabelInfo loveFriendList) {
        this.loveFriendList = loveFriendList;
    }

    public EditUserInfo getUserInfo() {
        return userInfo;
    }
}
