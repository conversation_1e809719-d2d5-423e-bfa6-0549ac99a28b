package com.chat.laty.entity;

import com.google.gson.annotations.SerializedName;

/**
 * <AUTHOR>
 * @date 2023/12/22 19:13
 * @description:
 */
public class OtherUserInfo {

    @SerializedName("id")
    private String id;
    @SerializedName("avatar")
    private String avatar;
    @SerializedName("nickname")
    private String nickname;
    @SerializedName("sex")
    private String sex;
    @SerializedName("height")
    private String height;
    @SerializedName("age")
    private String age;
    @SerializedName("careerName")
    private String careerName;
    @SerializedName("trendsFiles")
    private String trendsFiles;

    @SerializedName("trendsType")
    private String trendsType;
    @SerializedName("makeFriendsDetail")
    private String makeFriendsDetail;
    @SerializedName("isReal")
    private String isReal;
    @SerializedName("onlineStatus")
    private String onlineStatus;

    @SerializedName("isAccost")
    private String isAccost;

    @SerializedName("vipLevel")
    private int vipLevel;

    public int getVipLevel() {
        return vipLevel;
    }

    public void setVipLevel(int vipLevel) {
        this.vipLevel = vipLevel;
    }

    public String getTrendsType() {
        return trendsType;
    }

    public void setTrendsType(String trendsType) {
        this.trendsType = trendsType;
    }

    private boolean isSelect = false;

    public boolean isSelect() {
        return isSelect;
    }

    public void setSelect(boolean select) {
        isSelect = select;
    }

    public String getIsAccost() {
        return isAccost;
    }

    public void setIsAccost(String isAccost) {
        this.isAccost = isAccost;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getHeight() {
        return height;
    }

    public void setHeight(String height) {
        this.height = height;
    }

    public String getAge() {
        return age;
    }

    public void setAge(String age) {
        this.age = age;
    }

    public String getCareerName() {
        return careerName;
    }

    public void setCareerName(String careerName) {
        this.careerName = careerName;
    }

    public String getTrendsFiles() {
        return trendsFiles;
    }

    public void setTrendsFiles(String trendsFiles) {
        this.trendsFiles = trendsFiles;
    }

    public String getMakeFriendsDetail() {
        return makeFriendsDetail;
    }

    public void setMakeFriendsDetail(String makeFriendsDetail) {
        this.makeFriendsDetail = makeFriendsDetail;
    }

    public String getIsReal() {
        return isReal;
    }

    public void setIsReal(String isReal) {
        this.isReal = isReal;
    }

    public String getOnlineStatus() {
        return onlineStatus;
    }

    public void setOnlineStatus(String onlineStatus) {
        this.onlineStatus = onlineStatus;
    }
}
