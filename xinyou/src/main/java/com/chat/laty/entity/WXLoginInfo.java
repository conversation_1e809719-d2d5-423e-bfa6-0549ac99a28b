package com.chat.laty.entity;

import com.google.gson.annotations.SerializedName;

/**
 * <AUTHOR>
 * @date 2023/12/13 13:19
 * @description:
 */
public class WXLoginInfo {

    @SerializedName("isNewUser")
    private Integer isNewUser;
    @SerializedName("token")
    private String token;
    @SerializedName("openId")
    private String openId;
    @SerializedName("loginUserVo")
    private RyUserInfo loginUser;

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public Integer getIsNewUser() {
        return isNewUser;
    }

    public void setIsNewUser(Integer isNewUser) {
        this.isNewUser = isNewUser;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public RyUserInfo getLoginUser() {
        return loginUser;
    }

    public void setLoginUser(RyUserInfo loginUser) {
        this.loginUser = loginUser;
    }
}
