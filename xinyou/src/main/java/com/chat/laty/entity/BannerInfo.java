package com.chat.laty.entity;

import com.google.gson.annotations.SerializedName;

/**
 * <AUTHOR>
 * @date 2023/12/9 22:41
 * @description:
 */
public class BannerInfo {
    @SerializedName("imageUrl")
    private String imageUrl;
    @SerializedName("id")
    private String id;
    @SerializedName("imageImages")
    private String imageImages;
    @SerializedName("linkUrl")
    private String linkUrl;



    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getImageImages() {
        return imageImages;
    }

    public void setImageImages(String imageImages) {
        this.imageImages = imageImages;
    }

    public String getLinkUrl() {
        return linkUrl;
    }

    public void setLinkUrl(String linkUrl) {
        this.linkUrl = linkUrl;
    }

    public BannerInfo(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }
}
