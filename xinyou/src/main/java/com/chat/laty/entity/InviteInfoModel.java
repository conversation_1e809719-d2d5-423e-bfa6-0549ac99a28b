package com.chat.laty.entity;

import java.util.List;

public class InviteInfoModel {

    private String invitationCode;
    private String inviteNow; // 是否显示立即邀请按钮(1-是 0-否)
    private String isBindingFriend; // 是否显示绑定好友按钮(1-是 0-否)
    private String isChangeCode; // 是否显示更改邀请码按钮(1-是 0-否)
    private String isMyBinding; // 是否显示自主绑定按钮(1-是 0-否)
    private String isName;  // 是否实名(1-是 0-否)
    private int nowLevel;  // 当前等级
    private String levelUpMsg;  // 当前等级文字


    private float totalRechargeNum;  // 充值奖励
    private float totalSocializeNum;  // 社交奖励

    private List<InviteInfoRulesModel> promotionRules; //推广规则

    private String maxSocializeBili;
    private String maxRechargeBili;

    public String getInvitationCode() {
        if (invitationCode == null) {
            return "--";
        }
        return invitationCode;
    }

    public void setInvitationCode(String invitationCode) {
        this.invitationCode = invitationCode;
    }

    public String getInviteNow() {
        return inviteNow;
    }

    public void setInviteNow(String inviteNow) {
        this.inviteNow = inviteNow;
    }

    public String getIsBindingFriend() {
        return isBindingFriend;
    }

    public void setIsBindingFriend(String isBindingFriend) {
        this.isBindingFriend = isBindingFriend;
    }

    public String getIsChangeCode() {
        return isChangeCode;
    }

    public void setIsChangeCode(String isChangeCode) {
        this.isChangeCode = isChangeCode;
    }

    public String getIsMyBinding() {
        return isMyBinding;
    }

    public void setIsMyBinding(String isMyBinding) {
        this.isMyBinding = isMyBinding;
    }

    public String getIsName() {
        return isName;
    }

    public void setIsName(String isName) {
        this.isName = isName;
    }

    public float getTotalRechargeNum() {
        return totalRechargeNum;
    }

    public void setTotalRechargeNum(float totalRechargeNum) {
        this.totalRechargeNum = totalRechargeNum;
    }

    public float getTotalSocializeNum() {
        return totalSocializeNum;
    }

    public void setTotalSocializeNum(float totalSocializeNum) {
        this.totalSocializeNum = totalSocializeNum;
    }

    public List<InviteInfoRulesModel> getPromotionRules() {
        return promotionRules;
    }

    public void setPromotionRules(List<InviteInfoRulesModel> promotionRules) {
        this.promotionRules = promotionRules;
    }

    public int getNowLevel() {
        return nowLevel;
    }

    public void setNowLevel(int nowLevel) {
        this.nowLevel = nowLevel;
    }

    public String getLevelUpMsg() {
        return levelUpMsg;
    }

    public void setLevelUpMsg(String levelUpMsg) {
        this.levelUpMsg = levelUpMsg;
    }

    public String getMaxSocializeBili() {
        return maxSocializeBili;
    }

    public void setMaxSocializeBili(String maxSocializeBili) {
        this.maxSocializeBili = maxSocializeBili;
    }

    public String getMaxRechargeBili() {
        return maxRechargeBili;
    }

    public void setMaxRechargeBili(String maxRechargeBili) {
        this.maxRechargeBili = maxRechargeBili;
    }
}
