package com.chat.laty.entity;

import com.google.gson.annotations.SerializedName;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/21 0:17
 * @description:
 */
public class AddressInfo {

    @SerializedName("id")
    private String id;
    @SerializedName("name")
    private String name;
    @SerializedName("code")
    private String code;
    @SerializedName("deep")
    private String deep;
    @SerializedName("pid")
    private String pid;
    @SerializedName("city")
    private List<CityInfo> city;

    public static class CityDTO {
        @SerializedName("id")
        private String id;
        @SerializedName("name")
        private String name;
        @SerializedName("code")
        private String code;
        @SerializedName("deep")
        private String deep;
        @SerializedName("pid")
        private String pid;
        @SerializedName("area")
        private List<CityInfo> area;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getDeep() {
            return deep;
        }

        public void setDeep(String deep) {
            this.deep = deep;
        }

        public String getPid() {
            return pid;
        }

        public void setPid(String pid) {
            this.pid = pid;
        }

        public List<CityInfo> getArea() {
            return area;
        }

        public void setArea(List<CityInfo> area) {
            this.area = area;
        }
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDeep() {
        return deep;
    }

    public void setDeep(String deep) {
        this.deep = deep;
    }

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public List<CityInfo> getCity() {
        return city;
    }

    public void setCity(List<CityInfo> city) {
        this.city = city;
    }
}
