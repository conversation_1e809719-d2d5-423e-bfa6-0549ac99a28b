package com.chat.laty.entity;

import com.google.gson.annotations.SerializedName;

/**
 * <AUTHOR>
 * @date 2024/1/21 21:47
 * @description:
 */
public class MessageUnReadInfo {

    @SerializedName("sysMessageNum")
    private Integer sysMessageNum;
    @SerializedName("likeNum")
    private Integer likeNum;
    @SerializedName("visitorNum")
    private Integer visitorNum;
    @SerializedName("reportNum")
    private Integer reportNum;

    public Integer getSysMessageNum() {
        return sysMessageNum;
    }

    public void setSysMessageNum(Integer sysMessageNum) {
        this.sysMessageNum = sysMessageNum;
    }

    public Integer getLikeNum() {
        return likeNum;
    }

    public void setLikeNum(Integer likeNum) {
        this.likeNum = likeNum;
    }

    public Integer getVisitorNum() {
        return visitorNum;
    }

    public void setVisitorNum(Integer visitorNum) {
        this.visitorNum = visitorNum;
    }

    public Integer getReportNum() {
        return reportNum;
    }

    public void setReportNum(Integer reportNum) {
        this.reportNum = reportNum;
    }
}
