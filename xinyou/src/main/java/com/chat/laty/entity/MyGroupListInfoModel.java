package com.chat.laty.entity;

import android.os.Parcel;
import android.os.Parcelable;

import androidx.annotation.NonNull;

public class MyGroupListInfoModel implements Parcelable {

    private String userId;
    private String avatar;
    private String nickname;
    private int sex;
    private String isSettingsLevel;  //是否设定了级别(1-是 0-否)
    private String registerTime;
    private String isLogOff;
    private int dbnLevel;  //分销等级(0-普通用户 10-会员 20-超级会员 30-运营商 40-合伙人
    private int directNum;
    private float plugNum;
    private float rechargeNum;
    private float socializeNum;

    private int jrdirectNum;
    private float jrplugNum;
    private float jrrechargeNum;
    private float jrsocializeNum;

    private float rechargeToday;


    public MyGroupListInfoModel() {

    }



    protected MyGroupListInfoModel(Parcel in) {
        userId = in.readString();
        avatar = in.readString();
        nickname = in.readString();
        isSettingsLevel = in.readString();
        registerTime = in.readString();
        isLogOff = in.readString();
        dbnLevel = in.readInt();
        directNum = in.readInt();
        plugNum = in.readFloat();
        rechargeNum = in.readFloat();
        socializeNum = in.readFloat();
        jrdirectNum = in.readInt();
        jrplugNum = in.readFloat();
        jrrechargeNum = in.readFloat();
        jrsocializeNum = in.readFloat();
        rechargeToday=in.readFloat();
        sex=in.readInt();
    }

    public static final Creator<MyGroupListInfoModel> CREATOR = new Creator<MyGroupListInfoModel>() {
        @Override
        public MyGroupListInfoModel createFromParcel(Parcel in) {
            return new MyGroupListInfoModel(in);
        }

        @Override
        public MyGroupListInfoModel[] newArray(int size) {
            return new MyGroupListInfoModel[size];
        }
    };

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getIsSettingsLevel() {
        return isSettingsLevel;
    }

    public void setIsSettingsLevel(String isSettingsLevel) {
        this.isSettingsLevel = isSettingsLevel;
    }

    public String getIsLogOff() {
        return isLogOff;
    }

    public void setIsLogOff(String isLogOff) {
        this.isLogOff = isLogOff;
    }

    public String getRegisterTime() {
        return registerTime;
    }

    public void setRegisterTime(String registerTime) {
        this.registerTime = registerTime;
    }

    public int getDbnLevel() {
        return dbnLevel;
    }

    public void setDbnLevel(int dbnLevel) {
        this.dbnLevel = dbnLevel;
    }

    public int getDirectNum() {
        return directNum;
    }

    public void setDirectNum(int directNum) {
        this.directNum = directNum;
    }

    public float getPlugNum() {
        return plugNum;
    }

    public void setPlugNum(float plugNum) {
        this.plugNum = plugNum;
    }

    public float getRechargeNum() {
        return rechargeNum;
    }

    public void setRechargeNum(float rechargeNum) {
        this.rechargeNum = rechargeNum;
    }

    public float getSocializeNum() {
        return socializeNum;
    }

    public void setSocializeNum(float socializeNum) {
        this.socializeNum = socializeNum;
    }

    public float getRechargeToday() {
        return rechargeToday;
    }

    public void setRechargeToday(float rechargeToday) {
        this.rechargeToday = rechargeToday;
    }

    public int getSex() {
        return sex;
    }

    public void setSex(int sex) {
        this.sex = sex;
    }

    public int getJrdirectNum() {
        return jrdirectNum;
    }

    public void setJrdirectNum(int jrdirectNum) {
        this.jrdirectNum = jrdirectNum;
    }

    public float getJrplugNum() {
        return jrplugNum;
    }

    public void setJrplugNum(float jrplugNum) {
        this.jrplugNum = jrplugNum;
    }

    public float getJrrechargeNum() {
        return jrrechargeNum;
    }

    public void setJrrechargeNum(float jrrechargeNum) {
        this.jrrechargeNum = jrrechargeNum;
    }

    public float getJrsocializeNum() {
        return jrsocializeNum;
    }

    public void setJrsocializeNum(float jrsocializeNum) {
        this.jrsocializeNum = jrsocializeNum;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(@NonNull Parcel parcel, int i) {
        parcel.writeString(userId);
        parcel.writeString(avatar);
        parcel.writeString(nickname);
        parcel.writeString(isSettingsLevel);
        parcel.writeString(registerTime);
        parcel.writeString(isLogOff);
        parcel.writeInt(dbnLevel);
        parcel.writeInt(directNum);
        parcel.writeFloat(plugNum);
        parcel.writeFloat(rechargeNum);
        parcel.writeFloat(socializeNum);
        parcel.writeInt(jrdirectNum);
        parcel.writeFloat(jrplugNum);
        parcel.writeFloat(jrrechargeNum);
        parcel.writeFloat(jrsocializeNum);
        parcel.writeFloat(rechargeToday);
        parcel.writeInt(sex);
    }
}

