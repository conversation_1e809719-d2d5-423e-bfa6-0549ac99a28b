package com.chat.laty.entity;

import com.google.gson.annotations.SerializedName;

/**
 * <AUTHOR>
 * @date 2023/12/25 23:30
 * @description:
 */
public class AccostLanguageInfo {

    @SerializedName("id")
    private String id;
    @SerializedName("tempName")
    private String tempName;
    @SerializedName("tempText")
    private String tempText;
    @SerializedName("tempImg")
    private String tempImg;
    @SerializedName("tempImgThum")
    private String tempImgThum;
    @SerializedName("voiceUrl")
    private String voiceUrl;
    @SerializedName("isDefault")
    private String isDefault;
    @SerializedName("status")
    private String status;
    @SerializedName("statusDetail")
    private String statusDetail;
    @SerializedName("imgUrl")
    private String imgUrl;

    private boolean isSelect = false;

    public boolean isSelect() {
        return isSelect;
    }

    public void setSelect(boolean select) {
        isSelect = select;
    }

    public String getImgUrl() {
        return imgUrl;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }

    @SerializedName("voiceTimeLength")
    private String voiceTimeLength;

    public String getVoiceTimeLength() {
        return voiceTimeLength;
    }

    public void setVoiceTimeLength(String voiceTimeLength) {
        this.voiceTimeLength = voiceTimeLength;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTempName() {
        return tempName;
    }

    public void setTempName(String tempName) {
        this.tempName = tempName;
    }

    public String getTempText() {
        return tempText;
    }

    public void setTempText(String tempText) {
        this.tempText = tempText;
    }

    public String getTempImg() {
        return tempImg;
    }

    public void setTempImg(String tempImg) {
        this.tempImg = tempImg;
    }

    public String getTempImgThum() {
        return tempImgThum;
    }

    public void setTempImgThum(String tempImgThum) {
        this.tempImgThum = tempImgThum;
    }

    public String getVoiceUrl() {
        return voiceUrl;
    }

    public void setVoiceUrl(String voiceUrl) {
        this.voiceUrl = voiceUrl;
    }

    public String getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(String isDefault) {
        this.isDefault = isDefault;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatusDetail() {
        return statusDetail;
    }

    public void setStatusDetail(String statusDetail) {
        this.statusDetail = statusDetail;
    }
}
