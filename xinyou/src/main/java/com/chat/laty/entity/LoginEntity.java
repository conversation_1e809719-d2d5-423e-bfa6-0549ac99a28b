package com.chat.laty.entity;

import com.google.gson.annotations.SerializedName;

/**
 * <AUTHOR>
 * @date 2023/11/28 11:36
 * @description:
 */
public class LoginEntity {

    @SerializedName("token")
    private String token;
    @SerializedName("isNewUser")
    private Integer isNewUser;
    @SerializedName("openId")
    private String openId;
    @SerializedName("loginUserVo")
    private RyUserInfo userInfo;

    public Integer getIsNewUser() {
        return isNewUser;
    }

    public void setIsNewUser(Integer isNewUser) {
        this.isNewUser = isNewUser;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public RyUserInfo getUserInfo() {
        return userInfo;
    }

    public void setUserInfo(RyUserInfo userInfo) {
        this.userInfo = userInfo;
    }
}
