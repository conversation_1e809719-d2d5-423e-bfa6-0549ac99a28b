package com.chat.laty.entity;

import com.google.gson.annotations.SerializedName;

/**
 * <AUTHOR>
 * @date 2024/1/19 17:46
 * @description:
 */
public class MasonryInfo {
    @SerializedName("createTime")
    private String createTime;
    @SerializedName("changeDiamond")
    private String changeDiamond;
    @SerializedName("changeDetail")
    private String changeDetail;
    @SerializedName("lastDiamond")
    private String lastDiamond;
    @SerializedName("withdrawalAmount")
    private String withdrawalAmount;
    @SerializedName("commission")
    private String commission;

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getChangeDiamond() {
        return changeDiamond;
    }

    public void setChangeDiamond(String changeDiamond) {
        this.changeDiamond = changeDiamond;
    }

    public String getChangeDetail() {
        return changeDetail;
    }

    public void setChangeDetail(String changeDetail) {
        this.changeDetail = changeDetail;
    }

    public String getLastDiamond() {
        return lastDiamond;
    }

    public void setLastDiamond(String lastDiamond) {
        this.lastDiamond = lastDiamond;
    }

    public String getWithdrawalAmount() {
        return withdrawalAmount;
    }

    public void setWithdrawalAmount(String withdrawalAmount) {
        this.withdrawalAmount = withdrawalAmount;
    }

    public String getCommission() {
        return commission;
    }

    public void setCommission(String commission) {
        this.commission = commission;
    }
}
