package com.chat.laty.entity;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/1/20 21:29
 * @description:
 */
public class FatePairingInfo implements Serializable {
    @SerializedName("type")
    private String type;
    @SerializedName("userId")
    private String userId;
    @SerializedName("cmd")
    private String cmd;
    @SerializedName("msgTxt")
    private String msgTxt;

    public String getCmd() {
        return cmd;
    }

    public void setCmd(String cmd) {
        this.cmd = cmd;
    }

    public String getMsgTxt() {
        return msgTxt;
    }

    public void setMsgTxt(String msgTxt) {
        this.msgTxt = msgTxt;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    @Override
    public String toString() {
        return "FatePairingInfo{" +
                "type='" + type + '\'' +
                ", userId='" + userId + '\'' +
                ", cmd='" + cmd + '\'' +
                ", msgTxt='" + msgTxt + '\'' +
                '}';
    }
}
