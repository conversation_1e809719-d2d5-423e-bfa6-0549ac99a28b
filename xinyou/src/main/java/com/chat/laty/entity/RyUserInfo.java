package com.chat.laty.entity;

import com.google.gson.annotations.SerializedName;

import org.greenrobot.greendao.annotation.Entity;
import org.greenrobot.greendao.annotation.Generated;
import org.greenrobot.greendao.annotation.Index;

/**
 * <AUTHOR>
 * @date 2023/12/13 17:15
 * @description:
 */

@Entity
public class RyUserInfo {

    @SerializedName("id")
    @Index(unique = true)
    private String id;
    @SerializedName("avatar")
    private String avatar;
    @SerializedName("nickname")
    private String nickname;
    @SerializedName("sex")
    private String sex;
    @SerializedName("invitationCode")
    private String invitationCode;
    @SerializedName("birthday")
    private String birthday;
    @SerializedName("liveAddress")
    private String liveAddress;
    @SerializedName("liveAddressCode")
    private String liveAddressCode;
    @SerializedName("userId")
    private String userId;
    @SerializedName("liveLatLng")
    private String liveLatLng;
    @SerializedName("isReal")
    private String isReal;
    @SerializedName("isName")
    private String isName;
    @SerializedName("isPhone")
    private String isPhone;
    @SerializedName("audioUrl")
    private String audioUrl;
    @SerializedName("audioLength")
    private String audioLength;
    @SerializedName("makeFriendsDetail")
    private String makeFriendsDetail;
    @SerializedName("height")
    private String height;
    @SerializedName("weight")
    private String weight;
    @SerializedName("educationName")
    private String educationName;
    @SerializedName("educationCode")
    private String educationCode;
    @SerializedName("careerName")
    private String careerName;
    @SerializedName("careerCode")
    private String careerCode;
    @SerializedName("incomeName")
    private String incomeName;
    @SerializedName("incomeCode")
    private String incomeCode;
    @SerializedName("impressionLabels")
    private String impressionLabels;
    @SerializedName("loveFriendLabels")
    private String loveFriendLabels;
    @SerializedName("emotionStatus")
    private String emotionStatus;
    @SerializedName("emotionStatusCode")
    private String emotionStatusCode;
    @SerializedName("liveStatus")
    private String liveStatus;
    @SerializedName("liveStatusCode")
    private String liveStatusCode;
    @SerializedName("photoList")
    private String photoList;
    @SerializedName("coverUrl")
    private String coverUrl;
    @SerializedName("age")
    private String age;
    @SerializedName("trendsFiles")
    private String trendsFiles;
    @SerializedName("onlineStatus")
    private String onlineStatus;
    @SerializedName("hideLocation")
    private String hideLocation;
    @SerializedName("hideCity")
    private String hideCity;
    @SerializedName("hideOnline")
    private String hideOnline;
    @SerializedName("videoDisturb")
    private String videoDisturb;
    @SerializedName("voiceDisturb")
    private String voiceDisturb;
    @SerializedName("manAccostId")
    private String manAccostId;
    @SerializedName("womanAccostId")
    private String womanAccostId;
    @SerializedName("vipLevel")
    private int vipLevel;

    @SerializedName("notes")
    private String notes;

    @Generated(hash = 1747441163)
    public RyUserInfo(String id, String avatar, String nickname, String sex,
            String invitationCode, String birthday, String liveAddress,
            String liveAddressCode, String userId, String liveLatLng, String isReal,
            String isName, String isPhone, String audioUrl, String audioLength,
            String makeFriendsDetail, String height, String weight,
            String educationName, String educationCode, String careerName,
            String careerCode, String incomeName, String incomeCode,
            String impressionLabels, String loveFriendLabels, String emotionStatus,
            String emotionStatusCode, String liveStatus, String liveStatusCode,
            String photoList, String coverUrl, String age, String trendsFiles,
            String onlineStatus, String hideLocation, String hideCity,
            String hideOnline, String videoDisturb, String voiceDisturb,
            String manAccostId, String womanAccostId, int vipLevel, String notes) {
        this.id = id;
        this.avatar = avatar;
        this.nickname = nickname;
        this.sex = sex;
        this.invitationCode = invitationCode;
        this.birthday = birthday;
        this.liveAddress = liveAddress;
        this.liveAddressCode = liveAddressCode;
        this.userId = userId;
        this.liveLatLng = liveLatLng;
        this.isReal = isReal;
        this.isName = isName;
        this.isPhone = isPhone;
        this.audioUrl = audioUrl;
        this.audioLength = audioLength;
        this.makeFriendsDetail = makeFriendsDetail;
        this.height = height;
        this.weight = weight;
        this.educationName = educationName;
        this.educationCode = educationCode;
        this.careerName = careerName;
        this.careerCode = careerCode;
        this.incomeName = incomeName;
        this.incomeCode = incomeCode;
        this.impressionLabels = impressionLabels;
        this.loveFriendLabels = loveFriendLabels;
        this.emotionStatus = emotionStatus;
        this.emotionStatusCode = emotionStatusCode;
        this.liveStatus = liveStatus;
        this.liveStatusCode = liveStatusCode;
        this.photoList = photoList;
        this.coverUrl = coverUrl;
        this.age = age;
        this.trendsFiles = trendsFiles;
        this.onlineStatus = onlineStatus;
        this.hideLocation = hideLocation;
        this.hideCity = hideCity;
        this.hideOnline = hideOnline;
        this.videoDisturb = videoDisturb;
        this.voiceDisturb = voiceDisturb;
        this.manAccostId = manAccostId;
        this.womanAccostId = womanAccostId;
        this.vipLevel = vipLevel;
        this.notes = notes;
    }

    @Generated(hash = 1299259702)
    public RyUserInfo() {
    }

    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getAvatar() {
        return this.avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getNickname() {
        return this.nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getSex() {
        return this.sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getInvitationCode() {
        return this.invitationCode;
    }

    public void setInvitationCode(String invitationCode) {
        this.invitationCode = invitationCode;
    }

    public String getBirthday() {
        return this.birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public String getLiveAddress() {
        return this.liveAddress;
    }

    public void setLiveAddress(String liveAddress) {
        this.liveAddress = liveAddress;
    }

    public String getLiveAddressCode() {
        return this.liveAddressCode;
    }

    public void setLiveAddressCode(String liveAddressCode) {
        this.liveAddressCode = liveAddressCode;
    }

    public String getUserId() {
        return this.userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getLiveLatLng() {
        return this.liveLatLng;
    }

    public void setLiveLatLng(String liveLatLng) {
        this.liveLatLng = liveLatLng;
    }

    public String getIsReal() {
        return this.isReal;
    }

    public void setIsReal(String isReal) {
        this.isReal = isReal;
    }

    public String getIsName() {
        return this.isName;
    }

    public void setIsName(String isName) {
        this.isName = isName;
    }

    public String getIsPhone() {
        return this.isPhone;
    }

    public void setIsPhone(String isPhone) {
        this.isPhone = isPhone;
    }

    public String getAudioUrl() {
        return this.audioUrl;
    }

    public void setAudioUrl(String audioUrl) {
        this.audioUrl = audioUrl;
    }

    public String getAudioLength() {
        return this.audioLength;
    }

    public void setAudioLength(String audioLength) {
        this.audioLength = audioLength;
    }

    public String getMakeFriendsDetail() {
        return this.makeFriendsDetail;
    }

    public void setMakeFriendsDetail(String makeFriendsDetail) {
        this.makeFriendsDetail = makeFriendsDetail;
    }

    public String getHeight() {
        return this.height;
    }

    public void setHeight(String height) {
        this.height = height;
    }

    public String getWeight() {
        return this.weight;
    }

    public void setWeight(String weight) {
        this.weight = weight;
    }

    public String getEducationName() {
        return this.educationName;
    }

    public void setEducationName(String educationName) {
        this.educationName = educationName;
    }

    public String getEducationCode() {
        return this.educationCode;
    }

    public void setEducationCode(String educationCode) {
        this.educationCode = educationCode;
    }

    public String getCareerName() {
        return this.careerName;
    }

    public void setCareerName(String careerName) {
        this.careerName = careerName;
    }

    public String getCareerCode() {
        return this.careerCode;
    }

    public void setCareerCode(String careerCode) {
        this.careerCode = careerCode;
    }

    public String getIncomeName() {
        return this.incomeName;
    }

    public void setIncomeName(String incomeName) {
        this.incomeName = incomeName;
    }

    public String getIncomeCode() {
        return this.incomeCode;
    }

    public void setIncomeCode(String incomeCode) {
        this.incomeCode = incomeCode;
    }

    public String getImpressionLabels() {
        return this.impressionLabels;
    }

    public void setImpressionLabels(String impressionLabels) {
        this.impressionLabels = impressionLabels;
    }

    public String getLoveFriendLabels() {
        return this.loveFriendLabels;
    }

    public void setLoveFriendLabels(String loveFriendLabels) {
        this.loveFriendLabels = loveFriendLabels;
    }

    public String getEmotionStatus() {
        return this.emotionStatus;
    }

    public void setEmotionStatus(String emotionStatus) {
        this.emotionStatus = emotionStatus;
    }

    public String getEmotionStatusCode() {
        return this.emotionStatusCode;
    }

    public void setEmotionStatusCode(String emotionStatusCode) {
        this.emotionStatusCode = emotionStatusCode;
    }

    public String getLiveStatus() {
        return this.liveStatus;
    }

    public void setLiveStatus(String liveStatus) {
        this.liveStatus = liveStatus;
    }

    public String getLiveStatusCode() {
        return this.liveStatusCode;
    }

    public void setLiveStatusCode(String liveStatusCode) {
        this.liveStatusCode = liveStatusCode;
    }

    public String getPhotoList() {
        return this.photoList;
    }

    public void setPhotoList(String photoList) {
        this.photoList = photoList;
    }

    public String getCoverUrl() {
        return this.coverUrl;
    }

    public void setCoverUrl(String coverUrl) {
        this.coverUrl = coverUrl;
    }

    public String getAge() {
        return this.age;
    }

    public void setAge(String age) {
        this.age = age;
    }

    public String getTrendsFiles() {
        return this.trendsFiles;
    }

    public void setTrendsFiles(String trendsFiles) {
        this.trendsFiles = trendsFiles;
    }

    public String getOnlineStatus() {
        return this.onlineStatus;
    }

    public void setOnlineStatus(String onlineStatus) {
        this.onlineStatus = onlineStatus;
    }

    public String getHideLocation() {
        return this.hideLocation;
    }

    public void setHideLocation(String hideLocation) {
        this.hideLocation = hideLocation;
    }

    public String getHideCity() {
        return this.hideCity;
    }

    public void setHideCity(String hideCity) {
        this.hideCity = hideCity;
    }

    public String getHideOnline() {
        return this.hideOnline;
    }

    public void setHideOnline(String hideOnline) {
        this.hideOnline = hideOnline;
    }

    public String getVideoDisturb() {
        return this.videoDisturb;
    }

    public void setVideoDisturb(String videoDisturb) {
        this.videoDisturb = videoDisturb;
    }

    public String getVoiceDisturb() {
        return this.voiceDisturb;
    }

    public void setVoiceDisturb(String voiceDisturb) {
        this.voiceDisturb = voiceDisturb;
    }

    public String getManAccostId() {
        return this.manAccostId;
    }

    public void setManAccostId(String manAccostId) {
        this.manAccostId = manAccostId;
    }

    public String getWomanAccostId() {
        return this.womanAccostId;
    }

    public void setWomanAccostId(String womanAccostId) {
        this.womanAccostId = womanAccostId;
    }

    public int getVipLevel() {
        return this.vipLevel;
    }

    public void setVipLevel(int vipLevel) {
        this.vipLevel = vipLevel;
    }

    public String getNotes() {
        return this.notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    @Override
    public String toString() {
        return "RyUserInfo{" +
                "id='" + id + '\'' +
                ", avatar='" + avatar + '\'' +
                ", nickname='" + nickname + '\'' +
                ", sex='" + sex + '\'' +
                ", invitationCode='" + invitationCode + '\'' +
                ", birthday='" + birthday + '\'' +
                ", liveAddress='" + liveAddress + '\'' +
                ", liveAddressCode='" + liveAddressCode + '\'' +
                ", userId='" + userId + '\'' +
                ", liveLatLng='" + liveLatLng + '\'' +
                ", isReal='" + isReal + '\'' +
                ", isName='" + isName + '\'' +
                ", isPhone='" + isPhone + '\'' +
                ", audioUrl='" + audioUrl + '\'' +
                ", audioLength='" + audioLength + '\'' +
                ", makeFriendsDetail='" + makeFriendsDetail + '\'' +
                ", height='" + height + '\'' +
                ", weight='" + weight + '\'' +
                ", educationName='" + educationName + '\'' +
                ", educationCode='" + educationCode + '\'' +
                ", careerName='" + careerName + '\'' +
                ", careerCode='" + careerCode + '\'' +
                ", incomeName='" + incomeName + '\'' +
                ", incomeCode='" + incomeCode + '\'' +
                ", impressionLabels='" + impressionLabels + '\'' +
                ", loveFriendLabels='" + loveFriendLabels + '\'' +
                ", emotionStatus='" + emotionStatus + '\'' +
                ", emotionStatusCode='" + emotionStatusCode + '\'' +
                ", liveStatus='" + liveStatus + '\'' +
                ", liveStatusCode='" + liveStatusCode + '\'' +
                ", photoList='" + photoList + '\'' +
                ", coverUrl='" + coverUrl + '\'' +
                ", age='" + age + '\'' +
                ", trendsFiles='" + trendsFiles + '\'' +
                ", onlineStatus='" + onlineStatus + '\'' +
                ", hideLocation='" + hideLocation + '\'' +
                ", hideCity='" + hideCity + '\'' +
                ", hideOnline='" + hideOnline + '\'' +
                ", videoDisturb='" + videoDisturb + '\'' +
                ", voiceDisturb='" + voiceDisturb + '\'' +
                ", manAccostId='" + manAccostId + '\'' +
                ", womanAccostId='" + womanAccostId + '\'' +
                ", vipLevel='" + vipLevel + '\'' +
                ", notes='" + notes + '\'' +
                '}';
    }
}
