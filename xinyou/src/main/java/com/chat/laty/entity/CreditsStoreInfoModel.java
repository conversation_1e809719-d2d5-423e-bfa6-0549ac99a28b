package com.chat.laty.entity;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.List;

public class CreditsStoreInfoModel {

    private int freeTextNum;
    private int freeVoiceNum;
    private int freeVideoNum;
    private float integralBalance;
    private List<CreditStoreItemModel> textList;
    private List<CreditStoreItemModel> voiceList;
    private List<CreditStoreItemModel> videoList;

    public int getFreeTextNum() {
        return freeTextNum;
    }

    public void setFreeTextNum(int freeTextNum) {
        this.freeTextNum = freeTextNum;
    }

    public int getFreeVoiceNum() {
        return freeVoiceNum;
    }

    public void setFreeVoiceNum(int freeVoiceNum) {
        this.freeVoiceNum = freeVoiceNum;
    }

    public int getFreeVideoNum() {
        return freeVideoNum;
    }

    public void setFreeVideoNum(int freeVideoNum) {
        this.freeVideoNum = freeVideoNum;
    }

    public float getIntegralBalance() {
        return integralBalance;
    }

    public void setIntegralBalance(float integralBalance) {
        this.integralBalance = integralBalance;
    }

    public List<CreditStoreItemModel> getTextList() {
        return textList;
    }

    public void setTextList(List<CreditStoreItemModel> textList) {
        this.textList = textList;
    }

    public List<CreditStoreItemModel> getVoiceList() {
        return voiceList;
    }

    public void setVoiceList(List<CreditStoreItemModel> voiceList) {
        this.voiceList = voiceList;
    }

    public List<CreditStoreItemModel> getVideoList() {
        return videoList;
    }

    public void setVideoList(List<CreditStoreItemModel> videoList) {
        this.videoList = videoList;
    }

    public static class CreditStoreItemModel implements Parcelable {
        private String id;
        private String detail;
        private String name;
        private int number;
        private float price;
        private String productImg;

        public String msgType;

        public CreditStoreItemModel() {
        }

        protected CreditStoreItemModel(Parcel in) {
            id = in.readString();
            detail = in.readString();
            name = in.readString();
            number = in.readInt();
            price = in.readFloat();
            productImg = in.readString();
            msgType = in.readString();
        }

        @Override
        public void writeToParcel(Parcel dest, int flags) {
            dest.writeString(id);
            dest.writeString(detail);
            dest.writeString(name);
            dest.writeInt(number);
            dest.writeFloat(price);
            dest.writeString(productImg);
            dest.writeString(msgType);
        }

        @Override
        public int describeContents() {
            return 0;
        }

        public static final Creator<CreditStoreItemModel> CREATOR = new Creator<CreditStoreItemModel>() {
            @Override
            public CreditStoreItemModel createFromParcel(Parcel in) {
                return new CreditStoreItemModel(in);
            }

            @Override
            public CreditStoreItemModel[] newArray(int size) {
                return new CreditStoreItemModel[size];
            }
        };

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public float getPrice() {
            return price;
        }

        public void setPrice(float price) {
            this.price = price;
        }

        public String getDetail() {
            return detail;
        }

        public void setDetail(String detail) {
            this.detail = detail;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getNumber() {
            return number;
        }

        public void setNumber(int number) {
            this.number = number;
        }

        public String getProductImg() {
            return productImg;
        }

        public void setProductImg(String productImg) {
            this.productImg = productImg;
        }
    }
}
