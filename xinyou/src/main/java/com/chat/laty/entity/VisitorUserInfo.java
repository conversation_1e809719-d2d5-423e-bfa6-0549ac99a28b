package com.chat.laty.entity;

import com.google.gson.annotations.SerializedName;

/**
 * <AUTHOR>
 * @date 2024/1/19 21:44
 * @description:
 */
public class VisitorUserInfo {

    @SerializedName("toUserAvatar")
    private String toUserAvatar;
    @SerializedName("toUserId")
    private String toUserId;
    @SerializedName("toUserName")
    private String toUserName;
    @SerializedName("toUserSex")
    private String toUserSex;
    @SerializedName("createDate")
    private String createTime;

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getToUserAvatar() {
        return toUserAvatar;
    }

    public void setToUserAvatar(String toUserAvatar) {
        this.toUserAvatar = toUserAvatar;
    }

    public String getToUserId() {
        return toUserId;
    }

    public void setToUserId(String toUserId) {
        this.toUserId = toUserId;
    }

    public String getToUserName() {
        return toUserName;
    }

    public void setToUserName(String toUserName) {
        this.toUserName = toUserName;
    }

    public String getToUserSex() {
        return toUserSex;
    }

    public void setToUserSex(String toUserSex) {
        this.toUserSex = toUserSex;
    }
}
