package com.chat.laty.entity;

import com.google.gson.annotations.SerializedName;

/**
 * <AUTHOR>
 * @date 2024/1/25 11:57
 * @description:
 */
public class SystemMessageInfo {

    @SerializedName("id")
    private String id;
    @SerializedName("createDate")
    private String createDate;
    @SerializedName("message")
    private String message;
    @SerializedName("communityId")
    private String communityId;
    @SerializedName("communityType")
    private String communityType;
    @SerializedName("imageUrl")
    private String imageUrl;
    @SerializedName("userAvatar")
    private String userAvatar;
    @SerializedName("userName")
    private String userName;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCommunityId() {
        return communityId;
    }

    public void setCommunityId(String communityId) {
        this.communityId = communityId;
    }

    public String getCommunityType() {
        return communityType;
    }

    public void setCommunityType(String communityType) {
        this.communityType = communityType;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getUserAvatar() {
        return userAvatar;
    }

    public void setUserAvatar(String userAvatar) {
        this.userAvatar = userAvatar;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
