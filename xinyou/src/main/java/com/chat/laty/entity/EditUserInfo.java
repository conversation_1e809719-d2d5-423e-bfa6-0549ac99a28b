package com.chat.laty.entity;

import com.google.gson.annotations.SerializedName;

import org.greenrobot.greendao.annotation.Index;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/13 17:15
 * @description:
 */

public class EditUserInfo {

    @SerializedName("id")
    @Index(unique = true)
    private String id;
    @SerializedName("avatar")
    private String avatar;
    @SerializedName("nickname")
    private String nickname;
    @SerializedName("sex")
    private String sex;
    @SerializedName("invitationCode")
    private String invitationCode;
    @SerializedName("birthday")
    private String birthday;
    @SerializedName("liveAddress")
    private String liveAddress;
    @SerializedName("liveAddressCode")
    private String liveAddressCode;
    @SerializedName("userId")
    private String userId;
    @SerializedName("liveLatLng")
    private String liveLatLng;
    @SerializedName("isReal")
    private String isReal;
    @SerializedName("isName")
    private String isName;
    @SerializedName("isPhone")
    private String isPhone;
    @SerializedName("audioUrl")
    private String audioUrl;
    @SerializedName("audioLength")
    private String audioLength;
    @SerializedName("makeFriendsDetail")
    private String makeFriendsDetail;
    @SerializedName("height")
    private String height;
    @SerializedName("weight")
    private String weight;
    @SerializedName("educationName")
    private String educationName;
    @SerializedName("educationCode")
    private String educationCode;
    @SerializedName("careerName")
    private String careerName;
    @SerializedName("careerCode")
    private String careerCode;
    @SerializedName("incomeName")
    private String incomeName;
    @SerializedName("incomeCode")
    private String incomeCode;
    @SerializedName("impressionLabels")
    private String impressionLabels;
    @SerializedName("loveFriendLabels")
    private String loveFriendLabels;
    @SerializedName("emotionStatus")
    private String emotionStatus;
    @SerializedName("emotionStatusCode")
    private String emotionStatusCode;
    @SerializedName("liveStatus")
    private String liveStatus;
    @SerializedName("liveStatusCode")
    private String liveStatusCode;
    @SerializedName("photoList")
    private String photoList;
    @SerializedName("coverUrl")
    private String coverUrl;
    @SerializedName("age")
    private String age;
    @SerializedName("trendsFiles")
    private String trendsFiles;
    @SerializedName("onlineStatus")
    private String onlineStatus;
    @SerializedName("hideLocation")
    private String hideLocation;
    @SerializedName("hideCity")
    private String hideCity;
    @SerializedName("hideOnline")
    private String hideOnline;
    @SerializedName("videoDisturb")
    private String videoDisturb;
    @SerializedName("voiceDisturb")
    private String voiceDisturb;
    @SerializedName("manAccostId")
    private String manAccostId;
    @SerializedName("womanAccostId")
    private String womanAccostId;
    @SerializedName("auditList")
    private List<ShenHeInfo> auditList;

    public List<ShenHeInfo> getAuditList() {
        return auditList;
    }

    public void setAuditList(List<ShenHeInfo> auditList) {
        this.auditList = auditList;
    }


    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getAvatar() {
        return this.avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getNickname() {
        return this.nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getSex() {
        return this.sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getInvitationCode() {
        return this.invitationCode;
    }

    public void setInvitationCode(String invitationCode) {
        this.invitationCode = invitationCode;
    }

    public String getBirthday() {
        return this.birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public String getLiveAddress() {
        return this.liveAddress;
    }

    public void setLiveAddress(String liveAddress) {
        this.liveAddress = liveAddress;
    }

    public String getLiveAddressCode() {
        return this.liveAddressCode;
    }

    public void setLiveAddressCode(String liveAddressCode) {
        this.liveAddressCode = liveAddressCode;
    }

    public String getUserId() {
        return this.userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getLiveLatLng() {
        return this.liveLatLng;
    }

    public void setLiveLatLng(String liveLatLng) {
        this.liveLatLng = liveLatLng;
    }

    public String getIsReal() {
        return this.isReal;
    }

    public void setIsReal(String isReal) {
        this.isReal = isReal;
    }

    public String getIsName() {
        return this.isName;
    }

    public void setIsName(String isName) {
        this.isName = isName;
    }

    public String getIsPhone() {
        return this.isPhone;
    }

    public void setIsPhone(String isPhone) {
        this.isPhone = isPhone;
    }

    public String getAudioUrl() {
        return this.audioUrl;
    }

    public void setAudioUrl(String audioUrl) {
        this.audioUrl = audioUrl;
    }

    public String getAudioLength() {
        return this.audioLength;
    }

    public void setAudioLength(String audioLength) {
        this.audioLength = audioLength;
    }

    public String getMakeFriendsDetail() {
        return this.makeFriendsDetail;
    }

    public void setMakeFriendsDetail(String makeFriendsDetail) {
        this.makeFriendsDetail = makeFriendsDetail;
    }

    public String getHeight() {
        return this.height;
    }

    public void setHeight(String height) {
        this.height = height;
    }

    public String getWeight() {
        return this.weight;
    }

    public void setWeight(String weight) {
        this.weight = weight;
    }

    public String getEducationName() {
        return this.educationName;
    }

    public void setEducationName(String educationName) {
        this.educationName = educationName;
    }

    public String getEducationCode() {
        return this.educationCode;
    }

    public void setEducationCode(String educationCode) {
        this.educationCode = educationCode;
    }

    public String getCareerName() {
        return this.careerName;
    }

    public void setCareerName(String careerName) {
        this.careerName = careerName;
    }

    public String getCareerCode() {
        return this.careerCode;
    }

    public void setCareerCode(String careerCode) {
        this.careerCode = careerCode;
    }

    public String getIncomeName() {
        return this.incomeName;
    }

    public void setIncomeName(String incomeName) {
        this.incomeName = incomeName;
    }

    public String getIncomeCode() {
        return this.incomeCode;
    }

    public void setIncomeCode(String incomeCode) {
        this.incomeCode = incomeCode;
    }

    public String getImpressionLabels() {
        return this.impressionLabels;
    }

    public void setImpressionLabels(String impressionLabels) {
        this.impressionLabels = impressionLabels;
    }

    public String getLoveFriendLabels() {
        return this.loveFriendLabels;
    }

    public void setLoveFriendLabels(String loveFriendLabels) {
        this.loveFriendLabels = loveFriendLabels;
    }

    public String getEmotionStatus() {
        return this.emotionStatus;
    }

    public void setEmotionStatus(String emotionStatus) {
        this.emotionStatus = emotionStatus;
    }

    public String getEmotionStatusCode() {
        return this.emotionStatusCode;
    }

    public void setEmotionStatusCode(String emotionStatusCode) {
        this.emotionStatusCode = emotionStatusCode;
    }

    public String getLiveStatus() {
        return this.liveStatus;
    }

    public void setLiveStatus(String liveStatus) {
        this.liveStatus = liveStatus;
    }

    public String getLiveStatusCode() {
        return this.liveStatusCode;
    }

    public void setLiveStatusCode(String liveStatusCode) {
        this.liveStatusCode = liveStatusCode;
    }

    public String getPhotoList() {
        return this.photoList;
    }

    public void setPhotoList(String photoList) {
        this.photoList = photoList;
    }

    public String getCoverUrl() {
        return this.coverUrl;
    }

    public void setCoverUrl(String coverUrl) {
        this.coverUrl = coverUrl;
    }

    public String getAge() {
        return this.age;
    }

    public void setAge(String age) {
        this.age = age;
    }

    public String getTrendsFiles() {
        return this.trendsFiles;
    }

    public void setTrendsFiles(String trendsFiles) {
        this.trendsFiles = trendsFiles;
    }

    public String getOnlineStatus() {
        return this.onlineStatus;
    }

    public void setOnlineStatus(String onlineStatus) {
        this.onlineStatus = onlineStatus;
    }

    public String getHideLocation() {
        return this.hideLocation;
    }

    public void setHideLocation(String hideLocation) {
        this.hideLocation = hideLocation;
    }

    public String getHideCity() {
        return this.hideCity;
    }

    public void setHideCity(String hideCity) {
        this.hideCity = hideCity;
    }

    public String getHideOnline() {
        return this.hideOnline;
    }

    public void setHideOnline(String hideOnline) {
        this.hideOnline = hideOnline;
    }

    public String getVideoDisturb() {
        return this.videoDisturb;
    }

    public void setVideoDisturb(String videoDisturb) {
        this.videoDisturb = videoDisturb;
    }

    public String getVoiceDisturb() {
        return this.voiceDisturb;
    }

    public void setVoiceDisturb(String voiceDisturb) {
        this.voiceDisturb = voiceDisturb;
    }

    public String getManAccostId() {
        return this.manAccostId;
    }

    public void setManAccostId(String manAccostId) {
        this.manAccostId = manAccostId;
    }

    public String getWomanAccostId() {
        return this.womanAccostId;
    }

    public void setWomanAccostId(String womanAccostId) {
        this.womanAccostId = womanAccostId;
    }

    @Override
    public String toString() {
        return "EditUserInfo{" +
                "id='" + id + '\'' +
                ", avatar='" + avatar + '\'' +
                ", nickname='" + nickname + '\'' +
                ", sex='" + sex + '\'' +
                ", invitationCode='" + invitationCode + '\'' +
                ", birthday='" + birthday + '\'' +
                ", liveAddress='" + liveAddress + '\'' +
                ", liveAddressCode='" + liveAddressCode + '\'' +
                ", userId='" + userId + '\'' +
                ", liveLatLng='" + liveLatLng + '\'' +
                ", isReal='" + isReal + '\'' +
                ", isName='" + isName + '\'' +
                ", isPhone='" + isPhone + '\'' +
                ", audioUrl='" + audioUrl + '\'' +
                ", audioLength='" + audioLength + '\'' +
                ", makeFriendsDetail='" + makeFriendsDetail + '\'' +
                ", height='" + height + '\'' +
                ", weight='" + weight + '\'' +
                ", educationName='" + educationName + '\'' +
                ", educationCode='" + educationCode + '\'' +
                ", careerName='" + careerName + '\'' +
                ", careerCode='" + careerCode + '\'' +
                ", incomeName='" + incomeName + '\'' +
                ", incomeCode='" + incomeCode + '\'' +
                ", impressionLabels='" + impressionLabels + '\'' +
                ", loveFriendLabels='" + loveFriendLabels + '\'' +
                ", emotionStatus='" + emotionStatus + '\'' +
                ", emotionStatusCode='" + emotionStatusCode + '\'' +
                ", liveStatus='" + liveStatus + '\'' +
                ", liveStatusCode='" + liveStatusCode + '\'' +
                ", photoList='" + photoList + '\'' +
                ", coverUrl='" + coverUrl + '\'' +
                ", age='" + age + '\'' +
                ", trendsFiles='" + trendsFiles + '\'' +
                ", onlineStatus='" + onlineStatus + '\'' +
                ", hideLocation='" + hideLocation + '\'' +
                ", hideCity='" + hideCity + '\'' +
                ", hideOnline='" + hideOnline + '\'' +
                ", videoDisturb='" + videoDisturb + '\'' +
                ", voiceDisturb='" + voiceDisturb + '\'' +
                ", manAccostId='" + manAccostId + '\'' +
                ", womanAccostId='" + womanAccostId + '\'' +
                ", auditList=" + auditList +
                '}';
    }
}
