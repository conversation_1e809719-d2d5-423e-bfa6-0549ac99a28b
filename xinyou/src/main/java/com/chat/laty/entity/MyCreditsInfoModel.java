package com.chat.laty.entity;

import java.util.List;

public class MyCreditsInfoModel {

    private String integralBalance;
    private String todayIntegral;

    private List<MyCreditsItemModel> integralList;

    public String getIntegralBalance() {
        return integralBalance;
    }

    public void setIntegralBalance(String integralBalance) {
        this.integralBalance = integralBalance;
    }

    public String getTodayIntegral() {
        return todayIntegral;
    }

    public void setTodayIntegral(String todayIntegral) {
        this.todayIntegral = todayIntegral;
    }

    public List<MyCreditsItemModel> getIntegralList() {
        return integralList;
    }

    public void setIntegralList(List<MyCreditsItemModel> integralList) {
        this.integralList = integralList;
    }

    public static class MyCreditsItemModel {
        private String id;
        private int num;

        public boolean selected;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public int getNum() {
            return num;
        }

        public void setNum(int num) {
            this.num = num;
        }

        public boolean isSelected() {
            return selected;
        }

        public void setSelected(boolean selected) {
            this.selected = selected;
        }
    }
}
