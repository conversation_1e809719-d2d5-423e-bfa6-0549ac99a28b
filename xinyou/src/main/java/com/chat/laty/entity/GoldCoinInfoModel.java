package com.chat.laty.entity;

import java.util.ArrayList;
import java.util.List;

public class GoldCoinInfoModel {

    private String goldBalance;
    private String todayTotalGold;

    private List<GoldCoinTypeModel> goldList;
    private List<PayTypeModel> payTypeList;

    public String getGoldBalance() {
        return goldBalance;
    }

    public void setGoldBalance(String goldBalance) {
        this.goldBalance = goldBalance;
    }

    public String getTodayTotalGold() {
        return todayTotalGold;
    }

    public void setTodayTotalGold(String todayTotalGold) {
        this.todayTotalGold = todayTotalGold;
    }

    public List<GoldCoinTypeModel> getGoldList() {
        return goldList;
    }

    public void setGoldList(List<GoldCoinTypeModel> goldList) {
        this.goldList = goldList;
    }

    public List<PayTypeModel> getPayTypeList() {
        if (payTypeList == null) {
            payTypeList = new ArrayList<>();
        }
        return payTypeList;
    }

    public void setPayTypeList(List<PayTypeModel> payTypeList) {
        this.payTypeList = payTypeList;
    }

    public void addPayTypeList(List<PayTypeModel> payTypeList) {
        this.payTypeList.addAll(payTypeList);
    }

    public static class GoldCoinTypeModel {
        private String id;
        private int num;
        private float price;
        private int rewardPoints;

        public boolean selected;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public int getNum() {
            return num;
        }

        public void setNum(int num) {
            this.num = num;
        }

        public float getPrice() {
            return price;
        }

        public void setPrice(float price) {
            this.price = price;
        }

        public int getRewardPoints() {
            return rewardPoints;
        }

        public void setRewardPoints(int rewardPoints) {
            this.rewardPoints = rewardPoints;
        }
    }

}
