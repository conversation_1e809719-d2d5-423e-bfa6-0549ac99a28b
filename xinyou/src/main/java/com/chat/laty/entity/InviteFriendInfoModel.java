package com.chat.laty.entity;

public class InviteFriendInfoModel {

    private String userId;//用户id
    private String nickname;//昵称
    private String bindingDate;//邀请时间
    private String sex;//性别
    private String loginTime;//登录时间
    private String avatar;//头像地址
    private boolean isName;//是否实名


    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getBindingDate() {
        return bindingDate;
    }

    public void setBindingDate(String bindingDate) {
        this.bindingDate = bindingDate;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getLoginTime() {
        return loginTime;
    }

    public void setLoginTime(String loginTime) {
        this.loginTime = loginTime;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public boolean isName() {
        return isName;
    }

    public void setName(boolean name) {
        isName = name;
    }
}
