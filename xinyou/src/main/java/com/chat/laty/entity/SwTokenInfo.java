package com.chat.laty.entity;

import com.google.gson.annotations.SerializedName;

/**
 * <AUTHOR>
 * @date 2023/11/28 18:06
 * @description:
 */
public class SwTokenInfo {

    @SerializedName("token")
    private String token;
    @SerializedName("channelName")
    private String channelName;
    @SerializedName("swappID")
    private String swappID;

    public String getSwappID() {
        return swappID;
    }

    public void setSwappID(String swappID) {
        this.swappID = swappID;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getChannelName() {
        return channelName;
    }

    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }
}
