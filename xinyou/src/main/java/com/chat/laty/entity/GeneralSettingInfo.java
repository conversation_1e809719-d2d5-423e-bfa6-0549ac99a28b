package com.chat.laty.entity;

import com.google.gson.annotations.SerializedName;

/**
 * <AUTHOR>
 * @date 2023/12/25 15:33
 * @description:
 */
public class GeneralSettingInfo {

    @SerializedName("hideLocation")
    private Integer hideLocation;
    @SerializedName("hideCity")
    private Integer hideCity;
    @SerializedName("hideOnline")
    private Integer hideOnline;
    @SerializedName("videoDisturb")
    private Integer videoDisturb;
    @SerializedName("voiceDisturb")
    private Integer voiceDisturb;
    @SerializedName("manAccostId")
    private String manAccostId;

    public Integer getHideLocation() {
        return hideLocation;
    }

    public void setHideLocation(Integer hideLocation) {
        this.hideLocation = hideLocation;
    }

    public Integer getHideCity() {
        return hideCity;
    }

    public void setHideCity(Integer hideCity) {
        this.hideCity = hideCity;
    }

    public Integer getHideOnline() {
        return hideOnline;
    }

    public void setHideOnline(Integer hideOnline) {
        this.hideOnline = hideOnline;
    }

    public Integer getVideoDisturb() {
        return videoDisturb;
    }

    public void setVideoDisturb(Integer videoDisturb) {
        this.videoDisturb = videoDisturb;
    }

    public Integer getVoiceDisturb() {
        return voiceDisturb;
    }

    public void setVoiceDisturb(Integer voiceDisturb) {
        this.voiceDisturb = voiceDisturb;
    }

    public String getManAccostId() {
        return manAccostId;
    }

    public void setManAccostId(String manAccostId) {
        this.manAccostId = manAccostId;
    }
}
