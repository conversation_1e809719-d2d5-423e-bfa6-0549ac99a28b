package com.chat.laty.entity;

import com.google.gson.annotations.SerializedName;

/**
 * <AUTHOR>
 * @date 2024/1/16 22:19
 * @description:
 */
public class WithdrawalInfo {
    @SerializedName("id")
    private String id;
    @SerializedName("amount")
    private String amount;
    @SerializedName("exchangeDiamond")
    private String exchangeDiamond;

    public boolean selected;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public String getExchangeDiamond() {
        return exchangeDiamond;
    }

    public void setExchangeDiamond(String exchangeDiamond) {
        this.exchangeDiamond = exchangeDiamond;
    }
}
