package com.chat.laty.entity;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.List;

public class CommunityInfoModel implements Parcelable {

    public CommunityInfoModel() {

    }

    private String id;
    private String isReal; // 真人 1/0
    private String isLike;
    private String isAccost; // 是否已经搭讪
    private int likeNum; // 点赞数量
    private int mentsNum; // 评论数量
    private String userId;
    private String userName;
    private String avatar;
    private String age;
    private String sex; // 1 男 2 女
    private String createDate; // 发布时间
    private String videoImg; // 发布时间
    private String auditStatus; // 状态

    private String textContent; //
    private String year; //
    private String date; //
    private List<CommentInfoModel> commentList;
    public List<MediaFileModel> uploadImgInfos;

    private String fileUrl;

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(String auditStatus) {
        this.auditStatus = auditStatus;
    }

    protected CommunityInfoModel(Parcel in) {
        id = in.readString();
        isReal = in.readString();
        isLike = in.readString();
        isAccost = in.readString();
        likeNum = in.readInt();
        mentsNum = in.readInt();
        userId = in.readString();
        userName = in.readString();
        avatar = in.readString();
        age = in.readString();
        sex = in.readString();
        createDate = in.readString();
        videoImg = in.readString();
        textContent = in.readString();
        fileUrl = in.readString();
        auditStatus = in.readString();
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(id);
        dest.writeString(isReal);
        dest.writeString(isLike);
        dest.writeString(isAccost);
        dest.writeInt(likeNum);
        dest.writeInt(mentsNum);
        dest.writeString(userId);
        dest.writeString(userName);
        dest.writeString(avatar);
        dest.writeString(age);
        dest.writeString(sex);
        dest.writeString(createDate);
        dest.writeString(videoImg);
        dest.writeString(textContent);
        dest.writeString(fileUrl);
        dest.writeString(auditStatus);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<CommunityInfoModel> CREATOR = new Creator<CommunityInfoModel>() {
        @Override
        public CommunityInfoModel createFromParcel(Parcel in) {
            return new CommunityInfoModel(in);
        }

        @Override
        public CommunityInfoModel[] newArray(int size) {
            return new CommunityInfoModel[size];
        }
    };

    public String getVideoImg() {
        return videoImg;
    }

    public void setVideoImg(String videoImg) {
        this.videoImg = videoImg;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getIsReal() {
        return isReal;
    }

    public void setIsReal(String isReal) {
        this.isReal = isReal;
    }

    public String getIsLike() {
        return isLike;
    }

    public void setIsLike(String isLike) {
        this.isLike = isLike;
    }

    public String getIsAccost() {
        return isAccost;
    }

    public void setIsAccost(String isAccost) {
        this.isAccost = isAccost;
    }

    public int getLikeNum() {

        return likeNum;
    }

    public void setLikeNum(int likeNum) {
        this.likeNum = likeNum;
    }

    public int getMentsNum() {
        return mentsNum;
    }

    public void setMentsNum(int mentsNum) {
        this.mentsNum = mentsNum;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getAge() {
        if (age == null) {
            age = "";
        }
        return age;
    }

    public void setAge(String age) {
        this.age = age;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public String getTextContent() {
        return textContent;
    }

    public void setTextContent(String textContent) {
        this.textContent = textContent;
    }

    public List<CommentInfoModel> getCommentList() {
        return commentList;
    }

    public void setCommentList(List<CommentInfoModel> commentList) {
        this.commentList = commentList;
    }

    public static class CommentInfoModel {

        private String id;
        private String communityId;
        private String userName;
        private String parentUserName; // 被恢复人昵称
        private String content;

        private String avatar;
        private String createDate;

        public boolean selected;

        public String getId() {
            return id;
        }

        public String getAvatar() {
            return avatar;
        }

        public void setAvatar(String avatar) {
            this.avatar = avatar;
        }

        public String getCreateDate() {
            return createDate;
        }

        public void setCreateDate(String createDate) {
            this.createDate = createDate;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getCommunityId() {
            return communityId;
        }

        public void setCommunityId(String communityId) {
            this.communityId = communityId;
        }

        public String getUserName() {
            return userName;
        }

        public void setUserName(String userName) {
            this.userName = userName;
        }

        public String getParentUserName() {
            return parentUserName;
        }

        public void setParentUserName(String parentUserName) {
            this.parentUserName = parentUserName;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }


        @Override
        public String toString() {
            return "CommentInfoModel{" +
                    "id='" + id + '\'' +
                    ", communityId='" + communityId + '\'' +
                    ", userName='" + userName + '\'' +
                    ", parentUserName='" + parentUserName + '\'' +
                    ", content='" + content + '\'' +
                    ", avatar='" + avatar + '\'' +
                    ", createDate='" + createDate + '\'' +
                    ", selected=" + selected +
                    '}';
        }
    }

}
