package com.chat.laty.entity;

import com.google.gson.annotations.SerializedName;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/25 23:30
 * @description:
 */
public class AccostManLanguageInfo {

    @SerializedName("manAccostId")
    private String manAccostId;
    @SerializedName("accostList")
    private List<AccostLanguageInfo> accostList;

    public String getManAccostId() {
        return manAccostId;
    }

    public void setManAccostId(String manAccostId) {
        this.manAccostId = manAccostId;
    }

    public List<AccostLanguageInfo> getAccostList() {
        return accostList;
    }

    public void setAccostList(List<AccostLanguageInfo> accostList) {
        this.accostList = accostList;
    }
}
