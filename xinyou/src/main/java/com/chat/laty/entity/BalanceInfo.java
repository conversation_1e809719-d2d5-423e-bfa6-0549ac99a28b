package com.chat.laty.entity;

import com.google.gson.annotations.SerializedName;

import org.greenrobot.greendao.annotation.Entity;
import org.greenrobot.greendao.annotation.Generated;
import org.greenrobot.greendao.annotation.Index;

/**
 * <AUTHOR>
 * @date 2024/1/6 14:51
 * @description:
 */
@Entity
public class BalanceInfo {

    @SerializedName("goldNum")
    private Double goldNum;
    @SerializedName("integralNum")
    private Double integralNum;
    @SerializedName("freeTextNum")
    private Double freeTextNum;
    @SerializedName("freeVoiceNum")
    private Double freeVoiceNum;
    @SerializedName("freeVideoNum")
    private Double freeVideoNum;

    @Index(unique = true)
    private String userId;

    @Generated(hash = 1190663581)
    public BalanceInfo(Double goldNum, Double integralNum, Double freeTextNum,
            Double freeVoiceNum, Double freeVideoNum, String userId) {
        this.goldNum = goldNum;
        this.integralNum = integralNum;
        this.freeTextNum = freeTextNum;
        this.freeVoiceNum = freeVoiceNum;
        this.freeVideoNum = freeVideoNum;
        this.userId = userId;
    }

    @Generated(hash = 2066528285)
    public BalanceInfo() {
    }

    public Double getGoldNum() {
        return this.goldNum;
    }

    public void setGoldNum(Double goldNum) {
        this.goldNum = goldNum;
    }

    public Double getIntegralNum() {
        return this.integralNum;
    }

    public void setIntegralNum(Double integralNum) {
        this.integralNum = integralNum;
    }

    public Double getFreeTextNum() {
        return this.freeTextNum;
    }

    public void setFreeTextNum(Double freeTextNum) {
        this.freeTextNum = freeTextNum;
    }

    public Double getFreeVoiceNum() {
        return this.freeVoiceNum;
    }

    public void setFreeVoiceNum(Double freeVoiceNum) {
        this.freeVoiceNum = freeVoiceNum;
    }

    public Double getFreeVideoNum() {
        return this.freeVideoNum;
    }

    public void setFreeVideoNum(Double freeVideoNum) {
        this.freeVideoNum = freeVideoNum;
    }

    public String getUserId() {
        return this.userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    @Override
    public String toString() {
        return "BalanceInfo{" +
                "goldNum=" + goldNum +
                ", integralNum=" + integralNum +
                ", freeTextNum=" + freeTextNum +
                ", freeVoiceNum=" + freeVoiceNum +
                ", freeVideoNum=" + freeVideoNum +
                ", userId='" + userId + '\'' +
                '}';
    }
}
