package com.chat.laty.entity;

import com.google.gson.annotations.SerializedName;

/**
 * <AUTHOR>
 * @date 2023/11/28 11:36
 * @description:
 */
public class LoinEntity {

    @SerializedName("token")
    private String token;
    @SerializedName("loginUserVo")
    private RyUserInfo userInfo;

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public RyUserInfo getUserInfo() {
        return userInfo;
    }

    public void setUserInfo(RyUserInfo userInfo) {
        this.userInfo = userInfo;
    }
}
