package com.chat.laty.entity;

import com.google.gson.annotations.SerializedName;

import org.greenrobot.greendao.annotation.Entity;
import org.greenrobot.greendao.annotation.Generated;
import org.greenrobot.greendao.annotation.Index;

/**
 * <AUTHOR>
 * @date 2024/1/21 14:05
 * @description:
 */
@Entity
public class BaseUserInfo {

    @SerializedName("avatar")
    private String avatar;
    @SerializedName("nickname")
    private String nickname;
    @SerializedName("notes")
    private String notes;
    @SerializedName("userId")
    @Index(unique = true)
    private String userId;
    @Generated(hash = 334376380)
    public BaseUserInfo(String avatar, String nickname, String notes,
            String userId) {
        this.avatar = avatar;
        this.nickname = nickname;
        this.notes = notes;
        this.userId = userId;
    }
    @Generated(hash = 396840554)
    public BaseUserInfo() {
    }
    public String getAvatar() {
        return this.avatar;
    }
    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }
    public String getNickname() {
        return this.nickname;
    }
    public void setNickname(String nickname) {
        this.nickname = nickname;
    }
    public String getNotes() {
        return this.notes;
    }
    public void setNotes(String notes) {
        this.notes = notes;
    }
    public String getUserId() {
        return this.userId;
    }
    public void setUserId(String userId) {
        this.userId = userId;
    }


}
