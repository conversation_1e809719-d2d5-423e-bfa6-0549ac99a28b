package com.chat.laty.entity;

import android.text.TextUtils;

public class MediaItemModel {

    public MediaItemModel(String path, String mimeType) {
        this.path = path;
        this.mimeType = mimeType;
    }

    String path;
    public String url;
    public String thumbUrl;

    String mimeType;

    public MediaItemStatus status;

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getMimeType() {
        if (TextUtils.isEmpty(mimeType)) {
            mimeType = "image/jpeg";
        }
        return mimeType;
    }

    public void setMimeType(String mimeType) {
        this.mimeType = mimeType;
    }
}

