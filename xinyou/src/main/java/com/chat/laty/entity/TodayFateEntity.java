package com.chat.laty.entity;

import com.google.gson.annotations.SerializedName;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/28 16:08
 * @description:
 */
public class TodayFateEntity {
    @SerializedName("todayUserList")
    List<OtherUserInfo> todayUserList;

    @SerializedName("accostWomanList")
    List<AccostLanguageInfo> AccostLanguageInfo;

    @SerializedName("accostVoiceList")
    List<AccostLanguageInfo> accostVoiceList;
    @SerializedName("accostTextList")
    List<AccostLanguageInfo> accostTextList;

    public List<AccostLanguageInfo> getAccostTextList() {
        return accostTextList;
    }

    public void setAccostTextList(List<AccostLanguageInfo> accostTextList) {
        this.accostTextList = accostTextList;
    }

    public List<AccostLanguageInfo> getAccostVoiceList() {
        return accostVoiceList;
    }

    public void setAccostVoiceList(List<AccostLanguageInfo> accostVoiceList) {
        this.accostVoiceList = accostVoiceList;
    }

    public List<OtherUserInfo> getTodayUserList() {
        return todayUserList;
    }

    public void setTodayUserList(List<OtherUserInfo> todayUserList) {
        this.todayUserList = todayUserList;
    }

    public List<AccostLanguageInfo> getAccostLanguageInfo() {
        return AccostLanguageInfo;
    }

    public void setAccostLanguageInfo(List<AccostLanguageInfo> accostLanguageInfo) {
        AccostLanguageInfo = accostLanguageInfo;
    }
}
