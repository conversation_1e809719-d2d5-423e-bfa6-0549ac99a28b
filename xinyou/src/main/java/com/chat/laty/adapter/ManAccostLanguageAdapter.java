package com.chat.laty.adapter;

import android.content.Context;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.chat.laty.R;
import com.chat.laty.entity.AccostLanguageInfo;
import com.chat.laty.utils.LogUtil;
import com.chat.laty.utils.PicassoUtils;

/**
 * <AUTHOR>
 * @date 2023/11/21 15:27
 * @description:
 */
public class ManAccostLanguageAdapter extends BaseQuickAdapter<AccostLanguageInfo, QuickViewHolder> {

    private String mUserCurrentId;

    public ManAccostLanguageAdapter(String mUserCurrentId) {
        this.mUserCurrentId = mUserCurrentId;
    }

    @Override
    protected void onBindViewHolder(@NonNull QuickViewHolder viewHolder, int i, @Nullable AccostLanguageInfo s) {
        PicassoUtils.showImage(viewHolder.getView(R.id.accost_riv), s.getImgUrl());
//        viewHolder.setText(R.id.accost_content_tv, s.getTempName());
//        AppCompatTextView textView = viewHolder.getView(R.id.use_template_tv);
        viewHolder.setVisible(R.id.is_select, s.isSelect());
        LogUtil.e("mUserzCurrentId", "mUserCurrentId--->" + mUserCurrentId+" ");
//
//        viewHolder.setText(R.id.accost_language_play_tv, s.getVoiceTimeLength() + "s");
    }

    public void setUserChoiceId(String accostId) {
//        mUserCurrentId = accostId;
//        notifyDataSetChanged();
    }


    @NonNull
    @Override
    protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int i) {
        return new QuickViewHolder(R.layout.adapter_accost_language_choice_layout, viewGroup);
    }
}
