package com.chat.laty.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Color;
import android.text.TextUtils;
import android.util.Log;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.chat.laty.R;
import com.chat.laty.entity.VipInfoModel;

/**
 * <AUTHOR>
 * @date 2023/11/21 15:27
 * @description:礼物适配器
 */
public class VipCardAdapter extends BaseQuickAdapter<VipInfoModel.VipTypeModel, QuickViewHolder> {

    @SuppressLint("UseCompatLoadingForDrawables")
    @Override
    protected void onBindViewHolder(@NonNull QuickViewHolder helper, int i, @Nullable VipInfoModel.VipTypeModel item) {
//        PicassoUtils.showImage(helper.getView(R.id.iv_card), "");

        helper.setText(R.id.tv_name, item.getVipName());
        helper.setText(R.id.tv_time_limit, "(" + item.getTimeLength() + "天)");
        helper.setText(R.id.tv_status, TextUtils.equals("1", item.getIsOpen()) ? "已开通" : "暂未开通");
        helper.setText(R.id.tv_price, item.getPrice() + "");

        helper.setImageDrawable(R.id.iv_card, getContext().getDrawable(getVipCardId(item.getId())));
//        helper.setImageDrawable(R.id.iv_card_tag, getContext().getDrawable(getVipTabId(item.getId())));

//        int color = getContext().getColor(getVipColor(item.getId()));
        int color = Color.parseColor(getVipColors(item.getId()));
        Log.e("TAG", "onBindViewHolder: " + color + "   +id" + item.getId());
        helper.setTextColor(R.id.tv_name, color);
        helper.setTextColor(R.id.tv_time_limit, color);
        helper.setTextColor(R.id.tv_price, color);
        helper.setTextColor(R.id.tv_price_label, color);
        helper.setTextColor(R.id.tv_status, color);
        helper.setBackgroundColor(R.id.v_dot, color);
        helper.setTextColor(R.id.tv_dot_label, color);

    }

    @NonNull
    @Override
    protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int i) {
        return new QuickViewHolder(R.layout.adapter_vip_card_layout, viewGroup);
    }

    private int getVipTabId(String level) {
        switch (level) {
            case "1":
                return R.mipmap.vip_tag_1;
            case "2":
                return R.mipmap.vip_tag_2;
            case "3":
                return R.mipmap.vip_tag_3;
            case "4":
                return R.mipmap.vip_tag_4;
        }
        return R.mipmap.vip_tag_1;
    }


    private int getVipCardId(String level) {
        switch (level) {
            case "1":
                return R.mipmap.vip_card_1;
            case "2":
                return R.mipmap.vip_card_2;
            case "3":
                return R.mipmap.vip_card_3;
            case "4":
                return R.mipmap.vip_card_4;
        }
        return R.mipmap.vip_card_1;
    }

    private int getVipColor(String level) {
        switch (level) {
            case "1":
                return R.color.vip_color_1;
            case "2":
                return R.color.vip_color_2;
            case "3":
                return R.color.vip_color_3;
            case "4":
                return R.color.vip_color_4;
        }
        return R.color.vip_color_1;
    }

    private String getVipColors(String level) {
        switch (level) {
            case "1":
                return "#BB4F61";
            case "2":
                return "#B9732D";
            case "3":
                return "#6670EA";
            case "4":
                return "#BB4F61";
        }
        return "#613C29";
    }
}
