package com.chat.laty.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatTextView;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.chat.laty.R;
import com.chat.laty.entity.FriendInfo;
import com.chat.laty.utils.PicassoUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/21 15:27
 * @description:
 */
public class FriendAdapter extends BaseQuickAdapter<FriendInfo, QuickViewHolder> {
    private int type = 1;

    public FriendAdapter(int type) {
        this.type = type;
    }

    @Override
    protected void onBindViewHolder(@NonNull QuickViewHolder holder, int i, @Nullable FriendInfo friendInfo) {
        PicassoUtils.showImage(holder.getView(R.id.user_riv), friendInfo.getAvatar());
        holder.setText(R.id.user_name_tv, friendInfo.getNickname());
        holder.setGone(R.id.user_isreal_tv, (TextUtils.isEmpty(friendInfo.getIsReal()) || !"1".equals(friendInfo.getIsReal())));
        holder.setGone(R.id.user_isname_tv, (TextUtils.isEmpty(friendInfo.getIsName()) || !"1".equals(friendInfo.getIsName())));
//        holder.setGone(R.id.user_isphone_tv, (TextUtils.equals("2", friendInfo.getSex()) && (TextUtils.isEmpty(friendInfo.getIsPhone()) || !"1".equals(friendInfo.getIsPhone()))));

        AppCompatTextView isPhoneTv = holder.getView(R.id.user_isphone_tv);
        if (TextUtils.equals("1",friendInfo.getSex())) {
            isPhoneTv.setVisibility(View.VISIBLE);
            if ("1".equals(friendInfo.getIsPhone())) {
                isPhoneTv.setText("手机已认证");
            } else if ("0".equals(friendInfo.getIsPhone())) {
                isPhoneTv.setText("微信已认证");
            }
        } else {
            isPhoneTv.setVisibility(View.GONE);
        }

        AppCompatTextView followTv = holder.getView(R.id.follow_tv);
        if (!TextUtils.isEmpty(friendInfo.getIsFollow())) {
            if ("1".equals(friendInfo.getIsFollow())) {
                followTv.setText("取消关注");
                followTv.setBackgroundDrawable(getContext().getResources().getDrawable(R.drawable.badge_ff8c8c_to_ff5562_bg_40));
                followTv.setTextColor(getContext().getResources().getColor(R.color.color_white));
            } else {
                followTv.setText("立即关注");
                followTv.setTextColor(getContext().getResources().getColor(R.color.color_FF6D7A));
                followTv.setBackgroundDrawable(getContext().getResources().getDrawable(R.drawable.badge_ffecee_bg_line_ff6d7a_40));
            }
        }

//        holder.setBackgroundResource(R.id.rc_content, getContext().getResources().getDrawable(R.drawable.badge_half_tran_bg_10));

//        holder.setText(R.id.follow_tv, !TextUtils.isEmpty(friendInfo.getIsFollow()) && ("1".equals(friendInfo.getIsFollow())) ? "取关" : "关注");
        holder.setText(R.id.xindong_tv, friendInfo.getMindNum());

    }

    @NonNull
    @Override
    protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int i) {
        int layoutRes = R.layout.adapter_message_good_friend_layout;
        //页面类型(1-密友 2-好友 3-关注列表 4-粉丝列表),示例值(1)
        switch (i) {
            case 1:
                layoutRes = R.layout.adapter_message_close_friend_layout;
                break;
            case 2:
                layoutRes = R.layout.adapter_message_good_friend_layout;
                break;
            case 3:
                layoutRes = R.layout.adapter_message_follow_friend_layout;
                break;
            case 4:
                layoutRes = R.layout.adapter_message_fans_friend_layout;
                break;

        }
        return new QuickViewHolder(layoutRes, viewGroup);
    }

    @Override
    protected int getItemViewType(int position, @NonNull List<? extends FriendInfo> list) {
        return type;
    }
}
