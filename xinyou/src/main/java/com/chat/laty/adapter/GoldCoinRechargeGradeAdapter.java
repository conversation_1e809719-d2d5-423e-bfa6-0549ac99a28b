package com.chat.laty.adapter;

import android.content.Context;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatTextView;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.chat.laty.R;
import com.chat.laty.base.XYApplication;
import com.chat.laty.entity.RechargeGradeInfo;

/**
 * <AUTHOR>
 * @date 2023/11/21 15:27
 * @description:
 */
public class GoldCoinRechargeGradeAdapter extends BaseQuickAdapter<RechargeGradeInfo, QuickViewHolder> {

    private int mCurrentPosition = 0;

    public void setSelectItem(int position) {
        mCurrentPosition = position;
        notifyDataSetChanged();
    }

    @Override
    protected void onBindViewHolder(@NonNull QuickViewHolder helper, int i, @Nullable RechargeGradeInfo item) {
        helper.setText(R.id.species_tv, item.getSpecies());
        helper.setText(R.id.price_tv, item.getPrice());
        helper.setText(R.id.give_tv, item.getGive());

        AppCompatTextView speciesTv = helper.getView(R.id.species_tv);
        AppCompatTextView priceTv = helper.getView(R.id.price_tv);
        AppCompatTextView giveTv = helper.getView(R.id.give_tv);

        if (mCurrentPosition == helper.getLayoutPosition()) {
            speciesTv.setTextColor(XYApplication.getAppApplicationContext().getResources().getColor(R.color.srb_golden_stars));
            priceTv.setTextColor(XYApplication.getAppApplicationContext().getResources().getColor(R.color.srb_golden_stars));
            giveTv.setTextColor(XYApplication.getAppApplicationContext().getResources().getColor(R.color.srb_golden_stars));
        } else {
            speciesTv.setTextColor(XYApplication.getAppApplicationContext().getResources().getColor(R.color.black));
            priceTv.setTextColor(XYApplication.getAppApplicationContext().getResources().getColor(R.color.black));
            giveTv.setTextColor(XYApplication.getAppApplicationContext().getResources().getColor(R.color.black));
        }
    }

    @NonNull
    @Override
    protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int i) {
        return new QuickViewHolder(R.layout.adapter_recharge_grade_layout, viewGroup);
    }
}
