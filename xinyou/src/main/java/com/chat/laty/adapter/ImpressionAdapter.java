package com.chat.laty.adapter;

import android.content.Context;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.hjq.toast.Toaster;
import com.chat.laty.R;

/**
 * <AUTHOR>
 * @date 2023/11/21 15:27
 * @description:
 */
public class ImpressionAdapter extends BaseQuickAdapter<String, QuickViewHolder> {

    @Override
    protected void onBindViewHolder(@NonNull QuickViewHolder help, int i, @Nullable String item) {
        Toaster.show(item);
        help.setText(R.id.content_textview, item);
    }

    @NonNull
    @Override
    protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int i) {
        return new QuickViewHolder(R.layout.adapter_impression_layout, viewGroup);
    }
}
