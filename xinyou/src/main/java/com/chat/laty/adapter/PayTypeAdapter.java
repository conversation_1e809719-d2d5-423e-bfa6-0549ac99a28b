package com.chat.laty.adapter;

import android.content.Context;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.chat.laty.R;
import com.chat.laty.entity.PayTypeModel;

/**
 * <AUTHOR>
 * @date 2023/11/21 15:27
 * @description:礼物适配器
 */
public class PayTypeAdapter extends BaseQuickAdapter<PayTypeModel, QuickViewHolder> {

    @Override
    protected void onBindViewHolder(@NonNull QuickViewHolder helper, int i, @Nullable PayTypeModel item) {
        helper.itemView.setSelected(item.selected);
        helper.setImageDrawable(R.id.iv_icon, getContext().getDrawable(item.getIcon()));
//        PicassoUtils.showImage(helper.getView(R.id.iv_icon), item.getIcon());
        helper.setText(R.id.tv_title, item.getText());

        if (item.selected) {
            helper.setImageResource(R.id.iv_selected, R.mipmap.ic_pay_selected);
        } else {
            helper.setImageResource(R.id.iv_selected, 0);
        }
    }

    @NonNull
    @Override
    protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int i) {
        return new QuickViewHolder(R.layout.adapter_vip_pay_type_layout, viewGroup);
    }
}
