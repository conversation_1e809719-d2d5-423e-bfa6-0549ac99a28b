package com.chat.laty.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.chat.laty.R;
import com.chat.laty.entity.SiMiUploadBean1;
import com.chat.laty.utils.PicassoUtils;

/**
 * <AUTHOR>
 * @date 2023/11/21 15:27
 * @description:
 */
public class AddSiMiAdapter extends BaseQuickAdapter<SiMiUploadBean1, QuickViewHolder> {
    private boolean showDelete = false;

    @Override
    protected void onBindViewHolder(@NonNull QuickViewHolder viewHolder, int i, @Nullable SiMiUploadBean1 s) {
        PicassoUtils.showImageWithGlide(getContext(), viewHolder.getView(R.id.iv_thum), s.getImageUrl(),0);

        viewHolder.setGone(R.id.iv_thum, TextUtils.isEmpty(s.getImageUrl()));
        viewHolder.setGone(R.id.add_layout, !TextUtils.isEmpty(s.getImageUrl()));
        viewHolder.setGone(R.id.delete_layout,  TextUtils.isEmpty(s.getImageUrl()));
    }

    @NonNull
    @Override
    protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int i) {
        return new QuickViewHolder(R.layout.adapter_addsimi_img, viewGroup);
    }

    public void showDelete(boolean b) {
        this.showDelete = b;
        notifyDataSetChanged();
    }
}
