package com.chat.laty.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.hjq.toast.Toaster;
import com.chat.laty.R;
import com.chat.laty.base.BaseActivity;
import com.chat.laty.dialog.BottomChooseTypeDialog;
import com.chat.laty.entity.MediaItemModel;
import com.chat.laty.entity.MediaItemStatus;
import com.chat.laty.utils.PicassoUtils;

import java.util.ArrayList;
import java.util.List;

public class MediaPickerAdapter extends RecyclerView.Adapter<MediaPickerAdapter.Holder> {


    static int VIEW_TYPE_NORMAL = 0;
    static int VIEW_TYPE_ADD = 1;

    private static final int max_image = 9;
    private static final int max_video = 1;
    private static final int max = 9;

    private boolean mixtureMode = false;

    ArrayList<MediaItemModel> list = new ArrayList<>();

    public void setList(List<MediaItemModel> list) {
        this.list.addAll(list);
        notifyDataSetChanged();
    }

    public void clear() {
        this.list.clear();
        notifyDataSetChanged();
    }

    public List<MediaItemModel> getList() {
        return list;
    }

    BaseActivity activity;

    public MediaPickerAdapter(BaseActivity activity) {
        this.activity = activity;
    }

    public MediaPickerAdapter(BaseActivity activity, boolean mixtureMode) {
        this.activity = activity;
        this.mixtureMode = mixtureMode;
    }

    @NonNull
    @Override
    public Holder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.media_picker_item, parent, false);
        return new Holder(view);
    }


    @Override
    public void onBindViewHolder(@NonNull Holder holder, int position) {
        if (getItemViewType(position) != VIEW_TYPE_NORMAL) {
            holder.add.setVisibility(View.VISIBLE);
            holder.add.setOnClickListener(v -> showTypeDialog());
            holder.content.setVisibility(View.GONE);
            holder.delete.setVisibility(View.GONE);
            holder.player.setVisibility(View.GONE);
            holder.loading.setVisibility(View.GONE);
            holder.status.setVisibility(View.GONE);
            return;
        }
        MediaItemModel item = list.get(position);
        PicassoUtils.showImageWithGlide(holder.itemView.getContext(), holder.content, item.getPath(), 4);
        holder.add.setVisibility(View.GONE);
        holder.loading.setVisibility(item.status == MediaItemStatus.compress || item.status == MediaItemStatus.uploading ? View.VISIBLE : View.GONE);
        holder.player.setVisibility(item.getMimeType().contains("video/") ? View.VISIBLE : View.GONE);
        holder.status.setVisibility(item.status == MediaItemStatus.fail || item.status == MediaItemStatus.compress ? View.VISIBLE : View.GONE);
        holder.status.setText(item.status == MediaItemStatus.compress ? "资源\n压缩" : "上传\n失败");

        holder.delete.setOnClickListener(v -> {
            list.remove(item);
            notifyItemRemoved(position);
            notifyItemChanged(position);
        });
    }


    private void showTypeDialog() {

        if (mixtureMode) {
            if (list.size() >= max) {
                Toaster.show("最多可上传" + max + "个图片/视频");
                return;
            }
            BottomChooseTypeDialog dialog = new BottomChooseTypeDialog(activity);
            dialog.setOnDialogCallbackListener(type -> {
                int m = max - list.size();
                if (type == 0) {
                    activity.choicePic(m);
                } else if (type == 1) {
                    activity.choiceVideo(m);
                }
            });
            dialog.show();
        } else {
            if (list.isEmpty()) {
                BottomChooseTypeDialog dialog = new BottomChooseTypeDialog(activity);
                dialog.setOnDialogCallbackListener(type -> {
                    if (type == 0) {
                        activity.choicePic(max_image);
                    } else if (type == 1) {
                        activity.choiceVideo(max_video);
                    }
                });
                dialog.show();
                return;
            }
            if (list.get(0).getMimeType().contains("video")) {
                if (max_video == list.size()) {
                    Toaster.show("最多可上传" + max_video + "个视频");
                    return;
                }
                activity.choiceVideo(max_video - list.size());
            } else {
                if (max_image == list.size()) {
                    Toaster.show("最多可上传" + max_image + "个图片");
                    return;
                }
                activity.choicePic(max_image - list.size());
            }
        }
    }

    @Override
    public int getItemCount() {

        int m = max_image;

        if (mixtureMode) {
            m = max;
        } else {
            if (!list.isEmpty() && list.get(0).getMimeType().contains("video")) {
                m = max_video;
            }
        }
        return list.size() >= m ? m : list.size() + 1;
    }

    @Override
    public int getItemViewType(int position) {
        if (position > list.size() - 1) {
            return VIEW_TYPE_ADD;
        }
        return VIEW_TYPE_NORMAL;
    }

    static class Holder extends RecyclerView.ViewHolder {

        ImageView content;
        TextView status;
        View loading;
        View player;
        View delete;

        View add;

        public Holder(@NonNull View itemView) {
            super(itemView);
            content = itemView.findViewById(R.id.iv_content);
            status = itemView.findViewById(R.id.tv_state);
            loading = itemView.findViewById(R.id.pgr_uploading);
            player = itemView.findViewById(R.id.iv_player);
            delete = itemView.findViewById(R.id.iv_delete);
            add = itemView.findViewById(R.id.iv_add);
        }
    }
}
