package com.chat.laty.adapter;

import android.content.Context;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.chat.laty.R;
import com.chat.laty.entity.GiftInfo;
import com.chat.laty.utils.PicassoUtils;

/**
 * <AUTHOR>
 * @date 2023/11/21 15:27
 * @description:礼物适配器
 */
public class GiftAdapter extends BaseQuickAdapter<GiftInfo, QuickViewHolder> {

    private int showType = 1;
    private int mSelectPosition = -1;

    @Override
    protected void onBindViewHolder(@NonNull QuickViewHolder helper, int i, @Nullable GiftInfo item) {
        PicassoUtils.showImage(helper.getView(R.id.gift_image), item.getStaticImg());
        helper.setText(R.id.channel_title, item.getName());
        helper.setText(R.id.price_tv, (showType == 1) ? "金币:" + item.getPrice() : "积分:" + item.getIntegralPrice());
        helper.setBackgroundResource(R.id.select_layout,(helper.getLayoutPosition() == mSelectPosition)?R.drawable.bg_amount_layout:0);
    }

    @NonNull
    @Override
    protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int i) {
        return new QuickViewHolder(R.layout.adapter_gift_layout, viewGroup);
    }

    public void setShowType(int type) {
        this.showType = type;
        notifyDataSetChanged();
    }
    public void setSelectPosition(int positioon) {
        this.mSelectPosition = positioon;
        notifyDataSetChanged();
    }
}
