package com.chat.laty.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.chat.laty.R;
import com.chat.laty.activity.DischargedPicBrowserActivity;
import com.chat.laty.entity.DiaplayPicInfo;
import com.chat.laty.entity.ReportMessageInfo;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/21 15:27
 * @description:
 */
public class ReportMsgAdapter extends BaseQuickAdapter<ReportMessageInfo, QuickViewHolder> {

    @Override
    protected void onBindViewHolder(@NonNull QuickViewHolder help, int i, @Nullable ReportMessageInfo s) {
//        help.setText(R.id.title_tv, s.getUserName());
//        help.setText(R.id.content_tv, s.getReportDetail());
        help.setText(R.id.tv_year, s.getYear());
        help.setText(R.id.tv_date, s.getDay());
        help.setText(R.id.report_user_id_tv, "举报人ID:" + s.getUserId());
        help.setText(R.id.report_user_name_tv, "举报人昵称:" + s.getUserName());
        help.setText(R.id.report_resen_tv, "举报原因:" + s.getReportType());
        help.setText(R.id.report_desc_tv, "补充说明:" + s.getReportDetail());
        RecyclerView imgRv = help.getView(R.id.rv_images);

        List<String> urls = new ArrayList<>();
        if (!TextUtils.isEmpty(s.getFileUrl())) {
            urls = Arrays.asList(s.getFileUrl().split(","));
        }
        PicRvAdapter picAd = new PicRvAdapter(urls);
        imgRv.setAdapter(picAd);

        List<String> finalUrls = urls;
        picAd.setOnItemClickListener((baseQuickAdapter, view, i1) -> {
            ArrayList<DiaplayPicInfo> mDiaplayPicInfos = new ArrayList<>();
            for (String picurl : finalUrls) {
                mDiaplayPicInfos.add(new DiaplayPicInfo(picurl));
            }
            DischargedPicBrowserActivity.start(getContext(), mDiaplayPicInfos, i1);
        });

//        NineGridImageView NineGridView = help.getView(R.id.layout_nine_grid);
//
//        List<String> urls = new ArrayList<>();
//        if (!TextUtils.isEmpty(s.getFileUrl())) {
//            urls = Arrays.asList(s.getFileUrl().split(","));
//        }
//        NineGridView.setUrlList(urls);
    }

    @NonNull
    @Override
    protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int i) {
        return new QuickViewHolder(R.layout.adapter_report_message_layout, viewGroup);
    }
}
