package com.chat.laty.adapter;

import android.content.Context;
import android.content.SharedPreferences;
import android.graphics.Color;
import android.text.TextUtils;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.chat.laty.R;
import com.chat.laty.base.XYApplication;
import com.chat.laty.entity.OtherUserInfo;
import com.chat.laty.utils.PicassoUtils;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2023/11/21 15:27
 * @description:
 */
public class RecommendAdapter extends BaseQuickAdapter<OtherUserInfo, QuickViewHolder> {

    @Override
    protected void onBindViewHolder(@NonNull QuickViewHolder help, int i, @Nullable OtherUserInfo userInfo) {

        help.setText(R.id.user_name_tv, userInfo.getNickname());
        StringBuilder stringBuilder = new StringBuilder();

        if (!TextUtils.isEmpty(userInfo.getAge())) {
            help.setText(R.id.user_age_tv, userInfo.getAge());
            help.setGone(R.id.user_age_tv, true);

            TextView tvAge = help.getView(R.id.user_age_tv);
            if (userInfo.getSex().equals("2")) {
                tvAge.setCompoundDrawablesWithIntrinsicBounds(R.mipmap.ic_age1, 0, 0, 0);
            } else {
                tvAge.setCompoundDrawablesWithIntrinsicBounds(R.mipmap.ic_age1, 0, 0, 0);
            }

        } else {
            help.setGone(R.id.user_age_tv, false);
        }

        if (!TextUtils.isEmpty(userInfo.getHeight())) {
            help.setText(R.id.user_height, userInfo.getHeight());
            help.setGone(R.id.user_height, true);
        } else {
            help.setGone(R.id.user_height, false);
        }
        if (!TextUtils.isEmpty(userInfo.getCareerName())) {
            help.setText(R.id.user_career_name, userInfo.getCareerName());
            help.setGone(R.id.user_career_name, true);
        } else {
            help.setGone(R.id.user_career_name, false);
        }


//        help.setText(R.id.user_age_tv, stringBuilder.toString());
        help.setGone(R.id.online_view, "0".equals(userInfo.getOnlineStatus()));

        if (getStatusFromCache()) {
            help.setGone(R.id.accost_button, true);
//            help.setGone(R.id.chat_button, true);
        } else {
//            help.setGone(R.id.chat_button, "0".equals(userInfo.getIsAccost()));
            help.setGone(R.id.accost_button, "1".equals(userInfo.getIsAccost()));
        }

        help.setGone(R.id.user_isname_tv, "0".equals(userInfo.getIsReal()));

        PicassoUtils.showImage(help.getView(R.id.user_riv), userInfo.getAvatar());

        help.setGone(R.id.usericon_rv, TextUtils.isEmpty(userInfo.getTrendsFiles()));
        help.setGone(R.id.video_layout, TextUtils.isEmpty(userInfo.getTrendsFiles()));

        if (TextUtils.equals("2", userInfo.getTrendsType())) {
            PicassoUtils.showImage(help.getView(R.id.image_view), userInfo.getTrendsFiles());
            help.setGone(R.id.video_layout, false);
            help.setGone(R.id.usericon_rv, true);
        } else if (TextUtils.equals("1", userInfo.getTrendsType())) {
            help.setGone(R.id.usericon_rv, false);
            help.setGone(R.id.video_layout, true);
            if (!TextUtils.isEmpty(userInfo.getTrendsFiles())) {
                help.setGone(R.id.frient_xuan_tv, true);
                RecyclerView userIconRv = help.getView(R.id.usericon_rv);
                UserIconAdapter iconAdapter = new UserIconAdapter();
                userIconRv.setAdapter(iconAdapter);
                iconAdapter.setItems(Arrays.asList(userInfo.getTrendsFiles().split(",")));
            } else {
                if (!TextUtils.isEmpty(userInfo.getMakeFriendsDetail())) {
                    help.setText(R.id.frient_xuan_tv, userInfo.getMakeFriendsDetail());
                }
                help.setGone(R.id.frient_xuan_tv, TextUtils.isEmpty(userInfo.getMakeFriendsDetail()));
            }
        }

        if (userInfo.getVipLevel() == 0) {
            help.setGone(R.id.vip_tv, true);
            help.setGone(R.id.iv_txk, true);
            help.setTextColor(R.id.user_name_tv, Color.parseColor("#000000"));
        } else {
            Glide.with(getContext()).load(XYApplication.vipTypeModelList.get(userInfo.getVipLevel() - 1).getExclusiveIdentifier()).into((ImageView) help.getView(R.id.vip_tv));
            Glide.with(getContext()).load(XYApplication.vipTypeModelList.get(userInfo.getVipLevel() - 1).getExclusiveBorder()).into((ImageView) help.getView(R.id.iv_txk));
//            PicassoUtils.showImage(help.getView(R.id.vip_tv), XYApplication.vipTypeModelList.get(userInfo.getVipLevel()).getExclusiveIdentifier());
//            PicassoUtils.showImage(help.getView(R.id.iv_txk), XYApplication.vipTypeModelList.get(userInfo.getVipLevel()).getExclusiveBorder());
            help.setGone(R.id.vip_tv, false);
            help.setGone(R.id.iv_txk, false);
            help.setTextColor(R.id.user_name_tv, Color.parseColor(XYApplication.vipTypeModelList.get(userInfo.getVipLevel() - 1).getExclusiveNameColor()));
        }
    }

    @NonNull
    @Override
    protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int i) {
        return new QuickViewHolder(R.layout.adapter_recommend_layout, viewGroup);
    }

    /**
     * 从缓存中获取状态
     *
     * @return
     */
    private boolean getStatusFromCache() {
        SharedPreferences sharedPreferences = XYApplication.getAppApplicationContext().getSharedPreferences("app_prefs", Context.MODE_PRIVATE);
        return sharedPreferences.getBoolean("qsnms_status", false); // 默认值为 false
    }
}
