package com.chat.laty.adapter;

import android.content.Context;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.chat.laty.R;
import com.chat.laty.entity.GoldCoinInfoModel;

/**
 * <AUTHOR>
 * @date 2023/11/21 15:27
 * @description:礼物适配器
 */
public class GoldCoinAdapter extends BaseQuickAdapter<GoldCoinInfoModel.GoldCoinTypeModel, QuickViewHolder> {

    @Override
    protected void onBindViewHolder(@NonNull QuickViewHolder helper, int i, @Nullable GoldCoinInfoModel.GoldCoinTypeModel item) {

        helper.setText(R.id.tv_coin, "" + item.getNum());
        helper.setText(R.id.tv_price, "¥" + item.getPrice());
        helper.setText(R.id.tv_jf, "赠：" + item.getRewardPoints() + "积分");
        helper.itemView.setSelected(item.selected);

        helper.getView(R.id.ll_bg).setSelected(item.selected);
        helper.getView(R.id.tv_0).setSelected(item.selected);
        helper.getView(R.id.tv_jf).setSelected(item.selected);
        helper.getView(R.id.tv_coin).setSelected(item.selected);
        helper.getView(R.id.tv_price).setSelected(item.selected);
    }

    @NonNull
    @Override
    protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int i) {
        return new QuickViewHolder(R.layout.adapter_gold_coin_layout, viewGroup);
    }
}
