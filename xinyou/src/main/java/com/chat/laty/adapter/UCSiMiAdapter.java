package com.chat.laty.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.chat.laty.R;
import com.chat.laty.utils.PicassoUtils;

/**
 * <AUTHOR>
 * @date 2023/11/21 15:27
 * @description:
 */
public class UCSiMiAdapter extends BaseQuickAdapter<String, QuickViewHolder> {

    @Override
    protected void onBindViewHolder(@NonNull QuickViewHolder viewHolder, int i, @Nullable String s) {
        PicassoUtils.showImageWithGlide(getContext(),viewHolder.getView(R.id.iv_thum), s,0);
        viewHolder.setGone(R.id.iv_thum, TextUtils.isEmpty(s));
    }

    @NonNull
    @Override
    protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int i) {
        return new QuickViewHolder(R.layout.adapter_ucsimi_img, viewGroup);
    }
}
