package com.chat.laty.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.CompoundButton;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatTextView;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.chat.laty.R;
import com.chat.laty.entity.FriendInfo;
import com.chat.laty.utils.PicassoUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/11/21 15:27
 * @description:
 */
public class FriendChoiceAdapter extends BaseQuickAdapter<FriendInfo, QuickViewHolder> {

    List<FriendInfo> mFriends;
    // 存储勾选框状态的map集合
    private Map<Integer, Boolean> map = new HashMap<>();

    public FriendChoiceAdapter(List<FriendInfo> friends) {
        mFriends = friends;
        initMap();

    }

    @SuppressLint("RecyclerView")
    @Override
    protected void onBindViewHolder(@NonNull QuickViewHolder holder, int i, @Nullable FriendInfo friendInfo) {
        PicassoUtils.showImage(holder.getView(R.id.user_riv), friendInfo.getAvatar());
        holder.setText(R.id.user_name_tv, friendInfo.getNickname());
        holder.setGone(R.id.user_isreal_tv, (TextUtils.isEmpty(friendInfo.getIsReal()) || !"1".equals(friendInfo.getIsReal())));
        holder.setGone(R.id.user_isname_tv, (TextUtils.isEmpty(friendInfo.getIsName()) || !"1".equals(friendInfo.getIsName())));
//        holder.setGone(R.id.user_isphone_tv, (TextUtils.isEmpty(friendInfo.getIsPhone()) || !"1".equals(friendInfo.getIsPhone())));

        holder.setText(R.id.follow_tv, !TextUtils.isEmpty(friendInfo.getIsFollow()) && ("1".equals(friendInfo.getIsFollow())) ? "取关" : "关注");
        holder.setText(R.id.xindong_tv, friendInfo.getMindNum());

        AppCompatTextView isPhoneTv = holder.getView(R.id.user_isphone_tv);

        if (TextUtils.equals("1",friendInfo.getSex())) {
            isPhoneTv.setVisibility(View.VISIBLE);
            if ("1".equals(friendInfo.getIsPhone())) {
                isPhoneTv.setText("手机已认证");
            } else if ("0".equals(friendInfo.getIsPhone())) {
                isPhoneTv.setText("微信已认证");
            }
        } else {
            isPhoneTv.setVisibility(View.GONE);
        }

        CheckBox checkBox = holder.getView(R.id.cb);

        //设置checkBox改变监听
        checkBox.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {

            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                //用map集合保存
                map.put(i, isChecked);
            }
        });
        checkBox.setChecked(map.get(i));
    }

    //返回集合给MainActivity
    public Map<Integer, Boolean> getMap() {
        return map;
    }

    @NonNull
    @Override
    protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int i) {
        return new QuickViewHolder(R.layout.adapter_choice_good_friend_layout, viewGroup);
    }

    private void initMap() {
        for (int i = 0; i < mFriends.size(); i++) {
            map.put(i, i < 20);
        }
    }
}
