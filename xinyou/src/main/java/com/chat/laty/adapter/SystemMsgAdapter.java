package com.chat.laty.adapter;

import android.content.Context;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.chat.laty.R;
import com.chat.laty.entity.SystemMessageInfo;

/**
 * <AUTHOR>
 * @date 2023/11/21 15:27
 * @description:
 */
public class SystemMsgAdapter extends BaseQuickAdapter<SystemMessageInfo, QuickViewHolder> {

    @Override
    protected void onBindViewHolder(@NonNull QuickViewHolder help, int i, @Nullable SystemMessageInfo s) {
        help.setText(R.id.title_tv,s.getMessage());
        help.setText(R.id.content_tv,s.getCreateDate());
    }

    @NonNull
    @Override
    protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int i) {
        return new QuickViewHolder(R.layout.adapter_system_message_layout, viewGroup);
    }
}
