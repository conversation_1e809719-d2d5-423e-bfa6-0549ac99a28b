package com.chat.laty.adapter;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.alibaba.fastjson.JSONArray;
import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.chat.laty.R;
import com.chat.laty.entity.CommunityInfoModel;
import com.chat.laty.entity.MediaFileModel;
import com.chat.laty.utils.PicassoUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/21 15:27
 * @description:
 */
public class CommunityAdapter extends BaseQuickAdapter<CommunityInfoModel, QuickViewHolder> {

    String avatar = "";
    int type;

    private OnItemCommentCallback callback;

    public CommunityAdapter(int type) {
        this.type = type;
    }

    public void setAvatar(String avatar) {
        if (avatar == null) {
            avatar = "";
        }
        this.avatar = avatar;
    }

    public void setCallback(OnItemCommentCallback callback) {
        this.callback = callback;
    }

    @Override
    protected void onBindViewHolder(@NonNull QuickViewHolder helper, int position, @Nullable CommunityInfoModel item) {

        helper.setVisible(R.id.v_input_wrap, false);

        boolean isWoMen = TextUtils.equals("2", item.getSex());

        helper.setText(R.id.tv_user_name, item.getUserName());
        helper.setVisible(R.id.v_real_name, TextUtils.equals("1", item.getIsReal()));
        helper.setVisible(R.id.v_phone, !isWoMen);

        Drawable drawable = isWoMen ?
                ContextCompat.getDrawable(getContext(), R.drawable.baseline_female_24) :
                ContextCompat.getDrawable(getContext(), R.drawable.baseline_male_24);
        helper.setImageDrawable(R.id.iv_sex, drawable);

        int dgRes = isWoMen ? R.drawable.bg_border_primary_color_40 : R.drawable.bg_border_5595ff_40;
        helper.setBackgroundResource(R.id.v_sex, dgRes);

        helper.setText(R.id.tv_age, item.getAge());


//        boolean isAccost = TextUtils.equals("1", item.getIsAccost());
//        helper.setVisible(R.id.tv_chat, isAccost);
//        helper.setVisible(R.id.tv_accost, !isAccost);

        helper.setText(R.id.tv_user_info, item.getTextContent());
        helper.setVisible(R.id.tv_user_info, !TextUtils.isEmpty(item.getTextContent()));

        helper.setText(R.id.tv_time_info, item.getCreateDate());

        int color = ContextCompat.getColor(getContext(), TextUtils.equals("1", item.getIsLike()) ? R.color.colorPrimary : R.color.color_9d9d9d);
        helper.setTextColor(R.id.tv_collect_num, color);

        helper.setText(R.id.tv_collect_num, item.getLikeNum() > 0 ? item.getLikeNum() + "" : "点赞");

        helper.setText(R.id.tv_chat_num, "" + item.getMentsNum());


        PicassoUtils.showImage(helper.getView(R.id.iv_avatar), item.getAvatar());
        PicassoUtils.showImage(helper.getView(R.id.iv_avatar_v2), avatar);

        // --------------------

        if (item.uploadImgInfos == null) {
            item.uploadImgInfos = new ArrayList<>();
        }
        if (item.uploadImgInfos.isEmpty()) {
            if (TextUtils.isEmpty(item.getVideoImg())) {
                List<MediaFileModel> list = JSONArray.parseArray(item.getFileUrl(), MediaFileModel.class);
                if (list == null) {
                    list = new ArrayList<>();
                }
                item.uploadImgInfos = list;
            } else {
                MediaFileModel info = new MediaFileModel();
                info.setImgUrl(item.getFileUrl());
                info.setTumhImgUrl(item.getVideoImg());
                item.uploadImgInfos.add(info);
            }
        }

        RecyclerView recycler = helper.getView(R.id.nine_grid);

        if (item.uploadImgInfos.isEmpty()) {
            recycler.removeAllViews();
            recycler.setAdapter(null);
        } else {

            final List<MediaFileModel> li = type == 2 ? item.uploadImgInfos.subList(0, 1) : item.uploadImgInfos;

            GridLayoutManager manager = new GridLayoutManager(getContext(), li.size() > 2 ? 3 : li.size());
            recycler.setLayoutManager(manager);
            recycler.setNestedScrollingEnabled(false);

            NineGridViewAdapter nineGridViewAdapter = new NineGridViewAdapter(getContext());
            nineGridViewAdapter.setOnItemClickListener((baseQuickAdapter, view, i) -> {
                MediaFileModel model = li.get(i);
                nineGridViewAdapter.onClickListener(getContext(), model, li);
            });
            nineGridViewAdapter.setList(li);

            recycler.setAdapter(nineGridViewAdapter);
        }

        // ---------------------

        RecyclerView recyclerView = helper.getView(R.id.rv_records);

        if (item.getCommentList().isEmpty()) {
            recyclerView.removeAllViews();
            recyclerView.setAdapter(null);
            recyclerView.setVisibility(View.GONE);
            return;
        }
        helper.setVisible(R.id.rv_records, false);

        CommentAdapter adapter = new CommentAdapter();
        adapter.addOnItemChildClickListener(R.id.iv_close, (baseQuickAdapter, view, i) -> {
            if (callback != null) {
                callback.delete(item, position, baseQuickAdapter.getItem(i));
            }
        });

        adapter.setOnItemClickListener((baseQuickAdapter, view, i) -> {
            CommunityInfoModel.CommentInfoModel it = baseQuickAdapter.getItem(i);

            if (callback != null) {
                callback.replay(item, position, baseQuickAdapter.getItem(i));
            }
            if (true) return; // 支持删除评论

            if (TextUtils.isEmpty(it.getParentUserName())) {
                it.selected = !it.selected;
                baseQuickAdapter.notifyItemChanged(i);
            } else {
                if (callback != null) {
                    callback.replay(item, position, baseQuickAdapter.getItem(i));
                }
            }
        });
        adapter.setItems(item.getCommentList());
        recyclerView.setAdapter(adapter);
    }

    @NonNull
    @Override
    protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int i) {
        return new QuickViewHolder(R.layout.adapter_community_layout, viewGroup);
    }

    public interface OnItemCommentCallback {
        void delete(CommunityInfoModel model, int index1, CommunityInfoModel.CommentInfoModel data);

        void replay(CommunityInfoModel model, int index1, CommunityInfoModel.CommentInfoModel data);
    }

}


