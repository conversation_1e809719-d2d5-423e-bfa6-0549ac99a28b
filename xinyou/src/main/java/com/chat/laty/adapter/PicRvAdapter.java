package com.chat.laty.adapter;

import android.content.Context;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.chat.laty.R;
import com.chat.laty.utils.PicassoUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/21 15:27
 * @description:
 */
public class PicRvAdapter extends BaseQuickAdapter<String, QuickViewHolder> {

    public PicRvAdapter(@NonNull List<String> items) {
        super(items);
    }

    @Override
    protected void onBindViewHolder(@NonNull QuickViewHolder viewHolder, int i, @Nullable String s) {
        PicassoUtils.showImage(viewHolder.getView(R.id.iv_image), s);
    }

    @NonNull
    @Override
    protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int i) {
        return new QuickViewHolder(R.layout.adapter_pic_img, viewGroup);
    }
}
