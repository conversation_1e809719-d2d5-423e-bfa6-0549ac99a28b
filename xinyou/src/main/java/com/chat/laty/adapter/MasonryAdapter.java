package com.chat.laty.adapter;

import android.content.Context;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.chat.laty.R;
import com.chat.laty.entity.MasonryInfo;

/**
 * <AUTHOR>
 * @date 2024/1/21 15:27
 * @description:钻石适配器
 */
public class MasonryAdapter extends BaseQuickAdapter<MasonryInfo, QuickViewHolder> {

    private int showType;

    public MasonryAdapter(int anInt) {
        showType = anInt;
    }

    @Override
    protected void onBindViewHolder(@NonNull QuickViewHolder helper, int i, @Nullable MasonryInfo item) {
        helper.setText(R.id.tv_time, "" + item.getCreateTime());
        helper.setText(R.id.tv_detail, "" + item.getChangeDetail());
//        helper.setGone(R.id.tv_status, 5 != showType);

        if (5 == showType) {
            helper.setText(R.id.tv_status, "手续费 ¥" + item.getCommission());
            helper.setText(R.id.tv_balance, "¥" + item.getWithdrawalAmount());
        } else {
            helper.setText(R.id.tv_balance, "+"+item.getChangeDiamond()+"钻石");
            helper.setText(R.id.tv_status, "余额："+item.getLastDiamond()+"钻石");
        }

    }

    @NonNull
    @Override
    protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int i) {
        return new QuickViewHolder(R.layout.adapter_masonry_detail_layout, viewGroup);
    }
}
