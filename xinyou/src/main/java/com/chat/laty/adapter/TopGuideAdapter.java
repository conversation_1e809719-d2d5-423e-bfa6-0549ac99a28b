package com.chat.laty.adapter;

import android.content.Context;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.chat.laty.R;
import com.chat.laty.entity.TopGuideInfo;
import com.chat.laty.utils.PicassoUtils;

/**
 * <AUTHOR>
 * @date 2023/11/21 15:27
 * @description:
 */
public class TopGuideAdapter extends BaseQuickAdapter<TopGuideInfo, QuickViewHolder> {

    @Override
    protected void onBindViewHolder(@NonNull QuickViewHolder help, int i, @Nullable TopGuideInfo item) {
        help.setText(R.id.big_title_tv, item.getBigTitle());
        help.setText(R.id.small_desc_tv, item.getSmallTitle());
        PicassoUtils.showImage(help.getView(R.id.top_riv), item.getTopSrc());
        PicassoUtils.showImage(help.getView(R.id.bg_riv), item.getBackSrc());
    }

    @NonNull
    @Override
    protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int i) {
        return new QuickViewHolder(R.layout.adapter_top_guide_layout, viewGroup);
    }
}
