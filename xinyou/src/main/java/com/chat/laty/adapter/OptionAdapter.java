package com.chat.laty.adapter;

import android.content.Context;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.chat.laty.R;
import com.chat.laty.entity.OptionItem;
import com.chat.laty.utils.PicassoUtils;

/**
 * <AUTHOR>
 * @date 2023/11/21 15:27
 * @description:
 */
public class OptionAdapter extends BaseQuickAdapter<OptionItem, QuickViewHolder> {


    @Override
    protected void onBindViewHolder(@NonNull QuickViewHolder helper, int i, @Nullable OptionItem item) {
        helper.setText(R.id.option_tv, item.getTitleName());
        PicassoUtils.showOptionImage(helper.getView(R.id.option_riv), item.getResSrc());
    }

    @NonNull
    @Override
    protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int i) {
        return new QuickViewHolder(R.layout.adapter_mine_option_layout, viewGroup);
    }
}
