package com.chat.laty.adapter;

import android.content.Context;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.chat.laty.R;
import com.chat.laty.entity.RecommendationBean;
import com.chat.laty.utils.PicassoUtils;

public class RecommendPopAdapter extends BaseQuickAdapter<RecommendationBean, QuickViewHolder> {

    @Override
    protected void onBindViewHolder(@NonNull QuickViewHolder viewHolder, int i, @Nullable RecommendationBean bean) {
        PicassoUtils.showImage(viewHolder.getView(R.id.riv_avatar), bean.getAvatar());
        viewHolder.setText(R.id.tv_name, bean.getNickname());
    }

    @NonNull
    @Override
    protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int i) {
        return new QuickViewHolder(R.layout.item_recommend_layout, viewGroup);
    }

}
