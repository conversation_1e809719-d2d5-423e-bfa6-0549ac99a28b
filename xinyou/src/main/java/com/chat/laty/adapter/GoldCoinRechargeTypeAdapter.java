package com.chat.laty.adapter;

import android.content.Context;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.allen.library.helper.ShapeBuilder;
import com.allen.library.shape.ShapeTextView;
import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.chat.laty.R;
import com.chat.laty.base.XYApplication;

/**
 * <AUTHOR>
 * @date 2023/11/21 15:27
 * @description:
 */
public class GoldCoinRechargeTypeAdapter extends BaseQuickAdapter<String, QuickViewHolder> {

    private int mCurrentPosition = 0;

    public void setSelectItem(int position) {
        mCurrentPosition = position;
        notifyDataSetChanged();
    }

    @Override
    protected void onBindViewHolder(@NonNull QuickViewHolder helper, int i, @Nullable String item) {
        helper.setText(R.id.submit_btn, item);

        ShapeTextView selectTv = helper.getView(R.id.submit_btn);
        if (mCurrentPosition == helper.getLayoutPosition()) {
            selectTv.setTextColor(XYApplication.getAppApplicationContext().getResources().getColor(R.color.srb_golden_stars));
            selectTv.setShapeBuilder(new ShapeBuilder().setShapeSolidColor(R.color.srb_golden_stars));
        } else {
            ShapeBuilder sb = new ShapeBuilder().setShapeSolidColor(XYApplication.getAppApplicationContext().getResources().getColor(R.color.colorAccent));
            selectTv.setTextColor(XYApplication.getAppApplicationContext().getResources().getColor(R.color.black));
            selectTv.setShapeBuilder(selectTv.getShapeBuilder().setShapeSolidColor(R.color.black));
        }
    }

    @NonNull
    @Override
    protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int i) {
        return new QuickViewHolder(R.layout.adapter_recharge_type_layout, viewGroup);
    }
}
