package com.chat.laty.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatTextView;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.chat.laty.R;
import com.chat.laty.entity.AccostLanguageInfo;
import com.chat.laty.utils.PicassoUtils;

/**
 * <AUTHOR>
 * @date 2023/11/21 15:27
 * @description:
 */
public class AccostLanguageAdapter extends BaseQuickAdapter<AccostLanguageInfo, QuickViewHolder> {

    @Override
    protected void onBindViewHolder(@NonNull QuickViewHolder viewHolder, int i, @Nullable AccostLanguageInfo s) {
        //状态(1-审核中 2-审核通过 3-审核拒绝)
        PicassoUtils.showImage(viewHolder.getView(R.id.accost_riv), s.getTempImg());
        viewHolder.setText(R.id.accost_content_tv, s.getTempText());
        viewHolder.setText(R.id.template_tv, s.getTempName());
        AppCompatTextView textView = viewHolder.getView(R.id.use_template_tv);
        textView.setSelected("1".equals(s.getIsDefault()));

        viewHolder.setGone(R.id.accost_language_play_tv, TextUtils.isEmpty(s.getVoiceUrl()));

        viewHolder.setText(R.id.status_tv, TextUtils.equals("1", s.getStatus()) ? "审核中" : TextUtils.equals("2", s.getStatus()) ? "审核通过" : "审核拒绝");
        viewHolder.setText(R.id.accost_language_play_tv, s.getVoiceTimeLength() + "s");
    }

    @NonNull
    @Override
    protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int i) {
        return new QuickViewHolder(R.layout.adapter_accost_language_setting_layout, viewGroup);
    }
}
