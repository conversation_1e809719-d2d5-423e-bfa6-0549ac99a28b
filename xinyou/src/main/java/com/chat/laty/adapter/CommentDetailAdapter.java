package com.chat.laty.adapter;

import android.content.Context;
import android.text.Html;
import android.text.TextUtils;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.chat.laty.R;
import com.chat.laty.entity.CommunityInfoModel;
import com.chat.laty.utils.PicassoUtils;

/**
 * <AUTHOR>
 * @date 2023/11/21 15:27
 * @description:
 */
public class CommentDetailAdapter extends BaseQuickAdapter<CommunityInfoModel.CommentInfoModel, QuickViewHolder> {


    @Override
    protected void onBindViewHolder(@NonNull QuickViewHolder helper, int position, @Nullable CommunityInfoModel.CommentInfoModel item) {

        PicassoUtils.showImage(helper.getView(R.id.iv_avatar), item.getAvatar());
//        helper.setText(R.id.tv_user_name, item.getUserName());
        helper.setText(R.id.tv_time, item.getCreateDate());


        String content = "";
        if (TextUtils.isEmpty(item.getParentUserName())) {
            content = getContext().getString(R.string.comment_template_1, item.getUserName(), "");
        } else {
            content = getContext().getString(R.string.comment_template_2, item.getUserName(), item.getParentUserName(), "");
        }
//        helper.setText(R.id.tv_content, Html.fromHtml(content));
        helper.setText(R.id.tv_user_name, Html.fromHtml(content));

        helper.setText(R.id.tv_content, item.getContent());
    }

    @NonNull
    @Override
    protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int i) {
        return new QuickViewHolder(R.layout.adapter_detail_comment_layout, viewGroup);
    }

}


