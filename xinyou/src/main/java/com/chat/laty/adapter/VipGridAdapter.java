package com.chat.laty.adapter;

import android.content.Context;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.chat.laty.R;
import com.chat.laty.entity.VipGridModel;
import com.chat.laty.utils.PicassoUtils;

/**
 * <AUTHOR>
 * @date 2023/11/21 15:27
 * @description:礼物适配器
 */
public class VipGridAdapter extends BaseQuickAdapter<VipGridModel, QuickViewHolder> {

    @Override
    protected void onBindViewHolder(@NonNull QuickViewHolder helper, int i, @Nullable VipGridModel item) {
        PicassoUtils.showImage(helper.getView(R.id.iv_icon), item.getIcon());
//        helper.setImageDrawable(R.id.iv_icon, getContext().getDrawable(item.getIcon()));
        helper.setText(R.id.tv_title, item.getTitle());
    }

    @NonNull
    @Override
    protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int i) {
        return new QuickViewHolder(R.layout.adapter_vip_grid_layout, viewGroup);
    }
}
