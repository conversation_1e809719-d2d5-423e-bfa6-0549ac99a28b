package com.chat.laty.adapter;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatTextView;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.chat.laty.R;
import com.chat.laty.entity.VisitorUserInfo;
import com.chat.laty.utils.PicassoUtils;

/**
 * <AUTHOR>
 * @date 2023/11/21 15:27
 * @description:
 */
public class VisitorAdapter extends BaseQuickAdapter<VisitorUserInfo, QuickViewHolder> {

    @Override
    protected void onBindViewHolder(@NonNull QuickViewHolder viewHolder, int i, @Nullable VisitorUserInfo s) {
        PicassoUtils.showImage(viewHolder.getView(R.id.user_riv), s.getToUserAvatar());
        viewHolder.setText(R.id.user_name_tv, s.getToUserName());
        viewHolder.setText(R.id.time_tv, s.getCreateTime());
        setSex(viewHolder.itemView.getContext(), s.getToUserSex(), viewHolder.getView(R.id.user_sex_tv));
    }

    private void setSex(Context context, String sex, AppCompatTextView userAgeTv) {
        Drawable dwLeft = context.getResources().getDrawable((TextUtils.isEmpty(sex) && TextUtils.equals("1", sex)) ? R.mipmap.icon_nan_select : R.mipmap.icon_nv_select);
        dwLeft.setBounds(0, 0, dwLeft.getMinimumWidth(), dwLeft.getMinimumHeight());
        userAgeTv.setCompoundDrawables(dwLeft, null, null, null);
    }

    @NonNull
    @Override
    protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int i) {
        return new QuickViewHolder(R.layout.adapter_visitor_layout, viewGroup);
    }
}
