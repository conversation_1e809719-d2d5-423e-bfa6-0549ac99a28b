package com.chat.laty.adapter;

import android.content.Context;
import android.graphics.Color;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.chat.laty.R;
import com.chat.laty.entity.InviteInfoRulesModel;

public class RewardTableAdapter extends BaseQuickAdapter<InviteInfoRulesModel, QuickViewHolder> {

    private int currentUserLevel = -1; // 当前用户等级

    public void setCurrentUserLevel(int level) {
        this.currentUserLevel = level;
        notifyDataSetChanged();
    }

    @Override
    protected void onBindViewHolder(@NonNull QuickViewHolder helper, int position, @Nullable InviteInfoRulesModel item) {
        if (item == null) return;

        // 等级从1开始
        int level = item.getId();
        
        // 判断是否为当前等级（红色高亮）
        boolean isCurrentLevel = level == currentUserLevel;
        int textColor = isCurrentLevel ? Color.parseColor("#FA3D38") : 
                       getContext().getResources().getColor(R.color.color_333333);

        // 设置等级
        TextView levelTv = helper.getView(R.id.tv_level);
        if (isCurrentLevel) {
            levelTv.setText("当前等级" + level);
        } else {
            levelTv.setText(String.valueOf(level));
        }
        levelTv.setTextColor(textColor);

        // 设置邀请认证人数
        TextView certNumberTv = helper.getView(R.id.tv_cert_number);
        certNumberTv.setText(item.getCertNumber() != null ? item.getCertNumber() : "0");
        certNumberTv.setTextColor(textColor);

        // 设置收益奖励
        TextView profitRatioTv = helper.getView(R.id.tv_profit_ratio);
        String profitText = item.getProfitRatio() != null ? item.getProfitRatio() : "8";
        if (!profitText.endsWith("%")) {
            profitText += "%";
        }
        profitRatioTv.setText(profitText);
        profitRatioTv.setTextColor(textColor);
    }

    @NonNull
    @Override
    protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int i) {
        return new QuickViewHolder(R.layout.item_reward_table, viewGroup);
    }
}