package com.chat.laty.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.alibaba.fastjson.JSONArray;
import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.chat.laty.R;
import com.chat.laty.entity.CommunityInfoModel;
import com.chat.laty.entity.MediaFileModel;
import com.chat.laty.utils.PicassoUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/21 15:27
 * @description:
 */
public class CommunityV2Adapter extends BaseQuickAdapter<CommunityInfoModel, QuickViewHolder> {

    String avatar = "";
    int type;

    public CommunityV2Adapter(int type) {
        this.type = type;
    }

    public void setAvatar(String avatar) {
        if (avatar == null) {
            avatar = "";
        }
        this.avatar = avatar;
    }

    @Override
    protected void onBindViewHolder(@NonNull QuickViewHolder helper, int position, @Nullable CommunityInfoModel item) {

        helper.setText(R.id.tv_user_name, item.getUserName());

        boolean isAccost = TextUtils.equals("1", item.getIsAccost());
        helper.setVisible(R.id.tv_chat, isAccost);
        helper.setVisible(R.id.tv_accost, !isAccost);

        helper.setText(R.id.tv_user_info, item.getTextContent());
        helper.setVisible(R.id.tv_user_info, !TextUtils.isEmpty(item.getTextContent()));


        // --------------------

        if (item.uploadImgInfos == null) {
            item.uploadImgInfos = new ArrayList<>();
        }
        if (item.uploadImgInfos.isEmpty()) {
            if (TextUtils.isEmpty(item.getVideoImg())) {
                List<MediaFileModel> list = JSONArray.parseArray(item.getFileUrl(), MediaFileModel.class);
                if (list == null) {
                    list = new ArrayList<>();
                }
                item.uploadImgInfos = list;
            } else {
                MediaFileModel info = new MediaFileModel();
                info.setImgUrl(item.getFileUrl());
                info.setTumhImgUrl(item.getVideoImg());
                item.uploadImgInfos.add(info);
            }
        }


        final List<MediaFileModel> li = item.uploadImgInfos.isEmpty() ? new ArrayList<>() : item.uploadImgInfos.subList(0, 1);

        ImageView imageView = helper.getView(R.id.image_view);
        PicassoUtils.showImageWithGlide(getContext(), imageView, li.get(0).getTumhImgUrl(), 4);

        if (com.blankj.utilcode.util.CollectionUtils.isNotEmpty(li)) {
            MediaFileModel it = li.get(0);
            NineGridViewAdapter nineGridViewAdapter = new NineGridViewAdapter(getContext());

            helper.setVisible(R.id.iv_player, nineGridViewAdapter.isVideo(it));

            imageView.setOnClickListener(view -> nineGridViewAdapter.onClickListener(getContext(), it, li));
        } else {
            imageView.setOnClickListener(null);
        }
    }

    @NonNull
    @Override
    protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int i) {
        return new QuickViewHolder(R.layout.adapter_community_v2_layout, viewGroup);
    }
}


