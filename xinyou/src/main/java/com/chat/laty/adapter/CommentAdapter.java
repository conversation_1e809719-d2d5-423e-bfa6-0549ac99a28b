package com.chat.laty.adapter;

import android.content.Context;
import android.text.Html;
import android.text.TextUtils;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.chat.laty.R;
import com.chat.laty.entity.CommunityInfoModel;

/**
 * <AUTHOR>
 * @date 2023/11/21 15:27
 * @description:
 */
public class CommentAdapter extends BaseQuickAdapter<CommunityInfoModel.CommentInfoModel, QuickViewHolder> {

    @Override
    protected void onBindViewHolder(@NonNull QuickViewHolder helper, int position, @Nullable CommunityInfoModel.CommentInfoModel item) {

        String content = "";
        if (TextUtils.isEmpty(item.getParentUserName())) {
            content = getContext().getString(R.string.comment_template_1, item.getUserName(), item.getContent());
        } else {
            content = getContext().getString(R.string.comment_template_2, item.getUserName(), item.getParentUserName(), item.getContent());
        }
//        helper.setText(R.id.tv_content, Html.fromHtml(content));
        ((TextView)helper.itemView).setText(Html.fromHtml(content));
    }

    @NonNull
    @Override
    protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int i) {
        return new QuickViewHolder(R.layout.adapter_comment_layout, viewGroup);
    }
}


