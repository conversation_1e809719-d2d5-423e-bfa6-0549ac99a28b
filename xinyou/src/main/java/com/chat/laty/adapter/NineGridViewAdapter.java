package com.chat.laty.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.chat.laty.R;
import com.chat.laty.activity.DischargedPicBrowserActivity;
import com.chat.laty.activity.VideoPlayActivity;
import com.chat.laty.base.XYApplication;
import com.chat.laty.entity.DiaplayPicInfo;
import com.chat.laty.entity.MediaFileModel;
import com.chat.laty.utils.PicassoUtils;
import com.chat.laty.view.NineGridImageView;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/21 15:27
 * @description:
 */
public class NineGridViewAdapter extends BaseQuickAdapter<MediaFileModel, QuickViewHolder> {

    private int width = 0;
    private int height = 0;

    private int wrapWidth = 0;

    public NineGridViewAdapter(Context context) {
        wrapWidth = XYApplication.screenWidth - context.getResources().getDimensionPixelSize(R.dimen.dp_15) * 2;
    }

    public void setList(List<MediaFileModel> list) {

        if (list.size() > 2) {
            width = height = wrapWidth / 3;
        } else if (list.size() == 2) {
            width = wrapWidth / 2;
            height = width * 4 / 5;
        } else {
            width = wrapWidth * 4 / 5;
            height = width * 2 / 3;
        }
        setItems(list);
        notifyDataSetChanged();
    }

    @Override
    protected void onBindViewHolder(@NonNull QuickViewHolder holder, int i, @Nullable MediaFileModel model) {
//        PicassoUtils.showImage(holder.getView(R.id.image_view), model.getTumhImgUrl());
        ImageView imageView = holder.getView(R.id.image_view);
        NineGridImageView nineGridImageView = holder.getView(R.id.nine_grid);

        ViewGroup.LayoutParams params = imageView.getLayoutParams();

        if (getItems().size() == 1) {
            if (isVideo(model)) {
                params.width = wrapWidth * 4 / 7;
                params.height = wrapWidth * 4 / 5;
                imageView.setLayoutParams(params);
                imageView.setScaleType(ImageView.ScaleType.CENTER_INSIDE);
                imageView.setVisibility(View.VISIBLE);
                nineGridImageView.setVisibility(View.GONE);
                PicassoUtils.showImageWithGlide(getContext(), imageView, model.getTumhImgUrl(), 4);

            } else {
//                FrameLayout.LayoutParams lp = new FrameLayout.LayoutParams(FrameLayout.LayoutParams.WRAP_CONTENT, FrameLayout.LayoutParams.WRAP_CONTENT);
//                imageView.setLayoutParams(lp);
//                imageView.setMaxWidth(wrapWidth * 4 / 5);
//                imageView.setAdjustViewBounds(true);
//                imageView.setScaleType(ImageView.ScaleType.FIT_START);
                imageView.setVisibility(View.GONE);
                List<String> urls = new ArrayList<>();
                urls.add(model.getTumhImgUrl());
                nineGridImageView.setUrlList(urls);
                nineGridImageView.setVisibility(View.VISIBLE);
            }
        } else {
            params.width = width;
            params.height = height;
            imageView.setScaleType(ImageView.ScaleType.CENTER_CROP);
            imageView.setLayoutParams(params);
            imageView.setVisibility(View.VISIBLE);
            nineGridImageView.setVisibility(View.GONE);
            PicassoUtils.showImageWithGlide(getContext(), imageView, model.getTumhImgUrl(), 4);
        }

        holder.setVisible(R.id.iv_player, isVideo(model));
    }


    @NonNull
    @Override
    protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int i) {
        int layoutRes = R.layout.nine_grid_view_item_layout;

        return new QuickViewHolder(layoutRes, viewGroup);
    }

    public boolean isVideo(MediaFileModel item) {
        return item.getImgUrl() != null && item.getImgUrl().contains(".mp4");
    }


    public void onClickListener(Context context, MediaFileModel model, List<MediaFileModel> li) {
        if (isVideo(model)) {
            VideoPlayActivity.start(context, model.getImgUrl());
        } else {
            ArrayList<DiaplayPicInfo> pics = new ArrayList<>();
            int index = 0;
            for (int i = 0; i < li.size(); i++) {
                MediaFileModel it = li.get(i);

                if (isVideo(it)) {
                    continue;
                }

                String url = (TextUtils.isEmpty(it.getImgAddress()) || TextUtils.equals("0",it.getImgAddress())) ? it.getImgUrl() : it.getImgAddress();
                DiaplayPicInfo picInfo = new DiaplayPicInfo(url);
                pics.add(picInfo);
                if (model.getImgUrl().equals(url)) {
                    index = i;
                }
            }
            DischargedPicBrowserActivity.start(context, pics, index);
        }
    }
}
