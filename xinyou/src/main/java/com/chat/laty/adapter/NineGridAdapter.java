package com.chat.laty.adapter;

import android.content.Context;
import android.view.View;
import android.widget.ImageView;

import com.bumptech.glide.Glide;
import com.hjq.toast.Toaster;
import com.chat.laty.R;
import com.chat.laty.listener.OnAddPicturesListener;
import com.zhy.adapter.recyclerview.CommonAdapter;
import com.zhy.adapter.recyclerview.base.ViewHolder;

import java.util.List;

public class NineGridAdapter extends CommonAdapter<String> {


    private Context context;

    OnAddPicturesListener listener;

    private int deletePosition;

//    protected Transferee transferee;
//
//    protected TransferConfig config;

    public NineGridAdapter(Context context, List<String> selectPath) {
        super(context, R.layout.item_img, selectPath);
        this.context = context;

        selectPath.add("");
    }

    /**
     * 设置最大图片数量
     */
    public void setMaxSize(int maxNum) {
    }

    /**
     * 设置点击添加按钮的监听
     */
    public void setOnAddPicturesListener(OnAddPicturesListener listener) {
        this.listener = listener;
    }


    @Override
    protected void convert(ViewHolder viewHolder, String item, final int position) {
        ImageView ivThum = viewHolder.getView(R.id.iv_thum);
        ImageView ivAdd = viewHolder.getView(R.id.iv_add);
        if (item.equals("")) {
            //item为添加按钮
            ivThum.setVisibility(View.GONE);
            ivAdd.setVisibility(View.VISIBLE);
        } else {
            //item为普通图片
            ivThum.setVisibility(View.VISIBLE);
            ivAdd.setVisibility(View.GONE);
        }
        Glide.with(mContext).load(item).into(ivThum);
        ivThum.setOnClickListener(new PicturesClickListener(position));
        ivAdd.setOnClickListener(new PicturesClickListener(position));

        ivThum.setOnLongClickListener(new View.OnLongClickListener() {
            @Override
            public boolean onLongClick(View view) {
                deletePosition = position;
//                //最上面的三个删除按钮是往下的  其他的都是往上的
//                if (position < 3) {
//                    menuView.setSites(PopupView.SITE_BOTTOM);
//                } else {
//                    menuView.setSites(PopupView.SITE_TOP);
//                }
//                menuView.show(view);
                return false;
            }
        });
    }

    /**
     * 图片点击事件
     */
    private class PicturesClickListener implements View.OnClickListener {

        int position;

        public PicturesClickListener(int position) {
            this.position = position;
        }

        @Override
        public void onClick(View view) {
            switch (view.getId()) {
                case R.id.iv_thum:
                    //点击图片
                    Toaster.show("点击图片");
                    break;
                case R.id.iv_add:
                    //点击添加按钮
                    if (listener != null)
                        listener.onAdd();
                    Toaster.show("点击添加");
                    break;
            }
        }
    }

}