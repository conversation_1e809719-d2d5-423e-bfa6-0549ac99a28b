package com.chat.laty.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.chat.laty.R;
import com.chat.laty.base.XYApplication;
import com.chat.laty.entity.MyGroupListInfoModel;
import com.chat.laty.entity.PickerBean;
import com.chat.laty.entity.RyUserInfo;
import com.chat.laty.utils.PicassoUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/21 15:27
 * @description:礼物适配器
 */
public class MyGroupMoreAdapter extends BaseQuickAdapter<MyGroupListInfoModel, QuickViewHolder> {

    private int type = 1;
    private int level = 1;

    private String userId = "";

    RyUserInfo userInfo;

    public MyGroupMoreAdapter(int type, int level, String userId) {
        this.type = type;
        this.level = level;
        this.userId = userId;

        if (type == 1 && level > 0) {
            userInfo = XYApplication.getCurrentUserInfo();
        }

        Log.e("user_level", "MyGroupMoreAdapter: " + level);
    }


    @Override
    protected void onBindViewHolder(@NonNull QuickViewHolder helper, int i, @Nullable MyGroupListInfoModel item) {
        PicassoUtils.showImage(helper.getView(R.id.iv_avatar), item.getAvatar());
        helper.setText(R.id.tv_user_name, item.getNickname());
        helper.setText(R.id.tv_user_id, "ID：" + item.getUserId());
        helper.setText(R.id.tv_create_time, "注册时间：" + item.getRegisterTime());
        helper.setText(R.id.tv_vip_label, getLabel(item.getDbnLevel()));
        if (TextUtils.equals(userId, item.getUserId())) {
            helper.setGone(R.id.iv_more, true);
        } else {
            helper.setVisible(R.id.iv_more, true);
        }
        // test data
        // if (i == 3) item.setIsLogOff("1");

        boolean enable = TextUtils.equals("1", item.getIsLogOff());

        if (enable) {
            helper.setVisible(R.id.v_mask, true);
            helper.setVisible(R.id.tv_user_state, true);
        } else {
            helper.setGone(R.id.v_mask, true);
            helper.setGone(R.id.tv_user_state, true);

        }

        if (type == 1) {
            helper.setText(R.id.tv_sj_value, "总社交奖励：" + item.getSocializeNum());
            helper.setText(R.id.tv_tg_value, "总推广奖励：" + item.getPlugNum());
            helper.setText(R.id.tv_cz_value, "总充值奖励：" + item.getRechargeNum());
            helper.setText(R.id.tv_zt_value, "总直推人数：" + item.getDirectNum());

            helper.setText(R.id.jr_tv_sj_value, "今日社交奖励：" + item.getJrsocializeNum());
            helper.setText(R.id.jr_tv_tg_value, "今日推广奖励：" + item.getJrplugNum());
            helper.setText(R.id.jr_tv_cz_value, "今日充值奖励：" + item.getJrrechargeNum());
            helper.setText(R.id.jr_tv_zt_value, "今日直推人数：" + item.getJrdirectNum());

            //判断是否是男用户
            if (item.getSex() == 1) {
                //今日充值金额
                helper.setVisible(R.id.tv_recharge_today, true);
                helper.setText(R.id.tv_recharge_today, "今日充值金额：￥" + item.getRechargeToday());
            } else {
                helper.setVisible(R.id.tv_recharge_today, false);
            }


            if (!enable && level > 0 && item.getDbnLevel() > 0 && level >= item.getDbnLevel()) {
                boolean setting = TextUtils.equals("1", item.getIsSettingsLevel());
                helper.getView(R.id.tv_set_level).setSelected(!setting);
                helper.setText(R.id.tv_set_level, setting ? "已设定" : "设定级别");
                helper.setVisible(R.id.tv_set_level, true);
            } else {
                helper.setGone(R.id.tv_set_level, true);
            }
        } else {
            helper.setText(R.id.tv_zt_value, "直推人数：" + item.getDirectNum());
        }
    }

    @NonNull
    @Override
    protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int i) {

        if (type == 2) {
            return new QuickViewHolder(R.layout.adapter_my_group_more_layout, viewGroup);
        } else {
            return new QuickViewHolder(R.layout.adapter_my_group_zt_layout, viewGroup);
        }
    }

    public static String getLabel(int type) {
        switch (type) {
            case 10:
                return "会员";
            case 20:
                return "超级会员";
            case 30:
                return "运营商";
            case 40:
                return "合伙人";
            case 50:
                return "合伙人1";
            case 60:
                return "合伙人2";
            default:
                return "普通用户";
        }
    }

    List<Integer> levels = Arrays.asList(10, 20, 30, 40, 50, 60);

    public List<PickerBean> getLevelList(int userLevel, int dbnLevel) {

        List<PickerBean> list = new ArrayList<>();

        if (dbnLevel > 0 && userLevel >= dbnLevel) {
            // userLevel >= dbnLevel >= 10;

            for (Integer item : levels) {
                if (item <= userLevel && item >= dbnLevel) {
                    list.add(new PickerBean(String.valueOf(item), getLabel(item)));
                }
            }
        }
        return list;
    }
}

