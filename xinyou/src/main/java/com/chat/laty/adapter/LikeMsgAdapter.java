package com.chat.laty.adapter;

import android.content.Context;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.chat.laty.R;
import com.chat.laty.entity.SystemMessageInfo;
import com.chat.laty.utils.PicassoUtils;

/**
 * <AUTHOR>
 * @date 2023/11/21 15:27
 * @description:
 */
public class LikeMsgAdapter extends BaseQuickAdapter<SystemMessageInfo, QuickViewHolder> {

    @Override
    protected void onBindViewHolder(@NonNull QuickViewHolder help, int i, @Nullable SystemMessageInfo s) {
        help.setText(R.id.title_tv, s.getUserName());
        help.setText(R.id.content_tv, s.getCreateDate());

        PicassoUtils.showImage(help.getView(R.id.user_riv), s.getUserAvatar());
        PicassoUtils.showImage(help.getView(R.id.like_pic), s.getImageUrl());
    }

    @NonNull
    @Override
    protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int i) {
        return new QuickViewHolder(R.layout.adapter_like_message_layout, viewGroup);
    }
}
