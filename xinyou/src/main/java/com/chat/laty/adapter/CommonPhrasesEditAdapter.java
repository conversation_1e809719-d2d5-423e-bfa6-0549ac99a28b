package com.chat.laty.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.chat.laty.R;
import com.chat.laty.entity.CommonPhrasesInfo;

/**
 * <AUTHOR>
 * @date 2023/11/21 15:27
 * @description:
 */
public class CommonPhrasesEditAdapter extends BaseQuickAdapter<CommonPhrasesInfo, QuickViewHolder> {

    @Override
    protected void onBindViewHolder(@NonNull QuickViewHolder help, int i, @Nullable CommonPhrasesInfo item) {
        help.setText(R.id.phrases_content_tv,item.getName());

        help.setGone(R.id.delete_tv, TextUtils.equals("1",item.getIsSystem()));
    }

    @NonNull
    @Override
    protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int i) {
        return new QuickViewHolder(R.layout.adapter_common_phrases_edit_layout, viewGroup);
    }
}
