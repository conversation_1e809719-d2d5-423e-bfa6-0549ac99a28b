package com.chat.laty.adapter;


import android.content.Context;
import android.text.TextUtils;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.chat.laty.R;
import com.chat.laty.entity.UploadImgInfo;
import com.chat.laty.utils.PicassoUtils;


public class PicsAdapter extends BaseQuickAdapter<UploadImgInfo, QuickViewHolder> {


    @Override
    protected void onBindViewHolder(@NonNull QuickViewHolder helper, int i, @Nullable UploadImgInfo item) {
        if (TextUtils.isEmpty(item.getImgUrl()))
            PicassoUtils.showImage(helper.getView(R.id.channel_image), item.getImgAddress());
        else
            PicassoUtils.showImage(helper.getView(R.id.channel_image), item.getImgUrl());
    }

    @NonNull
    @Override
    protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int i) {
        return new QuickViewHolder(R.layout.adapter_pic_gridview_layout, viewGroup);
    }
}
