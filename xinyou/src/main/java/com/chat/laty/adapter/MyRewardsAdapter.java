package com.chat.laty.adapter;

import android.content.Context;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.chat.laty.R;
import com.chat.laty.entity.WithdrawalInfo;

/**
 * <AUTHOR>
 * @date 2023/11/21 15:27
 * @description:
 */
public class MyRewardsAdapter extends BaseQuickAdapter<WithdrawalInfo, QuickViewHolder> {

    @Override
    protected void onBindViewHolder(@NonNull QuickViewHolder help, int i, @Nullable WithdrawalInfo item) {
        help.setText(R.id.tv_coin,item.getAmount());
        help.setText(R.id.tv_price, item.getExchangeDiamond());

        help.itemView.setSelected(item.selected);

        help.getView(R.id.tv_coin).setSelected(item.selected);
        help.getView(R.id.tv_label).setSelected(item.selected);
        help.getView(R.id.tv_price).setSelected(item.selected);
    }

    @NonNull
    @Override
    protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int i) {
        return new QuickViewHolder(R.layout.adapter_my_rewards_layout, viewGroup);
    }
}
