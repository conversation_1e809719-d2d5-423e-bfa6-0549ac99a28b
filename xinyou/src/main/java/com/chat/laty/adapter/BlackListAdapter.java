package com.chat.laty.adapter;

import android.content.Context;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatTextView;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.makeramen.roundedimageview.RoundedImageView;
import com.chat.laty.R;
import com.chat.laty.base.XYApplication;
import com.chat.laty.entity.RyUserInfo;
import com.chat.laty.utils.FromJsonUtils;
import com.chat.laty.utils.LogUtil;
import com.chat.laty.utils.PicassoUtils;
import com.chat.laty.utils.ResultData;
import com.chat.laty.utils.net.Common;
import com.chat.laty.utils.net.HttpUtils;
import com.chat.laty.utils.net.TextCallBack;

import java.util.HashMap;

import static cn.xjc_soft.lib_utils.ThreadUtils.runOnUiThread;

/**
 * <AUTHOR>
 * @date 2023/11/21 15:27
 * @description:
 */
public class BlackListAdapter extends BaseQuickAdapter<String, QuickViewHolder> {

    @Override
    protected void onBindViewHolder(@NonNull QuickViewHolder holder, int i, @Nullable String userId) {
//        PicassoUtils.showImage(holder.getView(R.id.user_riv), friendInfo.getAvatar());
//        holder.setText(R.id.user_name_tv, friendInfo.getNickname());
//        holder.setGone(R.id.user_isreal_tv, (TextUtils.isEmpty(friendInfo.getIsReal()) || !"1".equals(friendInfo.getIsReal())));
//        holder.setGone(R.id.user_isname_tv, (TextUtils.isEmpty(friendInfo.getIsName()) || !"1".equals(friendInfo.getIsName())));
//        holder.setGone(R.id.user_isphone_tv, (TextUtils.isEmpty(friendInfo.getIsPhone()) || !"1".equals(friendInfo.getIsPhone())));
//
//        holder.setText(R.id.follow_tv, !TextUtils.isEmpty(friendInfo.getIsFollow()) && ("1".equals(friendInfo.getIsFollow())) ? "取关" : "关注");
//        holder.setText(R.id.xindong_tv, friendInfo.getMindNum());

        setUserInfo(userId,holder.getView(R.id.user_riv), holder.getView(R.id.user_name_tv));

    }

    @NonNull
    @Override
    protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int i) {
        return new QuickViewHolder(R.layout.adapter_black_list_layout, viewGroup);
    }

    private void setUserInfo(String userId, RoundedImageView userIconRiv, AppCompatTextView userNameTv) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("userId", userId);
        HttpUtils.get(Common.APP_GET_USER_BY_USERID, params, new TextCallBack() {
            @Override
            protected void onSuccess(String text) {
                ResultData<RyUserInfo> mData = FromJsonUtils.fromJson(text, RyUserInfo.class);
                if (200 == mData.getCode()) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            userNameTv.setText(mData.getData().getNickname());
                            PicassoUtils.showImage(userIconRiv,mData.getData().getAvatar());
                            XYApplication.getDaoInstant().getRyUserInfoDao().insertOrReplaceInTx(mData.getData());
                        }
                    });
                }

            }

            @Override
            protected void onFailure(ResponseException e) {
                LogUtil.e("响应结果", "==========" + e.toString());
            }
        });
    }

}
