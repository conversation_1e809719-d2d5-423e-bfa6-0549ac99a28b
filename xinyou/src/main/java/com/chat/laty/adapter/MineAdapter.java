package com.chat.laty.adapter;

import android.content.Context;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.chat.laty.R;

/**
 * <AUTHOR>
 * @date 2023/11/21 15:27
 * @description:
 */
public class MineAdapter extends BaseQuickAdapter<String, QuickViewHolder> {


    @Override
    protected void onBindViewHolder(@NonNull QuickViewHolder helper, int i, @Nullable String item) {
        helper.setText(R.id.submit_btn, item);
    }

    @NonNull
    @Override
    protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int i) {
        return new QuickViewHolder(R.layout.adapter_mine_layout, viewGroup);
    }
}
