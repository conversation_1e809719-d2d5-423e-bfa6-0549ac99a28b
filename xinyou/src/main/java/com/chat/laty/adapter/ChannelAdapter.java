package com.chat.laty.adapter;


import android.content.Context;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.makeramen.roundedimageview.RoundedImageView;
import com.chat.laty.R;
import com.chat.laty.entity.ChannelInfo;
import com.chat.laty.utils.PicassoUtils;


public class ChannelAdapter extends BaseQuickAdapter<ChannelInfo, QuickViewHolder> {


    @Override
    protected void onBindViewHolder(@NonNull QuickViewHolder helper, int i, @Nullable ChannelInfo item) {
        RoundedImageView icon = helper.getView(R.id.channel_image);
        PicassoUtils.showImage(icon, item.getIcon());

        helper.setText(R.id.channel_title, item.getTitle());
    }

    @NonNull
    @Override
    protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int i) {
        return new QuickViewHolder(R.layout.adapter_channel_layout, viewGroup);
    }
}
