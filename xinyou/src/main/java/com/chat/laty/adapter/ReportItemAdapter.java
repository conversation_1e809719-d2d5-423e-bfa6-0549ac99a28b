package com.chat.laty.adapter;

import android.content.Context;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatTextView;

import com.chad.library.adapter4.BaseQuickAdapter;
import com.chad.library.adapter4.viewholder.QuickViewHolder;
import com.chat.laty.R;

import java.util.ArrayList;
import java.util.List;

import cn.xjc_soft.lib_utils.LibCollections;

/**
 * <AUTHOR>
 * @date 2023/11/21 15:27
 * @description:
 */
public class ReportItemAdapter extends BaseQuickAdapter<String, QuickViewHolder> {
    private List<String> mSelects = new ArrayList<>();

    @Override
    protected void onBindViewHolder(@NonNull QuickViewHolder viewHolder, int i, @Nullable String s) {

        AppCompatTextView status = viewHolder.getView(R.id.select_statu);
        status.setSelected(mSelects.contains(s));
        viewHolder.setText(R.id.opt_tv, s);
    }

    @NonNull
    @Override
    protected QuickViewHolder onCreateViewHolder(@NonNull Context context, @NonNull ViewGroup viewGroup, int i) {
        return new QuickViewHolder(R.layout.adapter_report_opt_layout, viewGroup);
    }

    public void setSelectValue(String item) {
        if (mSelects.contains(item))
            mSelects.remove(item);
        else
            mSelects.add(item);
        notifyDataSetChanged();
    }

    public String getSelects() {
        String result = "";
        if (!LibCollections.isEmpty(mSelects)) {
            for (String item : mSelects) {
                result += item + ",";
            }
        }
        return result;
    }
}
