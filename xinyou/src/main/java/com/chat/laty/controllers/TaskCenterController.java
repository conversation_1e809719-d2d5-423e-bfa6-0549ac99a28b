package com.chat.laty.controllers;


import com.chat.laty.entity.TaskCenterInfoModel;
import com.chat.laty.mvp.IPresenter;
import com.chat.laty.mvp.IView;

/**
 * <AUTHOR>
 * @date 2022/4/26 11:55
 * @description:
 */
public interface TaskCenterController {

    interface IInfoView extends IView {
        void getInfoSucceed(TaskCenterInfoModel data);

        void getInfoFailed();

        void signSucceed();

        void signFailed();
    }

    interface Presenter extends IPresenter {
    }
}
