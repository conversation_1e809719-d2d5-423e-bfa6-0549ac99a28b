package com.chat.laty.controllers;


import com.chat.laty.entity.BaseBean;
import com.chat.laty.entity.RyUserInfo;
import com.chat.laty.mvp.IPresenter;
import com.chat.laty.mvp.IView;

/**
 * <AUTHOR>
 * @date 2022/4/26 11:55
 * @description:
 */
public interface ReceiveCallController {

    interface View extends IView {
        void showUserInfo(RyUserInfo data);

        void showRedisUserKey(BaseBean data);
    }

    interface Presenter extends IPresenter {
    }
}
