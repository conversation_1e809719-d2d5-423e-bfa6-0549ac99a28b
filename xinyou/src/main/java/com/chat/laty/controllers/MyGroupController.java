package com.chat.laty.controllers;


import com.chat.laty.entity.MyGroupInfoModel;
import com.chat.laty.entity.MyGroupListInfoModel;
import com.chat.laty.mvp.IPresenter;
import com.chat.laty.mvp.IView;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/26 11:55
 * @description:
 */
public interface MyGroupController {

    // 我的团队 - 主页
    interface IInfoView extends IView {
        void getInfoSucceed(MyGroupInfoModel data, boolean filter, String date);

        void getInfoFailed(boolean filter);

    }

    // 团队
    interface IGroupListView extends IView {
        void getGroupListSucceed(List<MyGroupListInfoModel> list, boolean load, boolean refresh, boolean loadmore);

        void getGroupListFailed(boolean load, boolean refresh, boolean loadmore);
    }

    interface ISetLevelView extends IView {
        void setLevelSucceed(int index, int level);

        void setLevelFailed();
    }

    interface Presenter extends IPresenter {
    }
}
