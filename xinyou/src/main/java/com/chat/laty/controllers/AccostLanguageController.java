package com.chat.laty.controllers;


import com.chat.laty.entity.AccostLanguageInfo;
import com.chat.laty.entity.AccostManLanguageInfo;
import com.chat.laty.entity.BaseBean;
import com.chat.laty.entity.UploadImgInfo;
import com.chat.laty.mvp.IPresenter;
import com.chat.laty.mvp.IView;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/26 11:55
 * @description:
 */
public interface AccostLanguageController {

    interface View extends IView {
        void showUploadImgResult(List<UploadImgInfo> data);

        void showAccostWomanList(List<AccostLanguageInfo> accostInfo);
        void showAccostManList(AccostManLanguageInfo accostInfo);

        void showupdateCallback(int code);

        void showDeleteCallback(Integer code);

        void showDefaultCallback(Integer code);

        void showCountDownFinish();

        void showUploadAudioResult(List<String> result);

        void showCountDownTravel(int l);

        void showUpdateCallback(BaseBean base);
    }

    interface Presenter extends IPresenter {
    }
}
