package com.chat.laty.controllers;


import com.chat.laty.mvp.IPresenter;
import com.chat.laty.mvp.IView;

import java.util.List;

import io.rong.imlib.model.Conversation;

/**
 * <AUTHOR>
 * @date 2022/4/26 11:55
 * @description:
 */
public interface ConversationController {

    interface View extends IView {
        void showConversationList(List<Conversation> conversations);
    }

    interface Presenter extends IPresenter {
    }
}
