package com.chat.laty.controllers;


import com.chat.laty.entity.SwTokenInfo;
import com.chat.laty.mvp.IPresenter;
import com.chat.laty.mvp.IView;

/**
 * <AUTHOR>
 * @date 2022/4/26 11:55
 * @description:
 */
public interface videoTelecastController {

    interface View extends IView {

        void showSwToken(SwTokenInfo data);

        void showConfigMap(SwTokenInfo data);
    }

    interface Presenter extends IPresenter {
    }
}
