package com.chat.laty.controllers;


import com.chat.laty.entity.BaseBean;
import com.chat.laty.entity.GeneralSettingInfo;
import com.chat.laty.entity.SimiInfo;
import com.chat.laty.entity.WebBeanInfo;
import com.chat.laty.mvp.IPresenter;
import com.chat.laty.mvp.IView;

/**
 * <AUTHOR>
 * @date 2022/4/26 11:55
 * @description:
 */
public interface GeneralSettingController {

    interface View extends IView {
        void showGeneralSetting(GeneralSettingInfo info);

        void showWebInfo(WebBeanInfo info);

        void showDeleteCallback(BaseBean baseBean);

        void showLogoutCallback(BaseBean uploadBean);

        void getSimiInfo(SimiInfo simiInfo);
    }

    interface Presenter extends IPresenter {
    }
}
