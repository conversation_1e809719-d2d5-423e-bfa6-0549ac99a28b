package com.chat.laty.controllers;


import com.chat.laty.entity.FatePairingInfo;
import com.chat.laty.entity.WebBeanInfo;
import com.chat.laty.mvp.IPresenter;
import com.chat.laty.mvp.IView;

/**
 * <AUTHOR>
 * @date 2022/4/26 11:55
 * @description:
 */
public interface FatePairController {

    interface View extends IView {

        void onFatePairMessageCallback(FatePairingInfo base,String extra);

        void showWebInfo(WebBeanInfo data);
    }

    interface Presenter extends IPresenter {
    }
}
