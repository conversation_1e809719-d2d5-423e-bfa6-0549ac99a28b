package com.chat.laty.controllers;


import com.chat.laty.entity.BaseBean;
import com.chat.laty.entity.CommunityInfoModel;
import com.chat.laty.mvp.IPresenter;
import com.chat.laty.mvp.IView;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/26 11:55
 * @description:
 */
public interface CommunityController {

    interface IListView extends IView {
        void getListSucceed(List<CommunityInfoModel> list, boolean load, boolean refresh, boolean loadmore);

        void getListFailed(String msg, boolean load, boolean refresh, boolean loadmore);

        void getUserCommunitys(List<CommunityInfoModel> data);
    }

    interface IAccostView extends IView {
        void accostSucceed(CommunityInfoModel data, int index);

        void likeSucceed(CommunityInfoModel data, int index);

        void updateSucceed(CommunityInfoModel model, int index);

        void replySucceed(CommunityInfoModel model, int index, CommunityInfoModel.CommentInfoModel data);

        void failed();

        void deleteCallback(BaseBean callbackBean);
    }

    interface Presenter extends IPresenter {
    }
}
