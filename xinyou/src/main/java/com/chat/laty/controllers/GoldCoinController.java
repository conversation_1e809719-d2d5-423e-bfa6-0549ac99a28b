package com.chat.laty.controllers;


import com.chat.laty.entity.GoldCoinDetailModel;
import com.chat.laty.entity.GoldCoinInfoModel;
import com.chat.laty.entity.WeiChatPayInfo;
import com.chat.laty.mvp.IPresenter;
import com.chat.laty.mvp.IView;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/26 11:55
 * @description:
 */
public interface GoldCoinController {

    interface IInfoView extends IView {
        void getInfoSucceed(GoldCoinInfoModel data);

        void getInfoFailed();


        void buySucceed(WeiChatPayInfo info);

        void buyFailed();
    }

    interface IDetailView extends IView {
        void getDetailSucceed(List<GoldCoinDetailModel> list, boolean load, boolean refresh, boolean loadmore);

        void getDetailFailed(boolean load, boolean refresh, boolean loadmore);
    }

    interface Presenter extends IPresenter {
    }
}
