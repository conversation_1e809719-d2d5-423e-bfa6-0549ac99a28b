package com.chat.laty.controllers;


import com.chat.laty.entity.BalanceInfo;
import com.chat.laty.entity.PickerViewBean;
import com.chat.laty.entity.RYTokenInfo;
import com.chat.laty.entity.UserCenterInfo;
import com.chat.laty.entity.WebBeanInfo;
import com.chat.laty.entity.WeiChatPayInfo;
import com.chat.laty.mvp.IPresenter;
import com.chat.laty.mvp.IView;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/26 11:55
 * @description:
 */
public interface MinePageController {

    interface View extends IView {
        void showAXPayParm(WeiChatPayInfo data);

        void showPayParm(RYTokenInfo data);

        void showDetails(PickerViewBean data);

        void showUserBalance(BalanceInfo balanceInfo);

        void showUserCenterDetails(UserCenterInfo data);

        void showWebInfo(WebBeanInfo data);

        void showViolationList(List<String> result);
    }

    interface Presenter extends IPresenter {
    }
}
