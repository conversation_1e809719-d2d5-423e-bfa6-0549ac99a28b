package com.chat.laty.controllers;


import com.chat.laty.entity.BaseBean;
import com.chat.laty.entity.RewardsEntity;
import com.chat.laty.mvp.IPresenter;
import com.chat.laty.mvp.IView;

/**
 * <AUTHOR>
 * @date 2022/4/26 11:55
 * @description:
 */
public interface RewardsController {

    interface View extends IView {
        void showRewards(RewardsEntity data);

        void showUpdateCallback(BaseBean baseBean);

        void showWithdrawalResult(BaseBean baseBean);
    }


    interface Presenter extends IPresenter {
    }
}
