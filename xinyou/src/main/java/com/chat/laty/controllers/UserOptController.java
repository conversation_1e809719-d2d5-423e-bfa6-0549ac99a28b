package com.chat.laty.controllers;


import com.chat.laty.entity.AddressInfo;
import com.chat.laty.entity.BaseBean;
import com.chat.laty.entity.LabelInfo;
import com.chat.laty.entity.PickerViewBean;
import com.chat.laty.entity.UploadImgInfo;
import com.chat.laty.mvp.IPresenter;
import com.chat.laty.mvp.IView;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/26 11:55
 * @description:
 */
public interface UserOptController {

    interface View extends IView {
        void showUploadResult(List<UploadImgInfo> result);

        void showLabels(int type, LabelInfo data);

        void showDetails(PickerViewBean data);

        void updateCallback(BaseBean callbackBean);

        void showAddress(List<AddressInfo> addressInfos);

        void showUploadAudioResult(List<String> result);

        void showUploadResults(List<String> data);
    }

    interface Presenter extends IPresenter {
    }
}
