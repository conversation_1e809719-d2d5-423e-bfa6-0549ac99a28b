package com.chat.laty.controllers;


import com.chat.laty.entity.SiMiUploadBean1;
import com.chat.laty.mvp.IPresenter;
import com.chat.laty.mvp.IView;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/26 11:55
 * @description:
 */
public interface SiMiPhotoController {

    interface View extends IView {

        void loadSiMiData(List<SiMiUploadBean1> data);

        void delSiMiResult(int position);

        void submitSiMiResult();

        void showUploadResults(List<String> data);
    }

    interface Presenter extends IPresenter {
    }
}
