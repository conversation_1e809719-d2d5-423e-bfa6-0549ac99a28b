package com.chat.laty.controllers;


import com.chat.laty.entity.BannerInfo;
import com.chat.laty.entity.MessageUnReadInfo;
import com.chat.laty.entity.RyUserInfo;
import com.chat.laty.mvp.IPresenter;
import com.chat.laty.mvp.IView;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/26 11:55
 * @description:
 */
public interface MessagePageController {

    interface View extends IView {
        void showMsgCount(MessageUnReadInfo data);
        void showUserInfo(RyUserInfo data);
        void showBanners(List<BannerInfo> data);
    }

    interface Presenter extends IPresenter {
    }
}
