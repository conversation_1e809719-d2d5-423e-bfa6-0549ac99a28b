package com.chat.laty.controllers;


import com.chat.laty.entity.ReportMessageInfo;
import com.chat.laty.entity.SystemMessageInfo;
import com.chat.laty.mvp.IPresenter;
import com.chat.laty.mvp.IView;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/26 11:55
 * @description:
 */
public interface SystemMsgController {

    interface View extends IView {
        void showMessages(List<SystemMessageInfo> data);

        void showLikeMessages(List<SystemMessageInfo> data);

        void showReportMessages(List<ReportMessageInfo> data);
    }

    interface Presenter extends IPresenter {
    }
}
