package com.chat.laty.controllers;


import com.chat.laty.entity.InviteBindRecordsInfoModel;
import com.chat.laty.entity.InviteFriendInfoModel;
import com.chat.laty.entity.InviteInfoModel;
import com.chat.laty.mvp.IPresenter;
import com.chat.laty.mvp.IView;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/26 11:55
 * @description:
 */
public interface InviteController {

    interface IInfoView extends IView {
        void getInfoSucceed(InviteInfoModel data);

        void getInfoFailed();

        void getFriendsSucceed(List<InviteFriendInfoModel> lise);

        void getFriendsFailed();

        void modifySucceed();
        void modifyFailed();

        void bindFriendSucceed();
        void bindFriendFailed();

        void getDownUrlSucceed(String url);

    }

    interface IBindSelfView extends IView{
        void bindSucceed();
        void  bindFailed();
    }

    interface IBindRecordsView extends IView{
        void getRecordsSucceed(List<InviteBindRecordsInfoModel> list);

        void getRecordsFailed();
    }

    interface Presenter extends IPresenter {
    }
}
