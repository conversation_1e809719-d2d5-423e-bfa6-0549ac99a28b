package com.chat.laty.controllers;


import com.chat.laty.entity.BalanceInfo;
import com.chat.laty.entity.BaseBean;
import com.chat.laty.entity.GiftInfo;
import com.chat.laty.entity.RyUserInfo;
import com.chat.laty.entity.VideoTipsInfo;
import com.chat.laty.mvp.IPresenter;
import com.chat.laty.mvp.IView;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/26 11:55
 * @description:
 */
public interface CallPhoneController {

    interface View extends IView {
        void showCountDown(long l);

        void showCountDownFinish();

        void showBalanceCountDown(long l);

        void showBalanceCountDownFinish();

        void showGiftResult(List<GiftInfo> data);

        void showUserBalance(BalanceInfo balanceInfo);

        void showVideoTips(VideoTipsInfo videoTipsInfo);

        void showUserInfo(RyUserInfo data);

        void showRedisUserKey(BaseBean data);
    }

    interface Presenter extends IPresenter {
    }
}
