package com.chat.laty.controllers;


import com.chat.laty.entity.FriendInfo;
import com.chat.laty.mvp.IPresenter;
import com.chat.laty.mvp.IView;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/26 11:55
 * @description:
 */
public interface FriendController {

    interface View extends IView {
        void showFriends(List<FriendInfo> friends);

        void showFollwoCallback();

        void showCancleFollwoCallback();
    }

    interface Presenter extends IPresenter {
    }
}
