package com.chat.laty.controllers;


import com.chat.laty.entity.VisitorUserInfo;
import com.chat.laty.mvp.IPresenter;
import com.chat.laty.mvp.IView;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/26 11:55
 * @description:
 */
public interface VisitorController {

    interface View extends IView {
        void showVisitorList(List<VisitorUserInfo> data);
    }

    interface Presenter extends IPresenter {
    }
}
