package com.chat.laty.controllers;


import com.chat.laty.entity.BaseBean;
import com.chat.laty.mvp.IPresenter;
import com.chat.laty.mvp.IView;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/26 11:55
 * @description:
 */
public interface ReportController {

    interface View extends IView {

        void showUploadResults(List<String> result);

        void showReportCallback(BaseBean callbackBean);
    }

    interface Presenter extends IPresenter {
    }
}
