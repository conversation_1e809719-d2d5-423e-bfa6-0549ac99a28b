package com.chat.laty.controllers;


import com.chat.laty.entity.BaseBean;
import com.chat.laty.entity.CostBean;
import com.chat.laty.entity.WebBeanInfo;
import com.chat.laty.mvp.IPresenter;
import com.chat.laty.mvp.IView;

/**
 * <AUTHOR>
 * @date 2022/4/26 11:55
 * @description:
 */
public interface CostSetController {

    interface View extends IView {
        void showCostCallback(CostBean costInfo);

        void showUpdateCallback(BaseBean baseBean);

        void showWebInfo(WebBeanInfo data);
    }

    interface Presenter extends IPresenter {
    }
}
