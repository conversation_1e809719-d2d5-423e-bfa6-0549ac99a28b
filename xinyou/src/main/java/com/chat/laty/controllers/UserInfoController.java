package com.chat.laty.controllers;


import com.chat.laty.entity.AddressInfo;
import com.chat.laty.entity.BaseBean;
import com.chat.laty.entity.LoinEntity;
import com.chat.laty.entity.RYTokenInfo;
import com.chat.laty.entity.RyUserInfo;
import com.chat.laty.entity.UserFreeInfo;
import com.chat.laty.entity.XYUserInfo;
import com.chat.laty.mvp.IPresenter;
import com.chat.laty.mvp.IView;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/26 11:55
 * @description:
 */
public interface UserInfoController {

    interface View extends IView {
        void showUploadResult(List<String> result);

        void updateCallback(BaseBean callbackBean);

        void showUserInfo(RyUserInfo data);

        void complementCallback(LoinEntity data);

        void showUserDetails(XYUserInfo data);

        void showRyToken(RYTokenInfo data);

        void showFollwoCallback();

        void showAddress(List<AddressInfo> addressInfos);

        void showLoginImCallback();

        void showUserFreeInfo(UserFreeInfo userFreeInfo);
    }

    interface Presenter extends IPresenter {
    }
}
