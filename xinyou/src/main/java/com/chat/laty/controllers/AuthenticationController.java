package com.chat.laty.controllers;


import com.chat.laty.entity.BaseBean;
import com.chat.laty.entity.BaseEntity;
import com.chat.laty.entity.UploadImgInfo;
import com.chat.laty.entity.UserCenterInfo;
import com.chat.laty.mvp.IPresenter;
import com.chat.laty.mvp.IView;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/12 11:55
 * @description:
 */
public interface AuthenticationController {

    interface View extends IView {
        void showRealNameCallback(BaseBean base);

        void showUploadResult(List<UploadImgInfo> result);

        void showRealPersonCallback(BaseBean base);

        void showUserVerifyStatus(BaseEntity statuInfo);

        void showUserCenterDetails(UserCenterInfo data);
    }

    interface Presenter extends IPresenter {
    }
}
