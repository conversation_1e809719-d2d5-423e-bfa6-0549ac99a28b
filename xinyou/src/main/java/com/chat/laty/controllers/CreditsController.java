package com.chat.laty.controllers;


import com.chat.laty.entity.CreditsFriendInfoModel;
import com.chat.laty.entity.CreditsRecordsModel;
import com.chat.laty.entity.CreditsStoreInfoModel;
import com.chat.laty.entity.MyCreditsDetailModel;
import com.chat.laty.entity.MyCreditsInfoModel;
import com.chat.laty.mvp.IPresenter;
import com.chat.laty.mvp.IView;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/26 11:55
 * @description:
 */
public interface CreditsController {

    // 我的积分 - 主页
    interface IMyInfoView extends IView {
        void getInfoSucceed(MyCreditsInfoModel data);

        void getFriendListSucceed(List<CreditsFriendInfoModel> list, boolean refresh, boolean loadmore);

        void getInfoFailed();

        void giveSucceed();

        void giveFailed();
    }

    // 我的积分 -详情
    interface IMyDetailView extends IView {
        void getDetailSucceed(List<MyCreditsDetailModel> list, boolean load, boolean refresh, boolean loadmore);

        void getDetailFailed(boolean load, boolean refresh, boolean loadmore);
    }


    // 积分商城 - 主页
    interface ICreditsStoreView extends IView {
        void getInfoSucceed(CreditsStoreInfoModel data);

        void getInfoFailed();
    }


    // 积分商城 - 主页
    interface ICreditsStoreDetailView extends IView {
        void exchangeSucceed();

        void exchangeFailed();
    }

    // 兑换记录
    interface ICreditsRecordsView extends IView {
        void getRecordsSucceed(List<CreditsRecordsModel> list);

        void getRecordsFailed();
    }

    interface Presenter extends IPresenter {
    }
}
