package com.chat.laty.controllers;


import com.luck.picture.lib.entity.LocalMedia;
import com.chat.laty.entity.UploadImgInfo;
import com.chat.laty.mvp.IPresenter;
import com.chat.laty.mvp.IView;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/26 11:55
 * @description:
 */
public interface DynamicController {

    interface UploadView extends IView {
        void uploadSucceed(LocalMedia item, List<UploadImgInfo> infos);

        void uploadFailed(LocalMedia item);
    }

    interface Presenter extends IPresenter {
    }

    interface ReleaseView extends IView {
        void releaseSucceed();

        void releaseFailed(String msg);
    }
}
