package com.chat.laty.controllers;


import com.chat.laty.entity.AppVersionInfo;
import com.chat.laty.entity.BannerInfo;
import com.chat.laty.entity.OtherUserInfo;
import com.chat.laty.entity.RYTokenInfo;
import com.chat.laty.entity.RecommendationBean;
import com.chat.laty.entity.TodayFateEntity;
import com.chat.laty.entity.WebBeanInfo;
import com.chat.laty.entity.WeiChatPayInfo;
import com.chat.laty.entity.XYUserInfo;
import com.chat.laty.mvp.IPresenter;
import com.chat.laty.mvp.IView;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/26 11:55
 * @description:
 */
public interface DiscoverController {

    interface View extends IView {
        void showPayParm(RYTokenInfo data);

        void showAXPayParm(WeiChatPayInfo data);

        void showRecommend(List<OtherUserInfo> data);

        void showBanners(List<BannerInfo> data);

        void showAccostToUserSuccess();

        void showTodayFate(TodayFateEntity todayFate);

        void showAppVersion(AppVersionInfo data);

        void showOneClickAccostCallback();

        void showWebInfo(WebBeanInfo data);

        void showUserDetails(XYUserInfo userInfo);

        void showRecomPop(List<RecommendationBean> recommendationBeans);

        void showRecomPop1();
    }

    interface Presenter extends IPresenter {
    }
}
