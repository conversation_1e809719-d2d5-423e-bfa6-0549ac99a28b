package com.chat.laty.controllers;


import com.chat.laty.entity.LoginEntity;
import com.chat.laty.entity.LoinEntity;
import com.chat.laty.entity.RYTokenInfo;
import com.chat.laty.entity.WXLoginInfo;
import com.chat.laty.entity.WebBeanInfo;
import com.chat.laty.mvp.IPresenter;
import com.chat.laty.mvp.IView;

/**
 * <AUTHOR>
 * @date 2022/4/26 11:55
 * @description:
 */
public interface LoginController {

    interface View extends IView {


        void showCountDown(long l);

        void showCountDownFinish();

        void showLogin(LoinEntity data);

        void showRyToken(RYTokenInfo data);

        void showLoginCallBack(LoginEntity data);

        void showWeiXinLogin(WXLoginInfo data);

        void showLoginImCallback();

        void showWebInfo(WebBeanInfo data);

        void loadVipData();
    }

    interface Presenter extends IPresenter {
    }
}
