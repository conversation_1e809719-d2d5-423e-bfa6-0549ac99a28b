package com.chat.laty.controllers;


import com.chat.laty.entity.BalanceInfo;
import com.chat.laty.entity.BaseBean;
import com.chat.laty.entity.CommonPhrasesInfo;
import com.chat.laty.entity.GiftInfo;
import com.chat.laty.entity.RyUserInfo;
import com.chat.laty.entity.UserFreeInfo;
import com.chat.laty.entity.XYUserInfo;
import com.chat.laty.mvp.IPresenter;
import com.chat.laty.mvp.IView;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/26 11:55
 * @description:
 */
public interface ChatController {

    interface View extends IView {
        void showUserInfo(RyUserInfo data);

        void showUserDetails(XYUserInfo userInfo);

        void showUserBalance(BalanceInfo balanceInfo);

        void showGiftResult(List<GiftInfo> data);

        void showCommonPhrases(List<CommonPhrasesInfo> data);

        void showDeleteCallback(BaseBean baseBean);

        void showAddCallback(BaseBean baseBean);

        void showSaveResult(BaseBean callbackBean);

        void showUserFreeInfo(UserFreeInfo userFreeInfo);
    }

    interface Presenter extends IPresenter {
    }
}
