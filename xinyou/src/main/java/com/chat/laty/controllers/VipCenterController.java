package com.chat.laty.controllers;


import com.chat.laty.entity.VipInfoModel;
import com.chat.laty.entity.VipDetailModel;
import com.chat.laty.entity.WeiChatPayInfo;
import com.chat.laty.mvp.IPresenter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/26 11:55
 * @description:
 */
public interface VipCenterController {

    interface IInfoView extends com.chat.laty.mvp.IView {
        void getInfoSucceed(VipInfoModel data);

        void getInfoFailed();

        void buySucceed(WeiChatPayInfo info);

        void failed();
    }

    interface IDetailView extends com.chat.laty.mvp.IView {
        void getDetailSucceed(List<VipDetailModel> list);

        void getDetailFailed();
    }

    interface Presenter extends IPresenter {
    }
}
