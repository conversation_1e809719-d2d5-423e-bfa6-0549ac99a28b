package com.chat.laty.manager;

import android.content.Context;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.LinearLayout;

import androidx.appcompat.widget.AppCompatTextView;

import com.lxj.xpopup.XPopup;
import com.lxj.xpopup.interfaces.OnConfirmListener;
import com.makeramen.roundedimageview.RoundedImageView;
import com.yhao.floatwindow.FloatWindow;
import com.yhao.floatwindow.MoveType;
import com.yhao.floatwindow.PermissionListener;
import com.yhao.floatwindow.Screen;
import com.chat.laty.R;
import com.chat.laty.activity.CallPhoneActivity;
import com.chat.laty.activity.ChatActivity;
import com.chat.laty.base.AppHelper;
import com.chat.laty.base.XYApplication;
import com.chat.laty.entity.BaseUserInfo;
import com.chat.laty.entity.XinYouCallInfo;
import com.chat.laty.entity.event.RCCallPlusSessionEvent;
import com.chat.laty.entity.event.SmallCallPlusSessionEvent;
import com.chat.laty.greenDao.BaseUserInfoDao;
import com.chat.laty.im.message.XYCallVideoContent;
import com.chat.laty.im.message.XYGiftContent;
import com.chat.laty.utils.FromJsonUtils;
import com.chat.laty.utils.LogUtil;
import com.chat.laty.utils.PicassoUtils;
import com.chat.laty.utils.ResultData;
import com.chat.laty.utils.SystemRoundUtils;
import com.chat.laty.utils.net.Common;
import com.chat.laty.utils.net.HttpUtils;
import com.chat.laty.utils.net.TextCallBack;

import org.greenrobot.eventbus.EventBus;

import java.util.HashMap;
import java.util.List;

import cn.rongcloud.callplus.api.RCCallPlusClient;
import cn.rongcloud.callplus.api.RCCallPlusCode;
import cn.rongcloud.callplus.api.RCCallPlusLocalVideoView;
import cn.rongcloud.callplus.api.RCCallPlusMediaType;
import cn.rongcloud.callplus.api.RCCallPlusReason;
import cn.rongcloud.callplus.api.RCCallPlusRenderMode;
import cn.rongcloud.callplus.api.RCCallPlusSession;
import cn.rongcloud.callplus.api.RCCallPlusUser;
import cn.rongcloud.callplus.api.RCCallPlusUserSessionStatus;
import cn.rongcloud.callplus.api.callback.IRCCallPlusEventListener;
import cn.rongcloud.callplus.api.callback.IRCCallPlusResultListener;
import io.rong.imkit.RongIM;
import io.rong.imkit.config.RongConfigCenter;
import io.rong.imkit.utils.RouteUtils;
import io.rong.imlib.IRongCallback;
import io.rong.imlib.RongIMClient;
import io.rong.imlib.model.Conversation;
import io.rong.imlib.model.Message;
import io.rong.message.TextMessage;

import static cn.xjc_soft.lib_utils.ThreadUtils.runOnUiThread;

/**
 * 附近聊爱探约小窗管理
 */
public class XySmallWindowManager {
    private static final String TAG = "XySmallWindowManager";

    private static XySmallWindowManager manager;
    private Context mContext;

    RCCallPlusSession mCallSession;

    public static XySmallWindowManager getInstance(Context context) {
        if (manager == null) {
            manager = new XySmallWindowManager(context);
        }
        return manager;
    }

    private XySmallWindowManager(Context context) {
        mContext = context;
    }

    /**
     * 获取用户信息来电提醒
     */
    public void showCallReminderUserInfo(RCCallPlusSession callSession, boolean isSecondPhone) {
        mCallSession = callSession;
        BaseUserInfo userInfo = XYApplication.getDaoInstant().getBaseUserInfoDao().queryBuilder().where(BaseUserInfoDao.Properties.UserId.eq(callSession.getCallerUserId())).unique();
        if (null != userInfo) {
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    showCallReminder(callSession, userInfo, isSecondPhone);
                }
            });
        } else {
            HashMap<String, Object> params = new HashMap<>();
            params.put("userId", callSession.getCallerUserId());
            HttpUtils.get(Common.APP_GET_CHAT_RY_USERINFO, params, new TextCallBack() {
                @Override
                protected void onSuccess(String text) {
                    ResultData<BaseUserInfo> mData = FromJsonUtils.fromJson(text, BaseUserInfo.class);
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            if (null != mData.getData())
                                showCallReminder(callSession, mData.getData(), isSecondPhone);
                        }
                    });
                }

                @Override
                protected void onFailure(ResponseException e) {
                }
            });
        }

    }

    public void showCallReminder(RCCallPlusSession callSession, BaseUserInfo userInfo, boolean isSecondPhone) {
        View rootView = LayoutInflater.from(mContext).inflate(R.layout.call_reminder_layout, null);
        AppCompatTextView mUserNameTv = rootView.findViewById(R.id.username_tv);
        AppCompatTextView mCallTypeTv = rootView.findViewById(R.id.call_type_tv);
        RoundedImageView mUserIconRiv = rootView.findViewById(R.id.user_riv);
        RoundedImageView mAnswerRiv = rootView.findViewById(R.id.answer_riv);

        PicassoUtils.showImage(mUserIconRiv, userInfo.getAvatar());
        mUserNameTv.setText(userInfo.getNickname());
        if (RCCallPlusMediaType.AUDIO == callSession.getMediaType()) {
            PicassoUtils.showImage(mAnswerRiv, R.mipmap.icon_jieshou_audio);
            mCallTypeTv.setText("邀请你进行语音通话…");
        } else {
            PicassoUtils.showImage(mAnswerRiv, R.mipmap.jieting);
            mCallTypeTv.setText("邀请你进行视频通话…");
        }
        rootView.findViewById(R.id.hang_up).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                SystemRoundUtils.cancelVibrator();
                SystemRoundUtils.stopRing();
                sendVideoMsg(new XinYouCallInfo("已拒绝", callSession.getMediaType().getValue() + "", "", mCallSession.getCallerUserId()), mCallSession.getCallerUserId());
                RCCallPlusClient.getInstance().hangup(callSession.getCallId());
                FloatWindow.destroy("CallReminderView");
            }
        });

        rootView.findViewById(R.id.answer_riv).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                SystemRoundUtils.cancelVibrator();
                SystemRoundUtils.stopRing();
                if (isSecondPhone) {
                    new XPopup.Builder(mContext)
                            .isDestroyOnDismiss(true) //对于只使用一次的弹窗，推荐设置这个
//                            .customAnimator(new RotateAnimator())
                            .asConfirm("通话提醒", "您当前正在通话中,是否接听当前来电?", new OnConfirmListener() {
                                @Override
                                public void onConfirm() {
                                    mCallSession = callSession;
                                    RCCallPlusClient.getInstance().accept(callSession.getCallId());
                                }
                            })
                            .show();

                } else {
                    EventBus.getDefault().postSticky(new RCCallPlusSessionEvent(callSession));
                    CallPhoneActivity.start(mContext, callSession.getCallId(), 1, callSession.getMediaType().getValue());
                    FloatWindow.destroy("CallPhoneVideo");
                }
                FloatWindow.destroy("CallReminderView");

            }
        });
        if (null == FloatWindow.get("CallReminderView")) {
            FloatWindow
                    .with(XYApplication.getAppApplicationContext())
                    .setView(rootView)
                    .setWidth(Screen.width, 1f)
                    .setDesktopShow(true)//桌面显示
                    .setMoveType(MoveType.slide)
                    .setPermissionListener(new PermissionListener() {
                        @Override
                        public void onSuccess() {

                        }

                        @Override
                        public void onFail() {

                        }
                    })
                    .setTag("CallReminderView")
                    .build();
        }
        FloatWindow.get("CallReminderView").show();
    }

    /**
     * 展示通知消息
     *
     * @param message
     */

    public void showNotification(Message message) {
        if (AppHelper.isActivityTop(ChatActivity.class, mContext)) {
            return;
        }
        BaseUserInfo userInfo = XYApplication.getDaoInstant().getBaseUserInfoDao().queryBuilder().where(BaseUserInfoDao.Properties.UserId.eq(message.getTargetId())).unique();
        if (null != userInfo) {
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    showNoticeMsg(userInfo, message);
                }
            });
        } else {
            HashMap<String, Object> userParams = new HashMap<>();
            userParams.put("userId", message.getTargetId());
            HttpUtils.get(Common.APP_GET_CHAT_RY_USERINFO, userParams, new TextCallBack() {
                @Override
                protected void onSuccess(String text) {

                    ResultData<BaseUserInfo> mData = FromJsonUtils.fromJson(text, BaseUserInfo.class);

                    if (null != mData && null != mData.getData()) {
                        XYApplication.getDaoInstant().getBaseUserInfoDao().insertOrReplace(mData.getData());
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                showNoticeMsg(mData.getData(), message);
                            }
                        });
                    }
                }

                @Override
                protected void onFailure(ResponseException e) {
                    LogUtil.e("响应结果", "==========" + e.toString());
                }
            });
        }
    }

    /**
     * 发送视频消息
     *
     * @param callInfo
     * @param userId
     */
    public void sendVideoMsg(XinYouCallInfo callInfo, String userId) {
        Conversation.ConversationType conversationType = Conversation.ConversationType.PRIVATE;
        XYCallVideoContent messages = XYCallVideoContent.obtain(callInfo);
        Message message = Message.obtain(userId, conversationType, messages);
        message.setCanIncludeExpansion(true);
        RongIM.getInstance().sendMessage(message, null, null, new IRongCallback.ISendMediaMessageCallback() {
            @Override
            public void onProgress(Message message, int i) {

            }

            @Override
            public void onCanceled(Message message) {

            }

            @Override
            public void onAttached(Message message) {

            }

            @Override
            public void onSuccess(Message message) {

            }

            @Override
            public void onError(final Message message, final RongIMClient.ErrorCode errorCode) {

            }
        });
    }

    /**
     * 消息通知
     *
     * @param userInfo
     * @param message
     */
    private void showNoticeMsg(BaseUserInfo userInfo, Message message) {
        View rootView = LayoutInflater.from(mContext).inflate(R.layout.overlay_layout, null);
        AppCompatTextView messageContent = rootView.findViewById(R.id.call_type_tv);
        AppCompatTextView userName = rootView.findViewById(R.id.username_tv);
        RoundedImageView userIcon = rootView.findViewById(R.id.user_riv);

        rootView.findViewById(R.id.chat_button).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                // 默认拉取历史消息数量
                RongConfigCenter.conversationConfig().setConversationHistoryMessageCount(10);
                RouteUtils.routeToConversationActivity(mContext, Conversation.ConversationType.PRIVATE, message.getTargetId(), null);
                FloatWindow.destroy("topNotice");
            }
        });

        if (message.getMessageDirection() == Message.MessageDirection.RECEIVE) {
            LogUtil.e("newMsg", "新消息--》" + message.toString());
            if (TextUtils.equals("RC:TxtMsg", message.getObjectName())) {
                messageContent.setText(((TextMessage) message.getContent()).getContent());
            } else if (TextUtils.equals("app:callvideocontent", message.getObjectName())) {
                messageContent.setText(((XYCallVideoContent) message.getContent()).getContent());
            } else if (TextUtils.equals("app:giftcontent", message.getObjectName())) {
                messageContent.setText(((XYGiftContent) message.getContent()).getContent());
            } else if (TextUtils.equals("RC:ImgMsg", message.getObjectName())) {
                messageContent.setText("[图片]");
            } else if (TextUtils.equals("RC:FileMsg", message.getObjectName())) {
                messageContent.setText("[文件]");
            } else if (TextUtils.equals("RC:SightMsg", message.getObjectName())) {
                messageContent.setText("[视频]");
            }

            userName.setText(userInfo.getNickname());
            PicassoUtils.showImage(userIcon, userInfo.getAvatar());

            if (null == FloatWindow.get("topNotice")) {
                FloatWindow
                        .with(XYApplication.getAppApplicationContext())
                        .setView(rootView)
                        .setWidth(Screen.width, 1f)
                        .setDesktopShow(true)//桌面显示
                        .setMoveType(MoveType.back)
                        .setTag("topNotice")
                        .build();
            }
            FloatWindow.get("topNotice").show();

            new Thread(new Runnable() {
                @Override
                public void run() {
                    try {
                        Thread.sleep(3000);//3秒后弹框消失
                        FloatWindow.destroy("topNotice");
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
            }).start();
        }
    }

    /**
     * @param callType 0语音  1视频
     */
    public void showCallPhone(int callType, int enterType) {
        View rootView = LayoutInflater.from(mContext).inflate(R.layout.view_call_phone_layout, null);
        FrameLayout myRemote = rootView.findViewById(R.id.remote);//我的视频
        LinearLayout yuYinLl = rootView.findViewById(R.id.yuyin_layout);//语音布局
        if (0 == callType) {
            yuYinLl.setVisibility(View.VISIBLE);
            myRemote.setVisibility(View.GONE);
        } else {
            yuYinLl.setVisibility(View.GONE);
            myRemote.setVisibility(View.VISIBLE);
            RCCallPlusClient.getInstance().startCamera();
            //创建本地视图对象
            RCCallPlusLocalVideoView localVideoView = new RCCallPlusLocalVideoView(mContext);
            //FIT: 视频帧通过保持宽高比(可能显示黑色边框)来缩放以适应视图的大小
            localVideoView.setRenderMode(RCCallPlusRenderMode.FIT);
            //设置本地视图给SDK
            RCCallPlusClient.getInstance().setVideoView(localVideoView);

            FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(FrameLayout.LayoutParams.MATCH_PARENT, FrameLayout.LayoutParams.MATCH_PARENT);
            params.gravity = Gravity.CENTER_HORIZONTAL;//在父布局中横向居中显示
            //将本地视图添加到XML中显示
            //示例代码中 mLocalVideoViewFrameLayout 为 android.widget.FrameLayout 对象
            myRemote.removeAllViews();
            myRemote.addView(localVideoView, params);
        }

        RCCallPlusClient.getInstance().enableMicrophone(true);

        rootView.findViewById(R.id.rootView).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                EventBus.getDefault().postSticky(new SmallCallPlusSessionEvent(mCallSession));
//                CallPhoneActivity.start(mContext, callType, enterType);
                FloatWindow.destroy("CallPhoneVideo");
            }
        });
        RCCallPlusClient.getInstance().setCallPlusResultListener(new IRCCallPlusResultListener() {

            /**
             * 发起通话方法结果回调
             *
             * @param code 方法请求结果
             * @param callId 通话Id
             * @param busyUserList 呼叫成功后，返回被邀请人列表中的忙线用户列表
             */
            @Override
            public void onStartCall(RCCallPlusCode code, String callId, List<RCCallPlusUser> busyUserList) {
                LogUtil.e(TAG, "onStartCall--->code:" + code);
                IRCCallPlusResultListener.super.onStartCall(code, callId, busyUserList);
            }

            /**
             * 接听通话结果回调
             *
             * @param code 方法请求结果
             * @param callId 通话Id
             */
            @Override
            public void onAccept(RCCallPlusCode code, String callId) {
                IRCCallPlusResultListener.super.onAccept(code, callId);
                EventBus.getDefault().postSticky(new RCCallPlusSessionEvent(mCallSession));
                CallPhoneActivity.start(mContext, callId, 1, mCallSession.getMediaType().getValue());
                FloatWindow.destroy("CallPhoneVideo");
                LogUtil.e(TAG, "onAccept--->code:" + code + "----" + callId);
            }

            /**
             * 挂断指定通话结果回调
             *
             * @param code 方法请求结果
             * @param callId 通话Id
             */
            @Override
            public void onHangup(RCCallPlusCode code, String callId) {
                IRCCallPlusResultListener.super.onHangup(code, callId);
                LogUtil.e(TAG, "onHangup--->code:" + code + "----" + callId);
            }
        });


        RCCallPlusClient.getInstance().setCallPlusEventListener(new IRCCallPlusEventListener() {
            @Override
            public void onCallStartTimeFromServer(long callStartTime) {
                LogUtil.e(TAG, "callStartTime--->" + callStartTime);
            }

            @Override
            public void onCallConnected(RCCallPlusSession callSession) {
                LogUtil.i(TAG, "onCallConnected，，，，");
                mCallSession = callSession;
//                if (AppHelper.isActivityTop(CallPhoneActivity.class, mContext)) {
//                    EventBus.getDefault().post(new SmallCallPlusSessionEvent(mCallSession));
//                } else {
//                    EventBus.getDefault().postSticky(new SmallCallPlusSessionEvent(mCallSession));
//                    CallPhoneActivity.start(mContext, mCallSession.getCallId(), 1, mCallSession.getMediaType().getValue());
//                }
            }

            /**
             * 本端用户通过该回调接收到通话呼叫
             *
             * @param callSession 通话实体信息
             */
            @Override
            public void onReceivedCall(RCCallPlusSession callSession, String extra) {
                RCCallPlusSession currentCallSession = RCCallPlusClient.getInstance().getCurrentCallSession();
                if (currentCallSession != null && !TextUtils.equals(callSession.getCallId(), currentCallSession.getCallId())) {
                    LogUtil.i(TAG, "onReceivedCall，，，，");
                    showCallReminderUserInfo(callSession, true);
                }
            }

            @Override
            public void onCallEnded(RCCallPlusSession session, RCCallPlusReason reason) {
                IRCCallPlusEventListener.super.onCallEnded(session, reason);
                LogUtil.i(TAG, "onCallEndedId->" + session.getCallId()+"-------mCurrentCallId->"+mCallSession.getCallId());
                if (mCallSession.getCallId() == session.getCallId()) {
                    FloatWindow.destroy("CallPhoneVideo");
                    return;
                }
                RCCallPlusClient.getInstance().stopCamera();
                RCCallPlusClient.getInstance().enableMicrophone(false);
            }

            /**
             * 远端用户状态改变监听
             *
             * @param callId 通话Id
             * @param userId 用户Id
             * @param status 该用户当前状态
             * @param reason 该用户当前状态原因
             */
            @Override
            public void onRemoteUserStateChanged(String callId, String userId, RCCallPlusUserSessionStatus status, RCCallPlusReason reason) {
                IRCCallPlusEventListener.super.onRemoteUserStateChanged(callId, userId, status, reason);
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        StringBuilder stringBuilder = new StringBuilder("通话 ");
                        stringBuilder.append(" 对方 ");
                        switch (status) {
                            case IDLE:
                                stringBuilder.append("空闲");
                                break;
                            case CALLING:
                                stringBuilder.append("呼叫中");
                                break;
                            case INVITED:
                                stringBuilder.append("被邀请中");
                                break;
                            case RINGING:
                                stringBuilder.append("响铃中");
                                break;
                            case BUSY_LINE_RINGING:
                                stringBuilder.append("忙线(响铃中)");
                                break;
                            case BUSY_LINE_WAIT:
                                stringBuilder.append("忙线(通话中)");
                                break;
                            case CONNECTING:
                                stringBuilder.append("已接听，连接中");
                                break;
                            case ON_CALL:
                                stringBuilder.append("通话中");
                                break;
                            case ENDED:
                                stringBuilder.append("通话已结束");
                                break;
                            case NO_ANSWER:
                                stringBuilder.append("未应答");
                                break;
                            case MISSED:
                                stringBuilder.append("未接听");
                                break;
                            case CANCELED:
                                stringBuilder.append("已取消");
                                break;
                            case DECLINED:
                                stringBuilder.append("已拒绝");
                                break;
                            case ERROR:
                                stringBuilder.append("错误");
                                break;
                        }
//                        Toaster.show(stringBuilder.toString());
                        LogUtil.i(TAG, stringBuilder.toString());
                    }
                });
            }
        });

        if (null == FloatWindow.get("CallPhoneVideo")) {
            FloatWindow
                    .with(XYApplication.getAppApplicationContext())
                    .setView(rootView)
                    .setWidth(Screen.width, 1f)
                    .setDesktopShow(false)
                    .setMoveType(MoveType.slide)
                    .setTag("CallPhoneVideo")
                    .build();
        }
        FloatWindow.get("CallPhoneVideo").show();
    }
}
