package com.chat.laty.manager;

import android.content.Context;
import android.text.TextUtils;

import com.hjq.toast.Toaster;
import com.chat.laty.activity.CallPhoneActivity;
import com.chat.laty.activity.ReceivedActivity;
import com.chat.laty.base.XYApplication;
import com.chat.laty.entity.FatePairingInfo;
import com.chat.laty.entity.event.FatePairEvent;
import com.chat.laty.entity.event.RCHangUpSessionEvent;
import com.chat.laty.entity.event.RCReceiveCallPlusSessionEvent;
import com.chat.laty.entity.event.RYCallConnectedEvent;
import com.chat.laty.utils.BgmPlayer;
import com.chat.laty.utils.LogUtil;
import com.chat.laty.utils.SystemRoundUtils;
import com.chat.laty.utils.XYSPUtils;
import com.chat.laty.utils.XYVideoUtils;
import com.chat.laty.utils.net.Common;
import com.chat.laty.utils.net.GsonUtils;
import com.chat.laty.utils.net.HttpUtils;
import com.chat.laty.utils.net.TextCallBack;

import org.greenrobot.eventbus.EventBus;

import java.util.HashMap;
import java.util.List;

import cn.rongcloud.callplus.api.RCCallPlusClient;
import cn.rongcloud.callplus.api.RCCallPlusCode;
import cn.rongcloud.callplus.api.RCCallPlusReason;
import cn.rongcloud.callplus.api.RCCallPlusSession;
import cn.rongcloud.callplus.api.RCCallPlusUser;
import cn.rongcloud.callplus.api.callback.IRCCallPlusEventListener;
import cn.rongcloud.callplus.api.callback.IRCCallPlusResultListener;

import static cn.xjc_soft.lib_utils.ThreadUtils.runOnUiThread;

public class RYCallPlusManager {
    private static final String TAG = "RYCallPlusManager";

    private static RYCallPlusManager manager;

    public static RYCallPlusManager getInstance() {
        if (manager == null) {
            manager = new RYCallPlusManager();
        }
        return manager;
    }

    private RYCallPlusManager() {
    }

    public void showCallReminderView() {
    }

    /**
     * 通话时间
     */
    private long callStartTime = -1;


    public long getCallStartTime() {
        return callStartTime;
    }

    public long getCallTime() {
        if (callStartTime <= -1) {
            return -1;
        }
        return System.currentTimeMillis() - callStartTime;
    }

//
//    public void showCallReminderView(String userId, int enterType, int callType){
//        View rootView = new CallReminderView(mContext);
//        rootView.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View view) {
//                CallPhoneActivity.start(mContext,userId,enterType,callType);
//            }
//        });
//        if (null == FloatWindow.get("CallReminderView")) {
//            FloatWindow
//                    .with(XYApplication.getAppApplicationContext())
//                    .setView(rootView)
////                            .setHeight(Screen.width, 0.2f)
//                    .setX(100)//设置控件初始位置
//                    .setY(Screen.height, 0.3f)
//                    .setDesktopShow(true)//桌面显示
//                    .setMoveType(MoveType.slide)
//                    .setTag("CallReminderView")
//                    .build();
//        } else {
//            FloatWindow.get("CallReminderView").show();
//        }
//>>>>>>> master
//    }

    int enterType = 0;
    String mExtra = "";
    Context context;

    public void performBackgroundTask(Context context) {
        this.context = context;
        RCCallPlusClient.getInstance().setCallPlusEventListener(phoneCallListener);
        RCCallPlusClient.getInstance().setCallPlusResultListener(new IRCCallPlusResultListener() {

            /**
             * 发起通话方法结果回调
             *
             * @param code 方法请求结果
             * @param callId 通话Id
             * @param busyUserList 呼叫成功后，返回被邀请人列表中的忙线用户列表
             */
            @Override
            public void onStartCall(RCCallPlusCode code, String callId, List<RCCallPlusUser> busyUserList) {
                IRCCallPlusResultListener.super.onStartCall(code, callId, busyUserList);
                enterType = 0;
                LogUtil.i(TAG, "onStartCall--->code:" + code + "----" + callId);
            }

            /**
             * 接听通话结果回调
             *
             * @param code 方法请求结果
             * @param callId 通话Id
             */
            @Override
            public void onAccept(RCCallPlusCode code, String callId) {
                IRCCallPlusResultListener.super.onAccept(code, callId);
                LogUtil.i(TAG, "onAccept--->code:" + code + "----" + callId);
                enterType = 1;
            }

        });
//        RongCoreClient.addOnReceiveMessageListener(receiveMsg);
    }


    IRCCallPlusEventListener phoneCallListener = new IRCCallPlusEventListener() {


        @Override
        public void onFirstRemoteVideoFrame(String userId, int width, int height) {
            IRCCallPlusEventListener.super.onFirstRemoteVideoFrame(userId, width, height);
        }

        @Override
        public void onReceivedCall(RCCallPlusSession callSession, String extra) {
//            LogUtil.i(TAG, "onReceivedCall，，，，" + new Gson().toJson(callSession));
            XYVideoUtils.setWaitSession(callSession);
            LogUtil.i(TAG, "onReceivedCall，，，，");
            // 来电
            LogUtil.e(TAG, "呼叫ID--->onReceivedCall:::" + callSession.getCallId());
            LogUtil.e(TAG, "呼叫ID--->extra-->" + extra);


            if (!TextUtils.isEmpty(extra)) {
                mExtra = extra;
//                EventBus.getDefault().post(new FateSessionEvent(extra));
                FatePairingInfo base = GsonUtils.JsonToBean(extra, FatePairingInfo.class);
                LogUtil.e(TAG, "FatePairingInfo-->" + base.toString());
                if (null != base && TextUtils.equals("1", base.getType())) {

                    HashMap<String, Object> params = new HashMap<>();
                    params.put("userId", base.getUserId());
                    params.put("toUserId", XYSPUtils.getString(Common.KEY_APP_USER_RY_ID));
                    params.put("roomId", callSession.getCallId());
                    HttpUtils.getResult(Common.APP_GET_CHAT_SUBMIT_ROOMINFO, params, new TextCallBack() {
                        @Override
                        protected void onSuccess(String text) {
                            LogUtil.e("响应结果", "==========上传房间信息-->" + text);
                            Toaster.show("配对成功");
                            EventBus.getDefault().post(new FatePairEvent(false));
                            runOnUiThread(new Runnable() {
                                public void run() {
                                    String callId = callSession.getCallId();
                                    SystemRoundUtils.cancelVibrator();
                                    SystemRoundUtils.stopRing();
                                    RCCallPlusClient.getInstance().accept(callId);
                                }
                            });
                        }

                        @Override
                        protected void onFailure(ResponseException e) {
                            LogUtil.e("响应结果", "onFailure==========" + e.toString());
                        }
                    });

                }
            } else {
                XYVideoUtils.setWaitSession(callSession);
                XYVideoUtils.waitSessionExtra = extra;
                SystemRoundUtils.playRing(context);
                SystemRoundUtils.setVibrator();
                ReceivedActivity.start(context);
                EventBus.getDefault().postSticky(new RCReceiveCallPlusSessionEvent(callSession, extra));
            }
        }

        @Override
        public void onCallConnected(RCCallPlusSession callSession) {
            IRCCallPlusEventListener.super.onCallConnected(callSession);
            BgmPlayer mBgmPlayer = BgmPlayer.getInstance(XYApplication.getAppApplicationContext());
            mBgmPlayer.stop();
//            if(XYVideoUtils.session != null) {
//                RCCallPlusClient.getInstance().hangup(XYVideoUtils.session.getCallId());
//            }
            if (callSession != null) {
                LogUtil.i(TAG, "onCallConnected，，，，" + callSession.getCallId());
            } else {
                LogUtil.i(TAG, "onCallConnected，，，，");
            }

            if (callSession != null) {

                if (XYVideoUtils.getWaitSession() != null && TextUtils.equals(XYVideoUtils.getWaitSession().getCallId(), callSession.getCallId())) {
                    XYVideoUtils.setWaitSession(null);
                }

                XYVideoUtils.callStartTime = System.currentTimeMillis();
                XYVideoUtils.session = callSession;
                XYVideoUtils.enterType = enterType;

                if (enterType == 1) {
//                    CallPhoneActivityV2.openAct(context);
                    RCCallPlusSession currentCallSession = callSession;
//                    RCCallPlusClient.getInstance().hangup(currentCallSession.getCallId());
                    CallPhoneActivity.start(context, currentCallSession.getCallId(), 1, currentCallSession.getMediaType().getValue(), mExtra);
                }
            }

            EventBus.getDefault().post(new RYCallConnectedEvent(callSession));
        }

        @Override
        public void onCallEnded(RCCallPlusSession session, RCCallPlusReason reason) {
//            LogUtil.i(TAG, "onCallEnded，，，，" + session );
            BgmPlayer mBgmPlayer = BgmPlayer.getInstance(XYApplication.getAppApplicationContext());
            mBgmPlayer.stop();
            LogUtil.i(TAG, "onCallEnded，，，，");
            LogUtil.e(TAG, "挂断ID--->" + session.getCallId());
            SystemRoundUtils.cancelVibrator();
            SystemRoundUtils.stopRing();

            if (XYVideoUtils.session != null && (TextUtils.equals(XYVideoUtils.session.getCallId(), session.getCallId()))) {
                RCCallPlusClient.getInstance().enableMicrophone(false);
                RCCallPlusClient.getInstance().stopCamera();
                XYVideoUtils.callStartTime = -1;
                XYVideoUtils.session = null;
            }


            if (XYVideoUtils.getWaitSession() != null && TextUtils.equals(XYVideoUtils.getWaitSession().getCallId(), session.getCallId())) {
                RCCallPlusClient.getInstance().enableMicrophone(false);
                RCCallPlusClient.getInstance().stopCamera();
                XYVideoUtils.setWaitSession(null);
            }


//            if (XYVideoUtils.session != null) {
//                CallPhoneActivity.start(context, XYVideoUtils.session.getCallId(), enterType, XYVideoUtils.session.getMediaType().getValue());
//            }
            RCCallPlusClient.getInstance().stopCamera();
            EventBus.getDefault().post(new RCHangUpSessionEvent(session));
        }
    };
}
