package com.chat.laty;

import android.annotation.SuppressLint;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothHeadset;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.BitmapFactory;
import android.os.Build;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.core.app.NotificationCompat;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;

import com.amap.api.location.AMapLocationClient;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.bitmap.CircleCrop;
import com.bumptech.glide.request.RequestOptions;
import com.google.android.material.snackbar.Snackbar;
import com.google.gson.Gson;
import com.hjq.toast.Toaster;
import com.makeramen.roundedimageview.RoundedImageView;
import com.next.easynavigation.view.EasyNavigationBar;
import com.chat.laty.activity.ChatActivity;
import com.chat.laty.activity.LoginByWeiChatUI;
import com.chat.laty.base.XYApplication;
import com.chat.laty.contants.Constant;
import com.chat.laty.controllers.MainPageController;
import com.chat.laty.dialog.BottomReceiveCallDialog;
import com.chat.laty.entity.BaseUserInfo;
import com.chat.laty.entity.RYTokenInfo;
import com.chat.laty.entity.event.CallPlushListenerEvent;
import com.chat.laty.entity.event.ClearMsgEvent;
import com.chat.laty.entity.event.MainTabChange;
import com.chat.laty.entity.event.RYTokenEvent;
import com.chat.laty.fragment.CommunityFragment;
import com.chat.laty.fragment.FirstPageFragment;
import com.chat.laty.fragment.MessageFragment;
import com.chat.laty.fragment.MineFragment;
import com.chat.laty.greenDao.BaseUserInfoDao;
import com.chat.laty.im.message.XYCallVideoContent;
import com.chat.laty.im.message.XYGiftContent;
import com.chat.laty.manager.RYCallPlusManager;
import com.chat.laty.mvp.BasePresenterActivity;
import com.chat.laty.presenters.MainPagePresenter;
import com.chat.laty.service.BluetoothReceiver;
import com.chat.laty.service.HeadsetReceiver;
import com.chat.laty.utils.Badger;
import com.chat.laty.utils.FromJsonUtils;
import com.chat.laty.utils.LogUtil;
import com.chat.laty.utils.PicassoUtils;
import com.chat.laty.utils.ResultData;
import com.chat.laty.utils.XYSPUtils;
import com.chat.laty.utils.net.Common;
import com.chat.laty.utils.net.HttpUtils;
import com.chat.laty.utils.net.TextCallBack;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import butterknife.BindView;
import cn.rongcloud.rtc.api.RCRTCAudioRouteManager;
import cn.rongcloud.rtc.api.RCRTCEngine;
import cn.xjc_soft.lib_utils.LibCollections;
import io.rong.common.rlog.RLog;
import io.rong.imkit.GlideKitImageEngine;
import io.rong.imkit.MessageInterceptor;
import io.rong.imkit.RongIM;
import io.rong.imkit.config.RongConfigCenter;
import io.rong.imkit.manager.UnReadMessageManager;
import io.rong.imkit.utils.RouteUtils;
import io.rong.imlib.IRongCoreListener;
import io.rong.imlib.RongCoreClient;
import io.rong.imlib.RongIMClient;
import io.rong.imlib.listener.OnReceiveMessageWrapperListener;
import io.rong.imlib.model.BlockedMessageInfo;
import io.rong.imlib.model.Conversation;
import io.rong.imlib.model.ConversationIdentifier;
import io.rong.imlib.model.Message;
import io.rong.imlib.model.MessageContent;
import io.rong.imlib.model.ReceivedProfile;
import io.rong.message.TextMessage;

public class MainActivity extends BasePresenterActivity<MainPagePresenter> implements MainPageController.View {

    @BindView(R.id.navigationBar)
    EasyNavigationBar mEasyNavigationBar;

    private String[] tabText = {"首页", "动态", "消息", "我的"};

    private int[] selectIcon = {R.mipmap.icon_firstpage_select, R.mipmap.icon_shequ_select, R.mipmap.icon_message_select, R.mipmap.icon_mine_select};
    //
//    //未选中icon
    private int[] normalIcon = {R.mipmap.icon_firstpage_normal, R.mipmap.icon_shequ_normal, R.mipmap.icon_message_normal, R.mipmap.icon_mine_normal};

    private List<Fragment> fragments = new ArrayList<>();

    BottomReceiveCallDialog mBottomDialog;

    private List<Message> mHistoryMsg = new ArrayList<>();

    HeadsetReceiver mHeadsetReceiver;
    BluetoothReceiver mBluetoothReceiver;
    private static final String KEY_FIRST_NOTIFICATION_REQUEST = "first_notification_request";
    private static final int REQUEST_NOTIFICATION_PERMISSION = 1001;

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void getRequest(String event) {
//        RCCallPlusClient.getInstance().setCallPlusEventListener(eventListener);
        if ("connectIM".equals(event)) {
//            CallPlusHelper.INSTANCE.init(resultListener, eventListener);
            // 启动后台服务
//            Intent serviceIntent = new Intent(this, BackgroundService.class);
//            startService(serviceIntent);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void getRequest(CallPlushListenerEvent event) {
//        RCCallPlusClient.getInstance().setCallPlusEventListener(eventListener);
//        if (1 == event.getType()) {
////            CallPlusHelper.INSTANCE.init(resultListener, eventListener);
//            // 启动后台服务
//            Intent serviceIntent = new Intent(this, BackgroundService.class);
//            startService(serviceIntent);
//        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void getRequest(RYTokenEvent event) {
//        RCCallPlusClient.getInstance().setCallPlusEventListener(eventListener);
        // 启动后台服务
//        Intent serviceIntent = new Intent(this, BackgroundService.class);
//        startService(serviceIntent);
//        presenter.getRYToken();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void changeTab(MainTabChange event) {
        mEasyNavigationBar.selectTab(event.getTabPosition(), true);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void clearUnMsg(ClearMsgEvent event) {
        mEasyNavigationBar.setMsgPointCount(2, 0);
        Badger.updateBadgerCount(0, MainActivity.this);
    }

    @Override
    protected void initDatas() {
        RYCallPlusManager.getInstance().performBackgroundTask(this);
    }

    @Override
    protected int getContentViewId() {
        return R.layout.activity_main;
    }

    @RequiresApi(api = Build.VERSION_CODES.M)
    @Override
    protected void initViews() {
        mHistoryMsg.clear();
        fragments.add(new FirstPageFragment());//首页
//        fragments.add(new DiscoverFragment());//发现
        fragments.add(new CommunityFragment());//动态
        fragments.add(new MessageFragment());//消息
//        fragments.add(new ConversationListFragment());//消息
        fragments.add(new MineFragment());//我的
        initNavigationBar(tabText);

        if (XYApplication.appLogin(false)) {
//            CallPlusHelper.INSTANCE.init(resultListener, eventListener);
//            RCCallPlusClient.getInstance().setCallPlusEventListener(phoneCallListener);

//            RongConfigCenter.notificationConfig().setCategoryNotification(Notification.CATEGORY_NAVIGATION);
            // 启动后台服务
//            Intent serviceIntent = new Intent(this, BackgroundService.class);
//            startService(serviceIntent);
//            presenter.getRYToken();
            RongIM.setConnectionStatusListener(connectionStatusListener);
            RongIM.getInstance().setMessageInterceptor(messageInterceptor);
            RongIMClient.getInstance().setMessageBlockListener(blockListener);

//            RongIM.getInstance().addUnReadMessageCountChangedObserver(observer, Conversation.ConversationType.PRIVATE);

            RongCoreClient.addOnReceiveMessageListener(receiveMsg);

            // 初始化音频路由管理类
            RCRTCAudioRouteManager.getInstance().init(this);
            // 设置音频路由为扬声器
            RCRTCEngine.getInstance().enableSpeaker(true);
        }

        AMapLocationClient.updatePrivacyShow(MainActivity.this, true, true);
        AMapLocationClient.updatePrivacyAgree(MainActivity.this, true);

        RongConfigCenter.featureConfig().setKitImageEngine(new GlideKitImageEngine() {
            @Override
            public void loadConversationPortrait(@NonNull Context context, @NonNull String url, @NonNull ImageView imageView, Message message) {
                Glide.with(context).load(url).apply(RequestOptions.bitmapTransform(new CircleCrop())).into(imageView);
            }
        });

//        OpenInstall.init(this);
//        OpenInstall.getInstall(new AppInstallAdapter() {
//            @Override
//            public void onInstall(AppData appData) {
//                // 打印数据便于调试
//                Log.d("OpenInstall", "getInstall : installData = " + appData.toString());
////                Toast.makeText(getThisActivity(), "app传值--》" + appData.getData(), Toast.LENGTH_SHORT).show();
////                //  获取渠道编号参数
////                String channelCode = appData.getChannel();
////                // 获取自定义参数
////                String bindData = appData.getData();
////                yqm_stv.getRightTextView().setText(bindData);
//                presenter.downloadBinding(appData.getData());
////                presenter.downloadBinding("{\"code\":\"3432324\"}");
//            }
//        });


//        if (!PopBackgroundPermissionUtil.INSTANCE.hasPopupBackgroundPermission(this)) {
//            new XPopup.Builder(this)
//                    .isDestroyOnDismiss(true) //对于只使用一次的弹窗，推荐设置这个
//                    .customAnimator(new CallPhoneActivity.RotateAnimator())
//                    .asConfirm("温馨提示", "检测到您未开启后台弹出界面权限,请前往开启.不开启可能会导致语音视频弹窗弹出失败", new OnConfirmListener() {
//                        @Override
//                        public void onConfirm() {
//                            // 去开启
//                            //跳转应用消息，间接打开应用权限设置-效率高
//                            Intent intent = new Intent();
//                            intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
//                            Uri uri = Uri.fromParts("package", getPackageName(), null);
//                            intent.setData(uri);
//                            startActivity(intent);
//                        }
//                    })
//                    .show();
//        }

        // 注册
        IntentFilter filter = new IntentFilter(Intent.ACTION_HEADSET_PLUG);
        mHeadsetReceiver = new HeadsetReceiver();
        registerReceiver(mHeadsetReceiver, filter);

        // 在你的Activity或者其他组件中注册BroadcastReceiver
        IntentFilter filter2 = new IntentFilter(BluetoothHeadset.ACTION_CONNECTION_STATE_CHANGED);
        filter2.addAction(BluetoothDevice.ACTION_ACL_CONNECTED);
        filter2.addAction(BluetoothDevice.ACTION_ACL_DISCONNECTED);
        mBluetoothReceiver = new BluetoothReceiver();
        registerReceiver(mBluetoothReceiver, filter2);

        // 请求权限通知权限
        /*if (ContextCompat.checkSelfPermission(this, Manifest.permission.POST_NOTIFICATIONS)
                != PackageManager.PERMISSION_GRANTED) {

            ActivityCompat.requestPermissions(
                    this,
                    new String[]{Manifest.permission.POST_NOTIFICATIONS},
                    REQUEST_NOTIFICATION_PERMISSION
            );
        }*/
        /*XXPermissions.with(MainActivity.this).permission(Permission.READ_PHONE_STATE).interceptor(new PermissionInterceptor()).request(new OnPermissionCallback() {
            @Override
            public void onGranted(List<String> permissions, boolean all) {
                if (!all) {
                    return;
                }
            }
        });*/



        /*XXPermissions.with(MainActivity.this).permission(Permission.RECORD_AUDIO).permission(Permission.CAMERA).interceptor(new PermissionInterceptor()).request(new OnPermissionCallback() {
            @Override
            public void onGranted(List<String> permissions, boolean all) {
                if (!all) {
                    return;
                }
            }
        });*/

    }


    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        switch (requestCode) {
            case 1111:
                Log.i("MainActivity", String.format("requestCode--->%s, resultCode -> %s, data--->%s", requestCode, resultCode, new Gson().toJson(data)));
                break;
        }
    }

    private UnReadMessageManager.IUnReadMessageObserver observer = new UnReadMessageManager.IUnReadMessageObserver() {
        @Override
        public void onCountChanged(int i) {
            mEasyNavigationBar.setMsgPointCount(2, i);
            Badger.updateBadgerCount(i, MainActivity.this);
        }
    };


    private long currentReceiveTime;

    OnReceiveMessageWrapperListener receiveMsg = new OnReceiveMessageWrapperListener() {
        @Override
        public void onReceivedMessage(Message message, ReceivedProfile profile) {

            long INTERVAL = 5000;
            if (System.currentTimeMillis() - currentReceiveTime > INTERVAL) {
                showNotification(message);
                currentReceiveTime = System.currentTimeMillis();
            }

        }
    };


    IRongCoreListener.MessageBlockListener blockListener = new IRongCoreListener.MessageBlockListener() {
        @Override
        public void onMessageBlock(BlockedMessageInfo info) {
            Toaster.show("您发送的消息涉及违规内容");
        }
    };

    MessageInterceptor messageInterceptor = new MessageInterceptor() {
        @Override
        public boolean interceptReceivedMessage(Message message, int left, boolean hasPackage, boolean offline) {
            LogUtil.e("messageInterceptor", "interceptReceivedMessage-->" + message.toString());
            return false;
        }

        @Override
        public boolean interceptOnSendMessage(Message message) {
            LogUtil.e("messageInterceptor", "Main===message-->" + message.toString());
//            Toaster.show("余额不足，请充值!");
//            GoldCoinRechargeUI.start(MainActivity.this);
//            message.setCanIncludeExpansion(true);
//            presenter.getUserBalance();
//            String userSex = XYSPUtils.getString(Common.KEY_APP_USER_GENDER);
//            if (TextUtils.equals("1", userSex)) {
//                BalanceInfo balanceInfo = XYApplication.getCurrentUserBalance();
//                if (balanceInfo.getGoldNum() <= 0) {
//                    Toaster.show("金币不足");
//                    sendEmptyGoldMsg(message.getTargetId(), balanceInfo);
//                    return true;
//                }
//
//            } else {
//                RyUserInfo currentUserInfo = XYApplication.getCurrentUserInfo();
//                if (!TextUtils.equals("1", currentUserInfo.getIsName())) {
//                    Toaster.show("请先完成个人认证");
//                    return true;
//                }
//                if (!TextUtils.equals("1", currentUserInfo.getIsReal())) {
//                    Toaster.show("还未认证");
//                    return true;
//                }
//            }
            return false;
        }

        @Override
        public boolean interceptOnSentMessage(Message message) {
            LogUtil.e("messageInterceptor", "interceptOnSentMessage-->" + message.toString());
            return false;
        }

        @Override
        public boolean interceptOnInsertOutgoingMessage(Conversation.ConversationType type, String targetId, Message.SentStatus sentStatus, MessageContent content, long sentTime) {
            LogUtil.e("messageInterceptor", "interceptOnInsertOutgoingMessage-->" + content);
            return false;
        }

        @Override
        public boolean interceptOnInsertIncomingMessage(Conversation.ConversationType type, String targetId, String senderId, Message.ReceivedStatus receivedStatus, MessageContent content, long sentTime) {
            LogUtil.e("messageInterceptor", "interceptOnInsertIncomingMessage-->" + content);
            return false;
        }
    };

    private RongIMClient.ConnectionStatusListener connectionStatusListener = new RongIMClient.ConnectionStatusListener() {
        @Override
        public void onChanged(ConnectionStatus status) {
            LogUtil.e("onChanged", "连接状态status:" + status.getValue());
            //开发者需要根据连接状态码，进行不同业务处理
//            Toaster.show("连接状态status:" + status.getValue());
//            if (0 == status.getValue()) {
//                dismissProgressDialog();
//            }
            if (3 == status.getValue()) {
//                XYSPUtils.delete(Constant.KEY_USER_RONGYUN_TOKEN);
                Toaster.show("您的账号已在其他设备登录");
                XYSPUtils.clear(MainActivity.this);
                LoginByWeiChatUI.startNew(MainActivity.this);
                finish();
            } else if (0 != status.getValue() && 1 != status.getValue()) {
                presenter.getRYToken();
            }
        }
    };

    public static void start(Context context) {
        Intent starter = new Intent(context, MainActivity.class);
        starter.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK | Intent.FLAG_ACTIVITY_NEW_TASK);
        context.startActivity(starter);
    }


    private void initNavigationBar(String[] tabTs) {
        mEasyNavigationBar.titleItems(tabTs).normalIconItems(normalIcon).selectIconItems(selectIcon).fragmentList(fragments).iconSize(26).navigationBackground(getResources().getColor(R.color.color_white)).fragmentManager(getSupportFragmentManager()).setOnTabLoadListener(new EasyNavigationBar.OnTabLoadListener() {
            @Override
            public void onTabLoadCompleteEvent() {
                if (XYApplication.appLogin(false)) {
                    RongIM.getInstance().addUnReadMessageCountChangedObserver(observer, Conversation.ConversationType.PRIVATE);
                }

            }
        }).build();
    }


    public void showNotification(Message message) {
        if (message.getMessageDirection() == Message.MessageDirection.RECEIVE) {
            View customView = LayoutInflater.from(getThisActivity()).inflate(R.layout.overlay_layout, null);
            TextView chatBtn = customView.findViewById(R.id.chat_button);
            AppCompatTextView messageContent = customView.findViewById(R.id.call_type_tv);
            AppCompatTextView userName = customView.findViewById(R.id.username_tv);
            RoundedImageView userIcon = customView.findViewById(R.id.user_riv);
//        Button dismissButton = customView.findViewById(R.id.dismissButton);

            if (TextUtils.equals("RC:TxtMsg", message.getObjectName())) {
                messageContent.setText(((TextMessage) message.getContent()).getContent());
            } else if (TextUtils.equals("app:callvideocontent", message.getObjectName())) {
                messageContent.setText(((XYCallVideoContent) message.getContent()).getContent());
            } else if (TextUtils.equals("app:giftcontent", message.getObjectName())) {
                messageContent.setText(((XYGiftContent) message.getContent()).getContent());
            } else if (TextUtils.equals("RC:ImgMsg", message.getObjectName())) {
                messageContent.setText("[图片]");
            } else if (TextUtils.equals("RC:FileMsg", message.getObjectName())) {
                messageContent.setText("[文件]");
            } else if (TextUtils.equals("RC:SightMsg", message.getObjectName())) {
                messageContent.setText("[视频]");
            } else if (TextUtils.equals("RC:HQVCMsg", message.getObjectName())) {
                messageContent.setText("[语音]");
            }

            // Create a custom Snackbar
            Snackbar snackbar = Snackbar.make(getThisActivity().findViewById(android.R.id.content), "", Snackbar.LENGTH_INDEFINITE);

            // Set background color
            snackbar.getView().setBackgroundColor(getThisActivity().getResources().getColor(R.color.colorAccent));

            // Get the Snackbar layout
            @SuppressLint("RestrictedApi") Snackbar.SnackbarLayout snackbarLayout = (Snackbar.SnackbarLayout) snackbar.getView();

            // Remove default Snackbar TextView
            TextView snackbarTextView = snackbarLayout.findViewById(com.google.android.material.R.id.snackbar_text);
            snackbarTextView.setVisibility(View.INVISIBLE);

            // Add custom layout to Snackbar layout
            snackbarLayout.addView(customView, 0);

            BaseUserInfo userInfo = XYApplication.getDaoInstant().getBaseUserInfoDao().queryBuilder().where(BaseUserInfoDao.Properties.UserId.eq(message.getTargetId())).unique();

            if (null != userInfo) {
                new Thread(new Runnable() {
                    @Override
                    public void run() {
                        BaseUserInfo userInfo = XYApplication.getDaoInstant().getBaseUserInfoDao().queryBuilder().where(BaseUserInfoDao.Properties.UserId.eq(message.getTargetId())).unique();
                        if (null != userInfo) {
                            runOnUiThread(new Runnable() {
                                @Override
                                public void run() {
                                    PicassoUtils.showImage(userIcon, userInfo.getAvatar());
                                    userName.setText(userInfo.getNickname());
//                                LogUtil.i("sendMsg", "已经发送通知");
//                                sendNotification(messageContent.getText().toString(), userInfo.getNickname(), message.getTargetId());
//                                    Notification nof = getNotification(userInfo.getNickname(), messageContent.getText().toString());
//                                    notificationManager.notify(message.getMessageId(), nof);

                                    if (LibCollections.isEmpty(mHistoryMsg)) {
                                        mHistoryMsg.add(message);
                                        Notification nof = getNotification(userInfo.getNickname(), messageContent.getText().toString());
                                        notificationManager.notify(message.getMessageId(), nof);
                                    }
                                }
                            });

                        }
                    }
                }).start();
            } else {
                HashMap<String, Object> userParams = new HashMap<>();
                userParams.put("userId", message.getTargetId());
                HttpUtils.get(Common.APP_GET_CHAT_RY_USERINFO, userParams, new TextCallBack() {
                    @Override
                    protected void onSuccess(String text) {

                        ResultData<BaseUserInfo> mData = FromJsonUtils.fromJson(text, BaseUserInfo.class);

                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                if (null != mData.getData()) {
                                    XYApplication.getDaoInstant().getBaseUserInfoDao().insertOrReplace(mData.getData());
                                    PicassoUtils.showImage(userIcon, mData.getData().getAvatar());
                                    userName.setText(mData.getData().getNickname());
                                }

                            }
                        });
                    }

                    @Override
                    protected void onFailure(ResponseException e) {
                        LogUtil.e("响应结果", "==========" + e.toString());
                    }
                });
            }

            chatBtn.setOnClickListener(view -> {
                // 默认拉取历史消息数量
                RongConfigCenter.conversationConfig().setConversationHistoryMessageCount(10);
                RouteUtils.routeToConversationActivity(MainActivity.this, Conversation.ConversationType.PRIVATE, message.getTargetId(), null);
                snackbar.dismiss();
            });

            FrameLayout.LayoutParams params = (FrameLayout.LayoutParams) snackbar.getView().getLayoutParams();
            params.gravity = Gravity.TOP;
            snackbar.getView().setLayoutParams(params);
            snackbar.show();

            new Thread(new Runnable() {
                @Override
                public void run() {
                    try {
                        Thread.sleep(3000);//3秒后弹框消失
                        snackbar.dismiss();
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
            }).start();

        }

    }

    private final static String NOTIFICATION_CHANNEL_ID = "1001";
    private final static String NOTIFICATION_CHANNEL_NAME = "Service";
    private NotificationManager notificationManager;
    private NotificationChannel notificationChannel;


    private Notification getNotification(String title, String message) {
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            notificationChannel = new NotificationChannel(NOTIFICATION_CHANNEL_ID, NOTIFICATION_CHANNEL_NAME, NotificationManager.IMPORTANCE_HIGH);
            notificationChannel.setLockscreenVisibility(Notification.VISIBILITY_PUBLIC);
            notificationManager = (NotificationManager) getSystemService(NOTIFICATION_SERVICE);
            notificationManager.createNotificationChannel(notificationChannel);
        }


        //创建一个通知消息的构造器
        Notification.Builder builder = new Notification.Builder(this);
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            //Android8.0开始必须给每个通知分配对应的渠道
            builder = new Notification.Builder(this, NOTIFICATION_CHANNEL_ID);
        }

        try {
            ConversationIdentifier identifier = ConversationIdentifier.obtain(Conversation.ConversationType.PRIVATE, NOTIFICATION_CHANNEL_ID, "");
            Intent intent = new Intent(MainActivity.this, ChatActivity.class);
            intent.putExtra("targetId", identifier.getTargetId());
            intent.putExtra("ConversationType", identifier.getType().getName().toLowerCase());
            intent.putExtra("ConversationIdentifier", identifier);
            PendingIntent pendingIntent = PendingIntent.getActivity(this, 0, intent, 0);
            builder.setContentIntent(pendingIntent);
        } catch (Exception e) {

        }


        builder.setAutoCancel(false)//设置是否允许自动清除
                .setSmallIcon(R.mipmap.ic_launcher_app)//设置状态栏里的小图标
                .setWhen(System.currentTimeMillis())//设置推送时间，格式为"小时：分钟"
                .setContentTitle(title)//设置通知栏里面的标题文本
                .setContentText(message);//设置通知栏里面的内容文本
        //根据消息构造器创建一个通知对象
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.JELLY_BEAN) {
            Notification notify = builder.build();
            return notify;
        }

        return null;
    }

    private void sendNotification(String message, String nickname, String targetId) {
//        RouteUtils.routeToConversationActivity(MainActivity.this, Conversation.ConversationType.PRIVATE, message.getTargetId(), null);
        NotificationCompat.Builder builder = new NotificationCompat.Builder(this, nickname);
        Notification notification = builder.setContentTitle(nickname).setAutoCancel(true).setContentInfo(message).setWhen(System.currentTimeMillis()).setSmallIcon(R.mipmap.ic_launcher_app).setLargeIcon(BitmapFactory.decodeResource(getResources(), R.mipmap.ic_launcher_app)).build();

        NotificationManager notificationManager = (NotificationManager) getSystemService(NOTIFICATION_SERVICE);
        NotificationChannel channel = null;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            channel = new NotificationChannel(nickname, "测试渠道名称", NotificationManager.IMPORTANCE_DEFAULT);
            notificationManager.createNotificationChannel(channel);
            notificationManager.notify(1123, notification);
        }
//        RouteUtils.routeToConversationActivity(MainActivity.this, Conversation.ConversationType.PRIVATE, targetId, null);
        ConversationIdentifier identifier = ConversationIdentifier.obtain(Conversation.ConversationType.PRIVATE, targetId, "");
        if (identifier == null) {
            RLog.e("RouteUtils", "routeToConversationActivity: conversationIdentifier is empty");
        } else if (TextUtils.isEmpty(identifier.getTargetId())) {
            RLog.e("RouteUtils", "routeToConversationActivity: targetId is empty");
        } else if (identifier.getType() == null) {
            RLog.e("RouteUtils", "routeToConversationActivity: type is empty");
        } else {
            try {
                Intent intent = new Intent(MainActivity.this, ChatActivity.class);
                intent.putExtra("targetId", identifier.getTargetId());
                intent.putExtra("ConversationType", identifier.getType().getName().toLowerCase());
                intent.putExtra("ConversationIdentifier", identifier);
                PendingIntent pendingIntent = PendingIntent.getActivity(this, 0, intent, 0);
                builder.setContentIntent(pendingIntent);
            } catch (Exception e) {

            }

        }
    }

    //系统返回按钮方法重写
    private long currentTime;

    @SuppressLint("RestrictedApi")
    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        if (event.getAction() == KeyEvent.ACTION_DOWN && event.getKeyCode() == KeyEvent.KEYCODE_BACK) {
            long INTERVAL = 2000;    //两秒间隔内点击两次退出就能退出程序
            if (System.currentTimeMillis() - currentTime > INTERVAL) {
                Toaster.show("再次点击退出");
                currentTime = System.currentTimeMillis();
            } else {

                unregisterReceiver(mHeadsetReceiver);
                unregisterReceiver(mBluetoothReceiver);
                finish();
            }
            return true;
        }
        return super.dispatchKeyEvent(event);
    }

    @Override
    protected boolean isUseEventBus() {
        return true;
    }

    @Override
    protected MainPagePresenter setPresenter() {
        return new MainPagePresenter(this);
    }

    @Override
    public void showRyToken(RYTokenInfo data) {
        if (null != data && !TextUtils.isEmpty(data.getToken())) {
            XYSPUtils.put(Constant.KEY_USER_RONGYUN_TOKEN, data.getToken());
        }
    }

    //    private void sendNotification(String message, String nickname, String targetId) {
////        RouteUtils.routeToConversationActivity(MainActivity.this, Conversation.ConversationType.PRIVATE, message.getTargetId(), null);
//        NotificationCompat.Builder builder = new NotificationCompat.Builder(this, nickname);
//        Notification notification = builder
//                .setContentTitle(nickname)
//                .setAutoCancel(true)
//                .setContentInfo(message)
//                .setWhen(System.currentTimeMillis())
//                .setSmallIcon(R.mipmap.ic_launcher_app)
//                .setLargeIcon(BitmapFactory.decodeResource(getResources(), R.mipmap.ic_launcher_app))
//                .build();
//
//        NotificationManager notificationManager = (NotificationManager) getSystemService(NOTIFICATION_SERVICE);
//        NotificationChannel channel = null;
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
//            channel = new NotificationChannel(nickname, "测试渠道名称", NotificationManager.IMPORTANCE_DEFAULT);
//            notificationManager.createNotificationChannel(channel);
//            notificationManager.notify(1123, notification);
//        }
////        RouteUtils.routeToConversationActivity(MainActivity.this, Conversation.ConversationType.PRIVATE, targetId, null);
//        ConversationIdentifier identifier = ConversationIdentifier.obtain(Conversation.ConversationType.PRIVATE, targetId, "");
//        if (identifier == null) {
//            RLog.e("RouteUtils", "routeToConversationActivity: conversationIdentifier is empty");
//        } else if (TextUtils.isEmpty(identifier.getTargetId())) {
//            RLog.e("RouteUtils", "routeToConversationActivity: targetId is empty");
//        } else if (identifier.getType() == null) {
//            RLog.e("RouteUtils", "routeToConversationActivity: type is empty");
//        } else {
//            Intent intent = new Intent(MainActivity.this, RongConversationActivity.class);
//            intent.putExtra("targetId", identifier.getTargetId());
//            intent.putExtra("ConversationType", identifier.getType().getName().toLowerCase());
//            intent.putExtra("ConversationIdentifier", identifier);
//            PendingIntent pendingIntent = PendingIntent.getActivity(this, 0, intent, 0);
//            builder.setContentIntent(pendingIntent);
//        }
//    }
    @Override
    protected void onResume() {
        super.onResume();
//        if (XYVideoUtils.session != null)
//            XYVideoUtils.start(XYApplication.getAppApplicationContext(), CallPhoneActivity.mEnterType, CallPhoneActivity.mCallType, CallPhoneActivity.mUserId);
        if (XYApplication.appLogin(false)) {
            new Thread(new Runnable() {
                @Override
                public void run() {
                    RongIM.getInstance().addUnReadMessageCountChangedObserver(observer, Conversation.ConversationType.PRIVATE);
                }
            }).start();
            RongIMClient.ConnectionStatusListener.ConnectionStatus currentConnectionStatus = RongIM.getInstance().getCurrentConnectionStatus();

        }

        //        RYCallPlusManager.getInstance(MainActivity.this).showCallReminderView();
    }


    @Override
    public FragmentActivity context() {
        return this;
    }

    // Activity中
    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        // 获取到Activity下的Fragment
        List<Fragment> fragments = getSupportFragmentManager().getFragments();
        if (fragments == null) {
            return;
        }
        // 查找在Fragment中onRequestPermissionsResult方法并调用
        for (Fragment fragment : fragments) {
            if (fragment != null) {
                // 这里就会调用我们Fragment中的onRequestPermissionsResult方法
                fragment.onRequestPermissionsResult(requestCode, permissions, grantResults);
            }
        }
    }
}