<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.chat.laty">
    <!-- 音视频需要网络权限 和 监听网络状态权限 -->
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" /> <!-- 摄像头采集需要 -->
    <uses-permission android:name="android.permission.CAMERA" /> <!-- 音频采集需要 -->
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" /> <!-- 用于进行网络定位 -->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" /> <!-- 用于访问GPS定位 -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" /> <!-- 用于获取运营商信息，用于支持提供运营商信息相关的接口 -->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- 用于访问wifi网络信息，wifi信息会用于进行网络定位 -->
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" /> <!-- 用于获取wifi的获取权限，wifi信息会用来进行网络定位 -->
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" /> <!-- 用于访问网络，网络定位需要上网 -->
    <uses-permission android:name="android.permission.READ_PHONE_STATE" /> <!-- 用于写入缓存数据到扩展存储卡 -->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" /> <!-- 用于申请调用A-GPS模块 -->
    <uses-permission android:name="android.permission.ACCESS_LOCATION_EXTRA_COMMANDS" /> <!-- 如果设置了target >= 28 如果需要启动后台定位则必须声明这个权限 -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" /> <!-- 如果您的应用需要后台定位权限，且有可能运行在Android Q设备上,并且设置了target>28，必须增加这个权限声明 -->
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" /> <!-- 音频模式切换权限 -->
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
    <uses-permission android:name="android.permission.ACCESS_NOTIFICATION_POLICY" />

    <uses-permission android:name="android.permission.NEARBY_WIFI_DEVICES" android:usesPermissionFlags="neverForLocation" />
    <uses-permission android:name="android.permission.BLUETOOTH"/>
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN"/>


    <queries>
        <package android:name="com.tencent.mm" />
    </queries>

    <queries package="${applicationId}">
        <intent>
            <action android:name="android.media.action.IMAGE_CAPTURE">

            </action>
        </intent>
        <intent>
            <action android:name="android.media.action.ACTION_VIDEO_CAPTURE">

            </action>
        </intent>
    </queries>

    <application
        android:name="com.chat.laty.base.XYApplication"
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher_app"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:requestLegacyExternalStorage="true"
        android:roundIcon="@mipmap/ic_launcher_app"
        android:theme="@style/AppTheme">

        <meta-data
            android:name="ScopedStorage"
            android:value="true" />
        <meta-data
            android:name="design_width_in_dp"
            android:value="393" />
        <meta-data
            android:name="design_height_in_dp"
            android:value="699" />

        <meta-data
            android:name="com.openinstall.APP_KEY"
            android:value="vrd1gw"/>

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileProvider"

            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <service android:permission="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE"
            android:name="cn.xjc_soft.lib_utils.MessengerUtils$ServerService" />

        <service android:name="com.amap.api.location.APSService" />

        <service android:name="com.chat.laty.service.BackgroundService" />
        <service android:name="com.chat.laty.service.FatePairingBackgroundService" />
        <service android:name="com.chat.laty.service.FloatVideoWindowService" />

        <meta-data
            android:name="com.amap.api.v2.apikey"
            android:value="e410024b883cbcac7507cc999d356553" />


        <activity android:name="com.chat.laty.activity.test.TestAct"
            android:screenOrientation="portrait"
            android:exported="true">
        </activity>

        <activity
            android:name="com.yalantis.ucrop.UCropActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar"/>

        <activity
            android:name="com.chat.laty.activity.AppStartActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity android:name="com.chat.laty.MainActivity"
            android:screenOrientation="portrait"
            android:launchMode="singleTop"
            android:windowSoftInputMode="stateHidden|adjustPan"
            />
        <activity
            android:name="com.chat.laty.wxapi.WXEntryActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:exported="true"
            android:screenOrientation="portrait"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
        <activity
            android:name="com.chat.laty.wxapi.WXPayEntryActivity"
            android:exported="true"
            android:launchMode="singleTop">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />

                <data android:scheme="wx6ef1008649af4d0b" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.chat.laty.activity.LoginRegisterUI"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />

        <activity
            android:name="com.chat.laty.activity.VipCenterActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />

        <activity
            android:name="com.chat.laty.activity.VipCenter1Activity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />

        <activity
            android:name="com.chat.laty.activity.VipDetailActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />

        <activity
            android:name="com.chat.laty.activity.GoldCoinActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />

        <activity
            android:name="com.chat.laty.activity.GoldCoinDetailActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />

        <activity
            android:name="com.chat.laty.activity.MyCreditsActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />

        <activity
            android:name="com.chat.laty.activity.CreditsDetailActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />

        <activity
            android:name="com.chat.laty.activity.TaskCenterActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <activity
            android:name="com.chat.laty.activity.InviteActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <activity
            android:name="com.chat.laty.activity.InviteActivity1"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <activity
            android:name="com.chat.laty.activity.InviteBindSelfActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <activity
            android:name="com.chat.laty.activity.InviteBindRecordsActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />

        <activity
            android:name="com.chat.laty.activity.CreditsStoreActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />

        <activity
            android:name="com.chat.laty.activity.CreditsStoreDetailActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />

        <activity
            android:name="com.chat.laty.activity.CreditsRecordsActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />

        <activity
            android:name="com.chat.laty.activity.MyGroupActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <activity
            android:name="com.chat.laty.activity.MyMoreGroupActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <activity
            android:name="com.chat.laty.activity.MyGroupSearchActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />


        <activity
            android:name="com.chat.laty.activity.ChatActivity"
            android:exported="true"
            android:screenOrientation="portrait">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />

                <data
                    android:host="com.yiplus.xinyou"
                    android:pathPrefix="/conversation/"
                    android:scheme="rong" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.chat.laty.activity.DischargedPicBrowserActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <activity
            android:name="com.chat.laty.activity.VideoPlayActivity"
            android:launchMode="singleTask"
            android:configChanges="orientation|screenSize"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <activity
            android:name="com.chat.laty.activity.ImproveInformationUI"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <activity
            android:name="com.chat.laty.activity.GoldCoinRechargeUI"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
<!--        android:excludeFromRecents="true"
            android:keepScreenOn="true"
            android:launchMode="singleInstance"
            android:windowSoftInputMode="stateHidden|adjustPan"
            android:exported="true" -->
        <activity
            android:name="com.chat.laty.activity.CallPhoneActivity"
            android:exported="true"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.chat.laty.activity.MineDynamicReleaseUI"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <activity
            android:name="com.chat.laty.activity.MineDynamicDetailUI"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <activity
            android:name="com.chat.laty.activity.LoginByPhoneUI"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <activity
            android:name="com.chat.laty.activity.LoginByWeiChatUI"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <activity
            android:name="com.chat.laty.activity.ComplementUserInfoUI"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <activity
            android:name="com.chat.laty.activity.FatePairingUI"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <activity
            android:name="com.chat.laty.activity.UserCenterUI"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <activity
            android:name="com.chat.laty.activity.WebViewActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize|adjustPan" />
        <activity
            android:name="com.chat.laty.activity.WebViewActivity2"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize|adjustPan" />
        <activity
            android:name="com.chat.laty.activity.FullImagePreActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <activity
            android:name="com.chat.laty.activity.GeneralSettingsUI"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <activity
            android:name="com.chat.laty.activity.AccostLanguageSettingUI"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <activity
            android:name="com.chat.laty.activity.AddAccostLanguageUI"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <activity
            android:name="com.chat.laty.activity.SettingActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <activity
            android:name="com.chat.laty.activity.AgreementUI"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <activity
            android:name="com.chat.laty.activity.DisturbSettingsUI"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <activity
            android:name="com.chat.laty.activity.CostSettingUI"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <activity
            android:name="com.chat.laty.activity.AccostLanguageListUI"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <activity
            android:name="com.chat.laty.activity.ReceivedActivity"
            android:exported="true"
            android:screenOrientation="portrait"
            android:theme="@style/TransparentActivity"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <activity
            android:name="com.chat.laty.activity.AcountSettingUI"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <activity
            android:name="com.chat.laty.activity.CancelAcountSettingUI"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />

        <activity
            android:name="com.chat.laty.activity.MyAuthenticationUI"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />

        <activity
            android:name="com.chat.laty.activity.MyShiMinAuthenticationUI"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />

        <activity
            android:name="com.chat.laty.activity.MyRealPersonAuthenticationUI"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />

        <activity
            android:name="com.chat.laty.activity.UserAddAudioSignatureUI"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />

        <activity
            android:name="com.chat.laty.activity.MyRewardsActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />

        <activity
            android:name="com.chat.laty.activity.BeautySetUI"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />

        <activity
            android:name="com.chat.laty.activity.MasonryDetailActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />

        <activity
            android:name="com.chat.laty.activity.VisitorMeUI"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <activity
            android:name="com.chat.laty.activity.SystemMsgActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <activity
            android:name="com.chat.laty.activity.LikeMsgActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <activity
            android:name="com.chat.laty.activity.ReportMsgActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <activity
            android:name="com.chat.laty.activity.UserDynamicsActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <activity
            android:name="com.chat.laty.activity.ReportUserActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <activity
            android:name="com.chat.laty.activity.BlacklistActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <activity
            android:name="com.chat.laty.activity.AppShowOnePicActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <activity
            android:name="com.chat.laty.activity.SiMiPhotoActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <activity
            android:name="com.chat.laty.activity.SiMiVideoActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <receiver
            android:name="com.chat.laty.service.XYCustomMessageReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="io.rong.push.intent.MESSAGE_ARRIVED" />
                <action android:name="io.rong.push.intent.MESSAGE_CLICKED" />
                <action android:name="io.rong.push.intent.THIRD_PARTY_PUSH_STATE" />
            </intent-filter>
        </receiver>

        <receiver android:name="com.chat.laty.service.HeadsetReceiver" android:exported="true" android:enabled="true"/>
        <receiver android:name="com.chat.laty.service.BluetoothReceiver" android:exported="true" android:enabled="true"/>

    </application>

</manifest>