<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@mipmap/home_bg"
    android:fitsSystemWindows="true">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/app_bar_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#00000000"
        app:elevation="0dp">

<!--        <com.google.android.material.appbar.CollapsingToolbarLayout-->
<!--            android:id="@+id/collapsing_toolbar"-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="wrap_content"-->
<!--            app:layout_scrollFlags="scroll|exitUntilCollapsed">-->

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_scrollFlags="scroll">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:layout_marginHorizontal="15dp"
                    android:layout_marginTop="@dimen/dp_13"
                    android:background="@drawable/shape_main_search_bg"
                    android:elevation="2dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:layout_marginStart="12dp"
                        android:padding="12dp"
                        android:src="@mipmap/ic_home_search" />

                    <EditText
                        android:id="@+id/et_search"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_marginHorizontal="8dp"
                        android:layout_weight="1"
                        android:background="@null"
                        android:hint="搜索感兴趣的内容"
                        android:layout_marginStart="15dp"
                        android:textColor="@color/black"
                        android:textColorHint="#999999"
                        android:textSize="14sp" />
<!--                    <ImageView-->
<!--                        android:id="@+id/tv_search"-->
<!--                        android:layout_width="40dp"-->
<!--                        android:layout_height="40dp"-->
<!--                        android:layout_marginStart="12dp"-->
<!--                        android:layout_marginEnd="15dp"-->
<!--                        android:padding="12dp"-->
<!--                        android:src="@mipmap/ic_home_search" />-->
                    <TextView
                        android:id="@+id/tv_search"
                        android:layout_width="wrap_content"
                        android:layout_height="28dp"
                        android:layout_marginEnd="6dp"
                        android:background="@drawable/shape_purple_gradient_round_bg_20"
                        android:gravity="center"
                        android:paddingHorizontal="12dp"
                        android:text="搜索"
                        android:textColor="@color/white"
                        android:textSize="14sp" />
                </LinearLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/top_guide_rv"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_13"
                    android:background="@color/all_transparent"
                    android:visibility="gone"
                    app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                    app:spanCount="3"
                    tools:itemCount="3"
                    tools:listitem="@layout/adapter_top_guide_layout" />
                <LinearLayout
                    android:orientation="horizontal"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                </LinearLayout>

                <!-- 2x2 网格布局 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="15dp"
                    android:layout_marginTop="20dp"
                    android:orientation="vertical">

                    <!-- 第一行 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:weightSum="2">

                        <ImageView
                            android:id="@+id/item_peidui"
                            android:layout_width="0dp"
                            android:layout_height="75dp"
                            android:layout_weight="1"
                            android:layout_marginEnd="8dp"
                            android:background="@mipmap/home_icon_1"
                            android:scaleType="fitXY" />

                        <ImageView
                            android:id="@+id/cl_skgw"
                            android:layout_width="0dp"
                            android:layout_height="75dp"
                            android:layout_weight="1"
                            android:layout_marginStart="8dp"
                            android:background="@mipmap/home_icon_2"
                            android:scaleType="fitXY" />

                    </LinearLayout>

                    <!-- 第二行 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:orientation="horizontal"
                        android:weightSum="2">

                        <ImageView
                            android:id="@+id/cl_jryf"
                            android:layout_width="0dp"
                            android:layout_height="75dp"
                            android:layout_weight="1"
                            android:layout_marginEnd="8dp"
                            android:background="@mipmap/home_icon_4"
                            android:scaleType="fitXY" />

                        <ImageView
                            android:id="@+id/cl_jfdh"
                            android:layout_width="0dp"
                            android:layout_height="75dp"
                            android:layout_weight="1"
                            android:layout_marginStart="8dp"
                            android:background="@mipmap/home_icon_3"
                            android:scaleType="fitXY" />

                    </LinearLayout>

                </LinearLayout>
                <!-- 隐藏的元素 - 保持原有ID以免影响Java代码 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:orientation="horizontal"
                    android:visibility="gone">

                    <ImageView
                        android:id="@+id/cl_rwzx"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:background="@mipmap/bg_rwzx" />

                    <ImageView
                        android:id="@+id/cl_hyzx"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:background="@mipmap/bg_vip" />

                    <ImageView
                        android:id="@+id/cl_fzpzn"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:background="@mipmap/bg_fangzhapian" />

                    <TextView android:id="@+id/tv_xz1" android:layout_width="0dp" android:layout_height="0dp" />
                    <TextView android:id="@+id/tv_xz2" android:layout_width="0dp" android:layout_height="0dp" />
                    <TextView android:id="@+id/tv_kf1" android:layout_width="0dp" android:layout_height="0dp" />
                    <TextView android:id="@+id/tv_kf2" android:layout_width="0dp" android:layout_height="0dp" />
                    <TextView android:id="@+id/tv_fp1" android:layout_width="0dp" android:layout_height="0dp" />
                    <TextView android:id="@+id/tv_fp2" android:layout_width="0dp" android:layout_height="0dp" />

                </LinearLayout>


                <com.youth.banner.Banner
                    android:id="@+id/banner"
                    android:layout_width="match_parent"
                    android:layout_height="100dp"
                    android:layout_marginHorizontal="15dp"
                    android:layout_marginVertical="5dp"
                    android:background="@color/all_transparent"
                    android:visibility="gone"
                    app:banner_indicator_normal_color="@android:color/white"
                    app:banner_indicator_selected_color="@color/color_FF6D7A"
                    app:banner_loop_time="4000"
                    app:banner_radius="@dimen/dp_5" />
                <com.chat.laty.utils.LyStatusBar
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/all_transparent" />
            </LinearLayout>
<!--        </com.google.android.material.appbar.CollapsingToolbarLayout>-->

        <FrameLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:layout_marginBottom="10dp"
            android:background="@drawable/shape_home_top_tab_bg"
            app:layout_collapseMode="pin">

            <com.google.android.material.tabs.TabLayout
                android:id="@+id/tab_layout"
                android:layout_width="wrap_content"
                android:layout_height="35dp"
                android:background="@color/all_transparent"
                app:tabBackground="@android:color/transparent"
                app:tabIndicator="@null"
                app:tabMode="auto"
                app:tabRippleColor="@android:color/transparent" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/search_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end|center_vertical"
                android:layout_marginEnd="@dimen/dp_10"
                android:drawableLeft="@mipmap/shaixuan"
                android:drawablePadding="@dimen/dp_6"
                android:text="搜索"
                android:textColor="#747474"
                android:textSize="16sp"
                android:visibility="gone" />
        </FrameLayout>
    </com.google.android.material.appbar.AppBarLayout>


    <!-- ViewPager 作为主要内容区域 -->
    <androidx.viewpager.widget.ViewPager
        android:id="@+id/viewpager"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/shape_bottomleft_bottomright_round_white_10"
        app:layout_behavior="@string/appbar_scrolling_view_behavior" />

    <!-- 浮动按钮 -->
    <com.chat.laty.view.AttachReButton
        android:visibility="gone"
        android:id="@+id/fate_peidui_rl"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="@dimen/dp_13"
        android:background="@drawable/badge_ff8c8c_to_ff5562_bg_40">
        <ImageView
            android:src="@mipmap/yuanfen"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>
    </com.chat.laty.view.AttachReButton>

</androidx.coordinatorlayout.widget.CoordinatorLayout>